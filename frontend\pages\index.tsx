import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import PPKUpload from '../components/PPKUpload';
import initPPKTestUtils from '../utils/ppkTestUtils';
import { apiClient } from '../utils/axiosClient';
import tokenManager from '../utils/tokenManager';

export default function Login() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [ppkChallenge, setPpkChallenge] = useState('');
  const [ppkSignature, setPpkSignature] = useState('');
  const [ppkStatus, setPpkStatus] = useState<'waiting' | 'generating' | 'ready' | 'error'>('waiting');
  const [requiresPPK, setRequiresPPK] = useState(false);  const router = useRouter();
  
  // Check for session errors from query params
  useEffect(() => {
    if (router.query.session === 'expired') {
      setError('Your session has expired. Please log in again.');
    } else if (router.query.session === 'invalid') {
      setError('Invalid session. Please log in again.');
    }
  }, [router.query]);    // Check if already logged in
  useEffect(() => {
    const checkSession = async () => {
      try {
        console.log('Checking if already logged in...');
        
        // Check if we have a token using token manager
        if (!tokenManager.isAuthenticated('admin')) {
          console.log('No token found, showing login page');
          return;
        }

        const response = await apiClient.frontend.get('/api/auth/admin/session', {
          headers: {
            'Authorization': `Bearer ${tokenManager.getToken('admin')}`
          }
        });

        console.log('Session check response:', response.status);
        console.log('Already logged in as:', response.data.admin?.username);
        router.push('/admin/dashboard');
      } catch (err: any) {
        console.log('Token invalid, clearing tokens');
        tokenManager.clearAllTokens();
      }
    };

    checkSession();
  }, [router]);

  // Initialize PPK test utilities in development mode
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      initPPKTestUtils();
      console.log('PPK testing utilities initialized for development');
    }
  }, []);
  
  // Fetch backend challenge when username changes
  useEffect(() => {
    if (!username) {
      setPpkChallenge('');
      setRequiresPPK(false);
      setPpkStatus('waiting');
      return;
    }
    const fetchChallenge = async () => {
      setPpkStatus('waiting');
      setPpkChallenge('');
      setRequiresPPK(false);      try {
        // Use the API route instead of directly calling the backend
        const response = await apiClient.frontend.post('/api/auth/admin/login/init', {
          username
        });
        
        const data = response.data;
        console.log('Challenge response:', data);
        
        if (data.requiresPPK && data.challenge) {
          setPpkChallenge(data.challenge);
          setRequiresPPK(true);
          console.log('PPK authentication required');
        } else if (!data.requiresPPK) {
          setPpkChallenge('');
          setRequiresPPK(false);
          console.log('PPK authentication not required');
        } else {
          throw new Error('Invalid response format from server');
        }
      } catch (err) {
        console.error('Failed to get challenge:', err);
        setError(err instanceof Error ? err.message : 'Failed to contact backend for challenge');
      }
    };
    fetchChallenge();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username]);
  // Handle PPK authentication
  const handlePPKReady = (challenge: string, signature: string, privateKey: CryptoKey) => {
    setPpkSignature(signature);
    setPpkStatus('ready');
  };

  const handlePPKError = (errorMsg: string) => {
    setError(`PPK Error: ${errorMsg}`);
    setPpkStatus('error');
  };  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (requiresPPK && ppkStatus !== 'ready') {
      setError('Please complete PEM authentication first');
      setLoading(false);
      return;
    }

    try {
      // Clear any existing cookies first (might help with issues)
      document.cookie = "admin-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie = "admin-token-access=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      
      console.log('Cookies before login:', document.cookie);
      
      // Use the admin login complete endpoint
      let endpoint = '/api/auth/admin/login/complete';
      let payload = {
        username,
        password,
        challenge: ppkChallenge,
        ppkSignature: ppkSignature,
        deviceFingerprint: 'web'
      };
        console.log('Login payload:', JSON.stringify(payload));
      
      const response = await apiClient.frontend.post(endpoint, payload);
      const data = response.data;
      
      console.log('Login successful', data);
        // Save token using token manager
      if (data.token) {
        tokenManager.setToken(data.token, 'admin');
        console.log('Token saved securely using token manager');
      }

      // Verify session using Authorization header
      try {
        const sessionResponse = await apiClient.frontend.get('/api/auth/admin/session', {
          headers: {
            'Authorization': `Bearer ${data.token}`
          }
        });
        
        console.log('Session check status:', sessionResponse.status);
        console.log('Session verified successfully with Authorization header');
        
        // Redirect to dashboard
        console.log('Attempting to redirect to dashboard...');
        
        // Try multiple redirect approaches
        try {
          await router.push('/admin/dashboard');
          console.log('Router push completed');        } catch (routerError) {
          window.location.href = '/admin/dashboard';
        }
      } catch (sessionError: any) {
        console.warn('Session verification failed:', sessionError);
        
        // Still redirect to dashboard and let it handle the error
        console.log('Redirecting to dashboard despite session verification failure');
        try {
          await router.push('/admin/dashboard');
        } catch (routerError) {
          window.location.href = '/admin/dashboard';
        }
      }
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.response?.data?.error || err.message || 'An error occurred during login');
      setPpkStatus('error');
    } finally {
      setLoading(false);
    }
  };

  const resetPpkStatus = () => {
    setPpkStatus('waiting');
    setPpkSignature('');
  };

  return (
    <div className="login-container">
      <Head>
        <title>CCALC Admin Portal - Secure Login</title>
        <meta name="description" content="Secure login for CCALC Admin Portal" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="login-card">
        <div className="login-header">
          <h1>CCALC Admin Portal</h1>
          <p className="subtitle">Secure Authentication Required</p>
        </div>

        <div className="security-badge">
          <div className="badge-icon">🛡️</div>
          <div className="badge-text">End-to-End Encrypted • Zero Trust</div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => {
                setUsername(e.target.value);
                resetPpkStatus();
              }}
              placeholder="Enter your admin username"
              required
              disabled={loading}
              autoComplete="username"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value);
                resetPpkStatus();
              }}
              placeholder="Enter your password"
              required
              disabled={loading}
              autoComplete="current-password"
            />
          </div>          {requiresPPK && (
            <div className="form-group ppk-section" style={{ 
              border: '2px solid #0070f3', 
              borderRadius: '8px', 
              padding: '15px', 
              backgroundColor: '#f0f7ff', 
              marginTop: '20px', 
              marginBottom: '20px' 
            }}>
              <label style={{ fontWeight: 'bold', color: '#0070f3' }}>PPK Authentication Required</label>
              <div className="ppk-status">
                {ppkStatus === 'ready' ? (
                  <div className="ppk-success">✓ PPK Verified</div>
                ) : (
                  <div className="ppk-instruction">
                    Please upload your private key file (.pem) to authenticate
                  </div>
                )}
              </div>              <PPKUpload
                challenge={ppkChallenge}
                onPPKReady={handlePPKReady}
                onPPKError={handlePPKError}
                disabled={loading || !username || ppkStatus === 'generating' || ppkStatus === 'ready'}
                className="ppk-upload-component"
              />
              {ppkStatus === 'ready' && (
                <button 
                  type="button" 
                  className="reset-ppk" 
                  onClick={resetPpkStatus}
                >
                  Reset PPK Authentication
                </button>
              )}
            </div>
          )}

          {error && <div className="error-message">{error}</div>}
          
          <button 
            type="submit" 
            disabled={loading || (requiresPPK && ppkStatus !== 'ready') || !username || !password}
            className={loading ? 'loading' : ''}
          >
            {loading ? 'Authenticating...' : 'Secure Login'}
          </button>
        </form>

        <div className="security-note">
          <p>This admin portal requires PPK (Personal Public Key) authentication in addition to username/password.</p>
          <p>All communications are end-to-end encrypted.</p>
        </div>
      </div>

      <style jsx>{`
        .login-container {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100vh;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
          padding: 20px;
        }
        .login-card {
          width: 100%;
          max-width: 450px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          padding: 40px;
          position: relative;
          overflow: hidden;
        }
        .login-header {
          text-align: center;
          margin-bottom: 30px;
        }
        .login-header h1 {
          margin: 0 0 8px;
          color: #333;
          font-size: 28px;
        }
        .subtitle {
          color: #666;
          margin: 0;
          font-size: 16px;
        }
        .security-badge {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 25px;
          padding: 10px;
          background-color: #f0f8ff;
          border-radius: 6px;
        }
        .badge-icon {
          font-size: 20px;
          margin-right: 10px;
        }
        .badge-text {
          font-size: 14px;
          color: #0070f3;
          font-weight: 500;
        }
        .form-group {
          margin-bottom: 20px;
        }
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #333;
        }
        input {
          width: 100%;
          padding: 12px 15px;
          border: 1px solid #ddd;
          border-radius: 6px;
          font-size: 16px;
          transition: border-color 0.3s;
        }
        input:focus {
          outline: none;
          border-color: #0070f3;
          box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.1);
        }
        input:disabled {
          background-color: #f5f5f5;
          cursor: not-allowed;
        }
        .ppk-status {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          font-size: 14px;
        }
        .ppk-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 10px;
          background-color: #999;
        }
        .ppk-indicator.generating {
          background-color: #faad14;
          animation: pulse 1.5s infinite;
        }
        .ppk-indicator.ready {
          background-color: #52c41a;
        }
        .ppk-indicator.error {
          background-color: #ff4d4f;
        }
        @keyframes pulse {
          0% { opacity: 0.6; }
          50% { opacity: 1; }
          100% { opacity: 0.6; }
        }
        button {
          width: 100%;
          padding: 14px;
          background-color: #0070f3;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
          position: relative;
        }
        button:hover:not(:disabled) {
          background-color: #005ae0;
        }
        button:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
        button.loading:after {
          content: "";
          position: absolute;
          width: 20px;
          height: 20px;
          top: 50%;
          right: 20px;
          margin-top: -10px;
          border: 3px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: white;
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          to { transform: rotate(360deg); }
        }        .error-message {
          color: #ff4d4f;
          margin-bottom: 20px;
          padding: 10px;
          background-color: #fff1f0;
          border: 1px solid #ffccc7;
          border-radius: 6px;
          font-size: 14px;
        }
        .ppk-section {
          border: 1px solid #e6f7ff;
          background-color: #f0f8ff;
          border-radius: 8px;
          padding: 15px;
        }
        .ppk-upload-component {
          margin-top: 10px;
        }
        .ppk-upload-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          background-color: #f0f8ff;
          color: #0070f3;
          border: 1px dashed #0070f3;
          border-radius: 6px;
          padding: 12px 20px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s;
          width: 100%;
        }
        .ppk-upload-button:hover {
          background-color: #e6f7ff;
        }
        .ppk-spinner {
          border: 3px solid rgba(0, 112, 243, 0.1);
          border-top: 3px solid #0070f3;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          animation: spin 1s linear infinite;
          margin-right: 10px;
          display: inline-block;
        }
        .ppk-loading, .ppk-success, .ppk-error {
          display: flex;
          align-items: center;
          padding: 10px;
          border-radius: 6px;
          font-size: 14px;
        }
        .ppk-success {
          color: #52c41a;
          background-color: #f6ffed;
          border: 1px solid #b7eb8f;
        }
        .ppk-error {
          color: #ff4d4f;
          background-color: #fff1f0;
          border: 1px solid #ffccc7;
        }
        .ppk-loading {
          color: #0070f3;
          background-color: #e6f7ff;
          border: 1px solid #91d5ff;
        }
        .ppk-retry-button {
          margin-left: 10px;
          padding: 4px 8px;
          background-color: transparent;
          color: #ff4d4f;
          border: 1px solid #ff4d4f;
          border-radius: 4px;
          font-size: 12px;
        }
        .security-note {
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #eee;
          font-size: 13px;
          color: #666;
          text-align: center;
        }
        .security-note p {
          margin: 5px 0;
        }
      `}</style>
    </div>
  );
}
