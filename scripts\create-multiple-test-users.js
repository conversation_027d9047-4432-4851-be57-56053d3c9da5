const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// Load environment variables
require('dotenv').config();

// MongoDB connection string
const mongoUri = process.env.MONGODB_URI || '*************************************************************';

// User schema (simplified version for this script)
const UserSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true, trim: true },
  email: { type: String, trim: true },
  expressionHash: { type: String, required: true },
  unlockExpression: { type: String },
  expressionType: { 
    type: String, 
    enum: ['calculator', 'pattern'], 
    default: 'calculator' 
  },
  expressionUpdatedAt: { type: Date },
  profile: {
    displayName: { type: String, required: true },
  },
  deviceFingerprintHash: { type: String },
  deviceMetadata: {
    model: { type: String },
    os: { type: String },
    registeredAt: { type: Date },
    registrationCoords: {
      lat: { type: Number },
      lng: { type: Number },
    },
  },
  bleUUIDHash: { type: String },
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked', 'pending_device_registration'],
    default: 'pending_device_registration',
  },
  lastLoginAt: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  builds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Build' }],
  messageExpiry: { type: Date },
  isSuperuser: { type: Boolean, default: false },
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

// Array of test users to create
const testUsers = [
  {
    username: 'testuser1',
    expression: '2+3*4',
    displayName: 'Test User 1'
  },
  {
    username: 'testuser2', 
    expression: '5*6+7',
    displayName: 'Test User 2'
  },
  {
    username: 'testuser3',
    expression: '10-3*2',
    displayName: 'Test User 3'
  },
  {
    username: 'mobiletest',
    expression: '8+4/2', 
    displayName: 'Mobile Test User'
  }
];

async function createMultipleTestUsers() {
  try {
    console.log('Connecting to MongoDB...');
    console.log('URI:', mongoUri.replace(/\/\/.*@/, '//***:***@'));
    
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    console.log(`\n🚀 Creating ${testUsers.length} test users...\n`);

    for (const userData of testUsers) {
      try {
        // Check if user already exists
        const existingUser = await User.findOne({ username: userData.username });
        if (existingUser) {
          console.log(`⚠️  User '${userData.username}' already exists. Skipping...`);
          continue;
        }

        // Hash the expression using bcrypt (to match authentication controller)
        const expressionHash = await bcrypt.hash(userData.expression, 10);

        // Create the user
        const newUser = new User({
          username: userData.username,
          expressionHash: expressionHash,
          unlockExpression: userData.expression, // Store plain text for admin viewing
          expressionType: 'calculator',
          expressionUpdatedAt: new Date(),
          profile: {
            displayName: userData.displayName
          },
          status: 'pending_device_registration', // Default status for new users
          failedLoginAttempts: 0,
          isSuperuser: false
        });

        const savedUser = await newUser.save();
        console.log(`✅ Created user: ${userData.username}`);
        console.log(`   Display Name: ${userData.displayName}`);
        console.log(`   Expression: ${userData.expression}`);
        console.log(`   Status: ${savedUser.status}`);
        console.log(`   ID: ${savedUser._id}\n`);

      } catch (error) {
        console.error(`❌ Error creating user '${userData.username}':`, error.message);
      }
    }

    console.log('\n📱 Mobile App Test Credentials:');
    console.log('================================');
    testUsers.forEach(user => {
      console.log(`Username: ${user.username}`);
      console.log(`Expression: ${user.expression}`);
      console.log('---');
    });

    console.log('\n💡 Instructions:');
    console.log('1. All users are created with status "pending_device_registration"');
    console.log('2. Choose any username/expression pair for mobile app testing');
    console.log('3. On first login from mobile app, device fingerprint and BLE UUID will be captured');
    console.log('4. After first successful login, user status becomes "active"');
    console.log('5. Subsequent logins will verify against the registered device fingerprint and BLE UUID');
    console.log('6. To test with a different device, use a different username');

  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

createMultipleTestUsers();
