import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';
import axios from 'axios';
import apiClient, { apiEndpoints } from '../../../utils/apiClient';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get admin token from cookies (this is an admin-only operation)
    const cookies = parse(req.headers.cookie || '');
    const token = cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { id } = req.query;
    const { deviceFingerprint, bleUUID } = req.body;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid user ID' });
    }

    if (!deviceFingerprint || !bleUUID) {
      return res.status(400).json({ error: 'Missing required fields' });
    }    // Forward request to backend
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.users.reset(id)}`,
        {
          deviceFingerprint,
          bleUUID
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      return res.status(200).json(response.data);
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        return res.status(error.response.status).json(error.response.data);
      }
      throw error;
    }
  } catch (error) {
    console.error('User device reset error:', error);
    return res.status(500).json({ error: 'Failed to reset user device' });
  }
}
