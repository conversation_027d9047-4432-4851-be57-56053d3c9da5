/**
 * Test User Data for Mobile App Login
 * 
 * Use these credentials to test mobile app authentication:
 */

const TEST_USER_CREDENTIALS = {
  username: 'testuser',
  displayName: 'Test User',
  expression: '2+3*4', // Result: 14
  isSuperuser: false
};

console.log('📱 Mobile App Test Credentials:');
console.log('================================');
console.log(`Username: ${TEST_USER_CREDENTIALS.username}`);
console.log(`Expression: ${TEST_USER_CREDENTIALS.expression} (evaluates to 14)`);
console.log('================================');
console.log('');
console.log('💡 To create this user:');
console.log('1. Start the frontend and backend servers');
console.log('2. Login to admin panel at http://localhost:3005');
console.log('3. Go to Users section');
console.log('4. Click "Add New User"');
console.log('5. Enter the above credentials');
console.log('6. Save the user');
console.log('');
console.log('📝 For mobile app testing:');
console.log('- User will be created with status "pending_device_registration"');
console.log('- On first login from mobile app, device fingerprint and BLE UUID will be captured automatically');
console.log('- Device fingerprint should be generated by mobile app');
console.log('- BLE UUID should be provided by mobile app BLE scanning');
console.log('- After first successful login, user status becomes "active"');
console.log('- Subsequent logins will verify against the registered device fingerprint and BLE UUID');
console.log('');
console.log('🔐 Security Flow:');
console.log('1. Admin creates user with username, expression, and display name only');
console.log('2. User appears with status "pending_device_registration" in admin panel');
console.log('3. User attempts first login from mobile app with username and expression');
console.log('4. Mobile app provides device fingerprint and BLE UUID during login');
console.log('5. Backend registers the device and changes status to "active"');
console.log('6. Future logins must use the same device fingerprint and BLE UUID');

module.exports = { TEST_USER_CREDENTIALS };
