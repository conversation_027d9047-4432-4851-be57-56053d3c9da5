/**
 * Voice Call Model
 * Stores all voice call metadata and recordings for admin panel access
 */

import mongoose, { Document, Schema } from 'mongoose';

export interface IVoiceCall extends Document {
  callId: string;
  callerId: string;
  recipientId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  status: 'connecting' | 'connected' | 'ended' | 'failed' | 'terminated';
  morphingProfile: string;
  recordingPath?: string;
  recordingSize?: number; // in bytes
  transcription?: string;
  callerIP: string;
  deviceFingerprint: string;
  bleDeviceId?: string;
  callQuality: 'excellent' | 'good' | 'fair' | 'poor';
  metadata: {
    callerUserAgent: string;
    networkType: string;
    encryptionLevel: string;
    compressionRatio: number;
    audioCodec: string;
  };
  adminNotes?: string;
  flaggedForReview: boolean;
  reviewedBy?: string;
  reviewedAt?: Date;
  getCallStats(): any;
}

export interface IVoiceCallModel extends mongoose.Model<IVoiceCall> {
  getCallsForAdmin(filter?: any, page?: number, limit?: number): Promise<any[]>;
  getCallAnalytics(options?: { startDate?: Date; endDate?: Date }): Promise<any>;
}

const VoiceCallSchema = new Schema<IVoiceCall>(
  {
    callId: { 
      type: String, 
      required: true, 
      unique: true,
      index: true 
    },
    callerId: { 
      type: String, 
      required: true,
      index: true 
    },
    recipientId: { 
      type: String, 
      required: true,
      index: true 
    },
    startTime: { 
      type: Date, 
      required: true,
      index: true 
    },
    endTime: { 
      type: Date 
    },
    duration: { 
      type: Number, 
      default: 0 
    },
    status: {
      type: String,
      enum: ['connecting', 'connected', 'ended', 'failed', 'terminated'],
      default: 'connecting',
      index: true
    },
    morphingProfile: {
      type: String,
      required: true,
      enum: ['superuser', 'agent', 'secure', 'custom'],
      default: 'agent'
    },
    recordingPath: {
      type: String,
      index: true
    },
    recordingSize: {
      type: Number
    },
    transcription: {
      type: String
    },
    callerIP: {
      type: String,
      required: true
    },
    deviceFingerprint: {
      type: String,
      required: true
    },
    bleDeviceId: {
      type: String
    },
    callQuality: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor'],
      default: 'good'
    },
    metadata: {
      callerUserAgent: { type: String, required: true },
      networkType: { type: String, default: 'unknown' },
      encryptionLevel: { type: String, default: 'AES-256' },
      compressionRatio: { type: Number, default: 0.7 },
      audioCodec: { type: String, default: 'AAC' }
    },
    adminNotes: {
      type: String
    },
    flaggedForReview: {
      type: Boolean,
      default: false,
      index: true
    },
    reviewedBy: {
      type: String
    },
    reviewedAt: {
      type: Date
    }
  },
  { 
    timestamps: true,
    collection: 'voice_calls'
  }
);

// Indexes for admin panel queries
VoiceCallSchema.index({ callerId: 1, startTime: -1 });
VoiceCallSchema.index({ recipientId: 1, startTime: -1 });
VoiceCallSchema.index({ status: 1, flaggedForReview: 1 });
VoiceCallSchema.index({ startTime: -1 });
VoiceCallSchema.index({ duration: -1 });

// Virtual for call duration in readable format
VoiceCallSchema.virtual('durationFormatted').get(function() {
  const minutes = Math.floor(this.duration / 60);
  const seconds = this.duration % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

// Add static methods to the schema
VoiceCallSchema.statics.getCallsForAdmin = function(filter: any = {}, page: number = 1, limit: number = 20) {
  const skip = (page - 1) * limit;
  return this.find(filter)
    .populate('callerId', 'username email')
    .populate('recipientId', 'username email')
    .sort({ startTime: -1 })
    .skip(skip)
    .limit(limit)
    .lean();
};

VoiceCallSchema.statics.getCallAnalytics = function(options: { startDate?: Date; endDate?: Date } = {}) {
  const matchStage: any = {};
  
  if (options.startDate || options.endDate) {
    matchStage.startTime = {};
    if (options.startDate) matchStage.startTime.$gte = options.startDate;
    if (options.endDate) matchStage.startTime.$lte = options.endDate;
  }

  const pipeline = [
    ...(Object.keys(matchStage).length > 0 ? [{ $match: matchStage }] : []),
    {
      $group: {
        _id: null,
        totalCalls: { $sum: 1 },
        completedCalls: {
          $sum: { $cond: [{ $eq: ['$status', 'ended'] }, 1, 0] }
        },
        failedCalls: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        averageDuration: { $avg: '$duration' },
        totalDuration: { $sum: '$duration' },
        recordingCount: {
          $sum: { $cond: [{ $ne: ['$recordingPath', null] }, 1, 0] }
        },
        flaggedCount: {
          $sum: { $cond: ['$flaggedForReview', 1, 0] }
        }
      }
    }
  ];

  return this.aggregate(pipeline);
};

// Add instance methods
VoiceCallSchema.methods.getCallStats = function() {
  return {
    callId: this.callId,
    duration: this.duration,
    status: this.status,
    quality: this.callQuality,
    hasRecording: !!this.recordingPath,
    morphingProfile: this.morphingProfile,
    flagged: this.flaggedForReview
  };
};

export default mongoose.model<IVoiceCall, IVoiceCallModel>('VoiceCall', VoiceCallSchema);
