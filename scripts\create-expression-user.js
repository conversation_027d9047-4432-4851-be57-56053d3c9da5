const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema (simplified version for this script)
const UserSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true, trim: true },
  email: { type: String, trim: true },
  expressionHash: { type: String, required: true },
  unlockExpression: { type: String },
  expressionType: { 
    type: String, 
    enum: ['calculator', 'pattern'], 
    default: 'calculator' 
  },
  expressionUpdatedAt: { type: Date },
  profile: {
    displayName: { type: String, required: true },
  },
  deviceFingerprintHash: { type: String },
  deviceMetadata: {
    model: { type: String },
    os: { type: String },
    registeredAt: { type: Date },
    registrationCoords: {
      lat: { type: Number },
      lng: { type: Number },
    },
  },
  bleUUIDHash: { type: String },
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked', 'pending_device_registration'],
    default: 'pending_device_registration',
  },
  lastLoginAt: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  builds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Build' }],
  messageExpiry: { type: Date },
  isSuperuser: { type: Boolean, default: false },
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function createComplexExpressionUser() {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB successfully');

    // COMPLEX EXPRESSION - User must enter EXACTLY this
    const exactExpression = '12 + 5*7 - 3*2 + 8/4';
    const username = 'mobileuser';
    const displayName = 'Mobile User';

    console.log('\n🧮 Expression Details:');
    console.log(`Exact Expression: "${exactExpression}"`);
    console.log('Calculation Steps: 12 + 35 - 6 + 2 = 43');
    console.log('⚠️  Authentication requires EXACT expression, NOT the result!');

    // Delete existing user if exists
    const existingUser = await User.findOne({ username: username });
    if (existingUser) {
      console.log(`\n🗑️  Deleting existing user '${username}'...`);
      await User.deleteOne({ username: username });
    }

    // Hash the EXACT expression (not the result)
    console.log('\n🔐 Hashing exact expression for authentication...');
    const expressionHash = await bcrypt.hash(exactExpression, 10);

    // Create the user
    const newUser = new User({
      username: username,
      email: `${username}@mobile.app`,
      expressionHash: expressionHash, // Hash of exact expression
      unlockExpression: exactExpression, // Store exact expression for admin viewing
      expressionType: 'calculator',
      expressionUpdatedAt: new Date(),
      profile: {
        displayName: displayName
      },
      status: 'pending_device_registration', // Will be set to active on first mobile login
      failedLoginAttempts: 0,
      isSuperuser: false
    });

    const savedUser = await newUser.save();
    
    console.log('\n✅ Complex expression user created successfully!');
    console.log('📋 User Details:');
    console.log(`   Username: ${savedUser.username}`);
    console.log(`   Display Name: ${savedUser.profile.displayName}`);
    console.log(`   Expression: "${savedUser.unlockExpression}"`);
    console.log(`   Status: ${savedUser.status}`);
    console.log(`   Created: ${savedUser.createdAt}`);

    // Verify the expression hash
    console.log('\n🔍 Verifying expression hash...');
    const isValid = await bcrypt.compare(exactExpression, savedUser.expressionHash);
    console.log(`   Hash verification: ${isValid ? '✅ Valid' : '❌ Invalid'}`);

    // Test wrong expressions
    console.log('\n🧪 Testing wrong expressions (should fail):');
    const wrongExpressions = [
      '43', // The result
      '12 + 35 - 6 + 2', // Calculated steps
      '12+5*7-3*2+8/4', // No spaces
      '12 + 5 * 7 - 3 * 2 + 8 / 4', // Different spacing
    ];

    for (const wrongExpr of wrongExpressions) {
      const shouldFail = await bcrypt.compare(wrongExpr, savedUser.expressionHash);
      console.log(`   "${wrongExpr}" → ${shouldFail ? '❌ Incorrectly passed' : '✅ Correctly failed'}`);
    }

    // Count total users
    console.log('\n📊 Database Status:');
    const totalUsers = await User.countDocuments();
    const pendingUsers = await User.countDocuments({ status: 'pending_device_registration' });
    const activeUsers = await User.countDocuments({ status: 'active' });
    
    console.log(`   Total Users: ${totalUsers}`);
    console.log(`   Pending Device Registration: ${pendingUsers}`);
    console.log(`   Active Users: ${activeUsers}`);

    // List all users
    const allUsers = await User.find({}, { 
      username: 1, 
      'profile.displayName': 1, 
      unlockExpression: 1, 
      status: 1, 
      createdAt: 1 
    }).sort({ createdAt: -1 });

    console.log('\n👥 All Users:');
    allUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.username} (${user.profile.displayName})`);
      console.log(`      Expression: "${user.unlockExpression}"`);
      console.log(`      Status: ${user.status}`);
      console.log(`      Created: ${user.createdAt.toISOString()}`);
      console.log('');
    });

    console.log('\n📱 Mobile App Instructions:');
    console.log('1. User must enter EXACTLY: "12 + 5*7 - 3*2 + 8/4"');
    console.log('2. Including all spaces and operators as shown');
    console.log('3. Authentication by exact expression, NOT by result (43)');
    console.log('4. On first successful login, device will be registered');
    console.log('5. Subsequent logins require same expression + device match');

  } catch (error) {
    console.error('❌ Error creating complex expression user:', error);
    if (error.code === 11000) {
      console.log('💡 Tip: User might already exist. The script should have deleted it first.');
    }
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

createComplexExpressionUser();
