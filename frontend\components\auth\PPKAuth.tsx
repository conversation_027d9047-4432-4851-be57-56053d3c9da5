import React, { useState, useEffect } from 'react';
import { Box, But<PERSON>, Typo<PERSON>, TextField, Alert, Paper, CircularProgress, Stepper, Step, StepLabel } from '@mui/material';
import PPKFileUploader from './PPKFileUploader';
import { generateChallenge } from '../../utils/ppkAuth';
import axios from 'axios';

interface PPKAuthProps {
  username: string;
  onSuccess: (token: string) => void;
  onCancel: () => void;
}

const PPKAuth: React.FC<PPKAuthProps> = ({ username, onSuccess, onCancel }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [challenge, setChallenge] = useState<string>('');
  const [privateKey, setPrivateKey] = useState<CryptoKey | null>(null);
  const [signature, setSignature] = useState<string | null>(null);

  useEffect(() => {
    // When component mounts, request a challenge from the server
    fetchChallenge();
  }, []);

  const fetchChallenge = async () => {
    setLoading(true);
    setError(null);
    try {
      // For frontend development without backend, generate a challenge locally
      // In production, this should come from the server
      const response = await axios.post('/api/auth/challenge', { username });
      setChallenge(response.data.challenge);
    } catch (error) {
      console.error('Error fetching challenge:', error);
      // Fallback to local challenge generation for development
      setChallenge(generateChallenge());
      setError('Using locally generated challenge (development mode)');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyLoaded = (key: CryptoKey) => {
    setPrivateKey(key);
    setActiveStep(1); // Move to signature step
  };

  const handleSignature = (sig: string) => {
    setSignature(sig);
    setActiveStep(2); // Move to verification step
  };

  const verifySignature = async () => {
    if (!signature || !challenge || !username) return;

    setLoading(true);
    setError(null);
    try {
      const response = await axios.post('/api/auth/ppk-login', {
        username,
        challenge,
        signature
      });

      if (response.data.token) {
        onSuccess(response.data.token);
      } else {
        setError('Authentication failed: No token received');
      }
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        setError(`Authentication failed: ${error.response.data.error}`);
      } else {
        setError('Authentication failed: Unknown error');
      }
    } finally {
      setLoading(false);
    }
  };

  const steps = ['Load Key', 'Sign Challenge', 'Verify'];

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <PPKFileUploader
            onKeyLoaded={handleKeyLoaded}
            onError={setError}
          />
        );
      case 1:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Sign Challenge
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Challenge: {challenge}
            </Alert>
            <PPKFileUploader
              onKeyLoaded={setPrivateKey}
              onError={setError}
              challenge={challenge}
              onSignature={handleSignature}
            />
          </Box>
        );
      case 2:
        return (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Verify Signature
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              Signature created successfully. Click verify to complete authentication.
            </Alert>
            <Button
              variant="contained"
              onClick={verifySignature}
              disabled={loading}
              sx={{ mr: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Verify'}
            </Button>
            <Button variant="outlined" onClick={onCancel} disabled={loading}>
              Cancel
            </Button>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Paper elevation={3} sx={{ maxWidth: 600, mx: 'auto', mt: 4 }}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          PPK Authentication for {username}
        </Typography>
        
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {renderStepContent()}
      </Box>
    </Paper>
  );
};

export default PPKAuth;
