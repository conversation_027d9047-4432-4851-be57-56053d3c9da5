const mongoose = require('mongoose');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema (simplified)
const UserSchema = new mongoose.Schema({
  username: String,
  deviceFingerprintHash: String,
  bleUUIDHash: String,
  status: String,
  deviceMetadata: Object,
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function resetUserDevice() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    const username = 'testuser2';
    
    const result = await User.updateOne(
      { username: username },
      { 
        $unset: { 
          deviceFingerprintHash: "",
          bleUUIDHash: "",
          deviceMetadata: ""
        },
        $set: { 
          status: 'pending_device_registration'
        }
      }
    );

    if (result.matchedCount === 0) {
      console.log(`❌ User '${username}' not found`);
    } else {
      console.log(`✅ Reset device registration for user '${username}'`);
      console.log('User is now ready for fresh device registration from mobile app');
      
      console.log('\n📱 Mobile App Login Credentials:');
      console.log(`Username: ${username}`);
      console.log(`Expression: 5+3`);
      console.log('Status: pending_device_registration (ready for mobile app)');
    }

  } catch (error) {
    console.error('Error resetting user device:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

resetUserDevice();
