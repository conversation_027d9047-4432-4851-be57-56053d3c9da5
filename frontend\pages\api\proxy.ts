/**
 * Simplified Unified API Proxy
 * Reduces complexity by providing a single proxy endpoint for all backend communication
 */
import type { NextApiRequest, NextApiResponse } from 'next';
import ServerApiClient from '../../utils/serverApiClient';

// Rate limiting configuration
const RATE_LIMITS = {
  auth: { requests: 10, window: 60000 }, // 10 requests per minute for auth
  data: { requests: 100, window: 60000 }, // 100 requests per minute for data
  admin: { requests: 50, window: 60000 }, // 50 requests per minute for admin
};

// Simple in-memory rate limiter (for production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(identifier: string, category: 'auth' | 'data' | 'admin'): boolean {
  const now = Date.now();
  const limit = RATE_LIMITS[category];
  const key = `${identifier}:${category}`;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + limit.window });
    return true;
  }
  
  if (current.count >= limit.requests) {
    return false;
  }
  
  current.count++;
  return true;
}

function getClientIP(req: NextApiRequest): string {
  const forwarded = req.headers['x-forwarded-for'];
  const ip = forwarded ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0]) : req.socket.remoteAddress;
  return ip || 'unknown';
}

function categorizeEndpoint(path: string): 'auth' | 'data' | 'admin' {
  if (path.includes('/auth/')) return 'auth';
  if (path.includes('/admin/')) return 'admin';
  return 'data';
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Extract the backend path from the query
    const { path } = req.query;
    
    if (!path || typeof path !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request',
        message: 'Backend path is required',
        details: { requiredFormat: '/api/proxy?path=/api/endpoint' }
      });
    }

    // Validate path format
    if (!path.startsWith('/api/')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid path',
        message: 'Path must start with /api/',
        details: { provided: path, expected: '/api/...' }
      });
    }

    // Rate limiting
    const clientIP = getClientIP(req);
    const category = categorizeEndpoint(path);
    
    if (!checkRateLimit(clientIP, category)) {
      return res.status(429).json({
        success: false,
        error: 'Rate limit exceeded',
        message: `Too many ${category} requests. Please try again later.`,
        details: {
          category,
          limit: RATE_LIMITS[category],
          retryAfter: 60
        }
      });
    }

    // Initialize server client
    const serverClient = new ServerApiClient();
    
    // Prepare headers
    const headers: Record<string, string> = {};
    
    // Forward authentication cookies
    if (req.headers.cookie) {
      headers.Cookie = req.headers.cookie;
    }
    
    // Forward CSRF token
    if (req.headers['x-csrf-token']) {
      headers['X-CSRF-Token'] = req.headers['x-csrf-token'] as string;
    }
    
    // Forward content type
    if (req.headers['content-type']) {
      headers['Content-Type'] = req.headers['content-type'] as string;
    }

    let response: Response;

    // Route based on HTTP method
    switch (req.method) {
      case 'GET':
        response = await serverClient.get(path, { headers });
        break;
        
      case 'POST':
        response = await serverClient.post(path, req.body, { headers });
        break;
        
      case 'PUT':
        response = await serverClient.put(path, req.body, { headers });
        break;
        
      case 'DELETE':
        response = await serverClient.delete(path, { headers });
        break;
        
      case 'PATCH':
        response = await serverClient.patch(path, req.body, { headers });
        break;
        
      default:
        return res.status(405).json({
          success: false,
          error: 'Method not allowed',
          message: `HTTP method ${req.method} is not supported`,
          details: { supported: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] }
        });
    }

    // Forward Set-Cookie headers from backend
    const setCookieHeaders = response.headers.get('set-cookie');
    if (setCookieHeaders) {
      res.setHeader('Set-Cookie', setCookieHeaders);
    }

    // Forward other relevant headers
    const contentType = response.headers.get('content-type');
    if (contentType) {
      res.setHeader('Content-Type', contentType);
    }

    // Parse response
    let responseData;
    try {
      responseData = await response.json();
    } catch (jsonError) {
      // Handle non-JSON responses
      responseData = await response.text();
    }

    // Forward status and data
    return res.status(response.status).json(responseData);

  } catch (error: any) {
    console.error('Proxy error:', {
      error: error.message,
      stack: error.stack,
      path: req.query.path,
      method: req.method,
      timestamp: new Date().toISOString()
    });

    return res.status(500).json({
      success: false,
      error: 'Proxy error',
      message: 'Failed to communicate with backend service',
      details: {
        type: 'backend_communication_error',
        timestamp: new Date().toISOString(),
        retryable: true
      }
    });
  }
}
