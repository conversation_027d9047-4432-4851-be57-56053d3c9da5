# CCALC Admin Portal

This is the administrative frontend for the CCALC (Covert, Authenticated, End-to-End Encrypted Chat/Call) application. The admin portal provides a secure interface for managing users, builds, and system controls.

## Features

- **Secure Authentication**: Multi-factor authentication with <PERSON>K (Personal Public Key)
- **User Management**: Create, update, and manage user accounts
- **Build Management**: Manage application builds and releases
- **System Controls**: Monitor and control system settings
- **End-to-End Encryption**: All communications are encrypted for maximum security
- **Zero Trust Architecture**: Security at every level

## Technology Stack

- **Frontend**: Next.js with TypeScript
- **Backend Communication**: Secure API routes with proper proxy implementation
- **Authentication**: JWT with HTTP-only cookies
- **Styling**: CSS-in-JS with styled-jsx
- **Security**: Rate limiting, CSRF protection, input validation

## Project Structure

- `pages/`: Next.js pages and API routes
  - `api/`: Backend API proxy routes
  - `index.tsx`: Login page
  - `dashboard.tsx`: Main dashboard
  - `users/`: User management pages
  - `builds/`: Build management pages
  - `controls/`: System control pages
- `components/`: Reusable UI components
- `styles/`: Global styles
- `utils/`: Utility functions
- `public/`: Static assets

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or yarn
- Backend API server running

### Environment Setup

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_BACKEND_URL=http://localhost:3000
```

### Installation

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## Security Considerations

- All API routes include rate limiting to prevent brute force attacks
- Authentication uses HTTP-only cookies to prevent XSS attacks
- Sensitive operations require confirmation
- Input validation on all forms
- End-to-end encryption for all communications

## Development Guidelines

- Always validate user input both on client and server side
- Use proper error handling
- Follow the existing component structure
- Test all changes thoroughly before deployment
- Use TypeScript types for all components and functions

## Deployment

The application can be deployed using Docker or directly on a server:

```bash
# Build the application
npm run build

# Start the server on port 3001
npm start
```

## Docker Deployment

```bash
# Build Docker image
docker build -t ccalc-admin-frontend .

# Run Docker container
docker run -p 3001:3001 -e NEXT_PUBLIC_BACKEND_URL=http://api-server:3000 ccalc-admin-frontend
```

## License

Proprietary and confidential.
