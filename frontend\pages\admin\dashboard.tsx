import React from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import Dashboard from '../../components/admin/Dashboard';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';

const AdminDashboardPage: NextPage = () => {
  return (
    <>
      <Head>
        <title>Admin Dashboard | CCALC</title>
        <meta name="description" content="CCALC Admin Dashboard" />
      </Head>      <AdminLayout>
        <Dashboard />
      </AdminLayout>
    </>
  );
};

export default withAdminAuth(AdminDashboardPage);