import { Request, Response, Router } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';
import { auth, requireUser } from '../../middleware/auth';
import Media from '../../models/Media';
import { Message } from '../../models/Chat';
import { encryptFile, decryptFile } from '../../utils/encryption';
import { generateThumbnail } from '../../utils/media-processing';
import { scanFileForMalware } from '../../utils/security-scanner';
import { validateFileType, validateFileSize } from '../../utils/file-validation';
import securityLogger from '../../utils/security-logger';
import Joi from 'joi';
import validate from '../../middleware/validate';

const router = Router();

// Configuration
const UPLOAD_DIR = process.env.MEDIA_UPLOAD_DIR || 'uploads/media';
const THUMBNAIL_DIR = process.env.THUMBNAIL_DIR || 'uploads/thumbnails';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '50') * 1024 * 1024; // 50MB default
const ALLOWED_MIME_TYPES = [
  'image/jpeg', 'image/png', 'image/gif', 'image/webp',
  'video/mp4', 'video/quicktime', 'video/webm',
  'audio/mpeg', 'audio/wav', 'audio/aac',
  'application/pdf', 'text/plain',
  'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

// Ensure upload directories exist
async function ensureDirectories() {
  try {
    await fs.mkdir(UPLOAD_DIR, { recursive: true });
    await fs.mkdir(THUMBNAIL_DIR, { recursive: true });
  } catch (error) {
    console.error('Failed to create upload directories:', error);
  }
}

// Multer configuration for file uploads
const storage = multer.memoryStorage(); // Store in memory for encryption
const upload = multer({
  storage,
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 5, // Max 5 files per request
  },
  fileFilter: (req, file, cb) => {
    if (ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not allowed`));
    }
  },
});

// Validation schemas
const uploadMediaSchema = Joi.object({
  messageId: Joi.string().optional(),
  visibility: Joi.string().valid('private', 'chat_members', 'superuser_only').default('private'),
  expiresIn: Joi.number().integer().min(1).max(365).optional(), // Days
  quality: Joi.string().valid('low', 'medium', 'high', 'original').default('original'),
});

const getMediaSchema = Joi.object({
  decrypt: Joi.boolean().default(true),
  thumbnail: Joi.boolean().default(false),
});

/**
 * Upload encrypted media files with security scanning
 * POST /api/media/upload
 */
export async function uploadMedia(req: Request, res: Response): Promise<void> {
  try {
    const { messageId, visibility, expiresIn, quality } = req.body;
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      res.status(400).json({ error: 'No files provided' });
      return;
    }

    const userId = req.user!._id;
    const deviceFingerprint = req.headers['x-device-fingerprint'] as string || 'unknown';
    const bleUUID = req.headers['x-ble-uuid'] as string || 'unknown';
    const ipAddress = req.ip || req.socket.remoteAddress || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';

    const uploadedMedia: any[] = [];
    const errors: string[] = [];

    for (const file of files) {
      try {        // Validate file
        const fileValidation = validateFileType(file.mimetype, file.originalname);
        if (!fileValidation.isValid) {
          errors.push(`${file.originalname}: ${fileValidation.error}`);
          continue;
        }const sizeValidation = validateFileSize(file.size, file.mimetype);
        if (!sizeValidation.isValid) {
          errors.push(`${file.originalname}: ${sizeValidation.error}`);
          continue;
        }

        // Generate unique identifiers
        const mediaId = crypto.randomUUID();
        const encryptionKey = crypto.randomBytes(32);
        const iv = crypto.randomBytes(16);
        const salt = crypto.randomBytes(32);        // Encrypt file
        const encryptedBuffer = await encryptFile(file.buffer, encryptionKey.toString('hex'), iv.toString('hex'));
        const encryptedName = `${mediaId}_${crypto.randomBytes(16).toString('hex')}`;
        const encryptedPath = path.join(UPLOAD_DIR, encryptedName);

        // Save encrypted file
        await fs.writeFile(encryptedPath, encryptedBuffer.encrypted);

        // Generate thumbnail for images/videos
        let thumbnailPath: string | undefined;
        if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
          try {
            const thumbnailBuffer = await generateThumbnail(file.buffer.toString('base64'), file.mimetype);
            if (thumbnailBuffer) {
              const encryptedThumbnail = await encryptFile(thumbnailBuffer, encryptionKey.toString('hex'), iv.toString('hex'));
              const thumbnailName = `thumb_${encryptedName}`;
              thumbnailPath = path.join(THUMBNAIL_DIR, thumbnailName);
              await fs.writeFile(thumbnailPath, encryptedThumbnail.encrypted);
            }
          } catch (thumbError) {
            console.warn('Failed to generate thumbnail:', thumbError);
          }
        }

        // Determine file category
        let category: string;
        if (file.mimetype.startsWith('image/')) category = 'image';
        else if (file.mimetype.startsWith('video/')) category = 'video';
        else if (file.mimetype.startsWith('audio/')) category = 'audio';
        else if (file.mimetype.includes('pdf') || file.mimetype.includes('document')) category = 'document';
        else category = 'other';

        // Create media record
        const media = new Media({
          mediaId,
          uploaderId: userId,
          messageId: messageId || undefined,
          
          file: {
            originalName: file.originalname,
            encryptedName,
            mimeType: file.mimetype,
            size: file.size,
            encryptedSize: encryptedBuffer.encrypted.length,
            extension: path.extname(file.originalname),
            category,
          },
            storage: {
            encryptedPath,
            thumbnailPath,
            encryptionKey: encryptionKey.toString('base64'),
            iv: encryptedBuffer.iv,
            tag: encryptedBuffer.tag,
            salt: salt.toString('base64'),
            algorithm: 'aes-256-gcm',
            compressionUsed: false,
          },
          
          access: {
            visibility,
            allowedUsers: visibility === 'chat_members' && messageId ? [] : [userId], // Will be populated based on chat
            downloadCount: 0,
            expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 24 * 60 * 60 * 1000) : undefined,
          },
          
          security: {
            scanned: false,
            uploadSource: {
              deviceFingerprint,
              bleUUID,
              ipAddress,
              userAgent,
            },
          },
          
          processing: {
            status: 'processing',
            thumbnailGenerated: !!thumbnailPath,
            compressionCompleted: true,
            encryptionCompleted: true,
          },
        });

        await media.save();

        // Schedule background security scan
        setImmediate(() => performSecurityScan((media._id as any).toString()));

        uploadedMedia.push({
          mediaId: media.mediaId,
          filename: file.originalname,
          size: file.size,
          category,
          status: 'uploaded',
        });        // Log successful upload
        securityLogger.logDataAccess(
          'media',
          'media_upload',
          userId as string,
          req,
          'success',
          { mediaId, category, size: file.size, filename: file.originalname }
        );

      } catch (fileError) {
        console.error(`Failed to upload ${file.originalname}:`, fileError);
        errors.push(`${file.originalname}: Upload failed`);
      }
    }

    res.status(200).json({
      message: 'Upload completed',
      uploaded: uploadedMedia,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total: files.length,
        successful: uploadedMedia.length,
        failed: errors.length,
      },
    });

  } catch (error) {
    console.error('Media upload error:', error);
    res.status(500).json({ error: 'Failed to upload media' });
  }
}

/**
 * Download and decrypt media with access control
 * GET /api/media/:mediaId/download
 */
export async function downloadMedia(req: Request, res: Response): Promise<void> {
  try {
    const { mediaId } = req.params;
    const { decrypt = true, thumbnail = false } = req.query;
    const userId = req.user!._id;

    const media = await Media.findOne({ mediaId, isActive: true });
    if (!media) {
      res.status(404).json({ error: 'Media not found' });
      return;
    }    // Check access permissions
    const hasAccess = await checkMediaAccess(media, userId as string);
    if (!hasAccess) {      securityLogger.logSecurityViolation(
        'unauthorized_media_access',
        userId as string,
        req,
        { mediaId, attemptedAction: 'media_access' }
      );
      res.status(403).json({ error: 'Access denied' });
      return;
    }

    // Check expiry
    if (media.access.expiresAt && media.access.expiresAt < new Date()) {
      res.status(410).json({ error: 'Media has expired' });
      return;
    }

    // Get file path
    const filePath = thumbnail && media.storage.thumbnailPath 
      ? media.storage.thumbnailPath 
      : media.storage.encryptedPath;

    try {
      const encryptedBuffer = await fs.readFile(filePath);
        if (decrypt) {
        // Decrypt file
        const encryptionKey = Buffer.from(media.storage.encryptionKey, 'base64');
        const iv = media.storage.iv;
        const tag = media.storage.tag;
        const decryptedBuffer = await decryptFile({ 
          encrypted: encryptedBuffer, 
          iv, 
          tag 
        }, encryptionKey.toString('hex'));

        // Update access tracking
        media.access.downloadCount += 1;
        media.access.lastAccessed = new Date();
        await media.save();

        // Set appropriate headers
        res.setHeader('Content-Type', media.file.mimeType);
        res.setHeader('Content-Disposition', `attachment; filename="${media.file.originalName}"`);
        res.setHeader('Content-Length', decryptedBuffer.length);
        
        res.send(decryptedBuffer);
      } else {
        // Return encrypted file (for admin purposes)
        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${media.file.encryptedName}"`);
        res.send(encryptedBuffer);
      }      // Log successful download
      securityLogger.logDataAccess(
        'media',
        'media_download',
        userId as string,
        req,
        'success',
        { mediaId, fileName: media.file.originalName, thumbnail, decrypt }
      );

    } catch (fileError) {
      console.error('Failed to read media file:', fileError);
      res.status(500).json({ error: 'Failed to retrieve media file' });
    }

  } catch (error) {
    console.error('Media download error:', error);
    res.status(500).json({ error: 'Failed to download media' });
  }
}

/**
 * Get media metadata and access information
 * GET /api/media/:mediaId
 */
export async function getMediaInfo(req: Request, res: Response): Promise<void> {
  try {
    const { mediaId } = req.params;
    const userId = req.user!._id;

    const media = await Media.findOne({ mediaId, isActive: true })
      .populate('uploaderId', 'username profile.displayName')
      .populate('messageId');

    if (!media) {
      res.status(404).json({ error: 'Media not found' });
      return;
    }

    // Check access permissions
    const hasAccess = await checkMediaAccess(media, userId as string);
    if (!hasAccess) {
      res.status(403).json({ error: 'Access denied' });
      return;
    }

    // Return sanitized media info
    const mediaInfo = {
      mediaId: media.mediaId,
      filename: media.file.originalName,
      mimeType: media.file.mimeType,
      size: media.file.size,
      category: media.file.category,
      uploadedBy: media.uploaderId,
      uploadedAt: media.createdAt,
      metadata: media.metadata,
      access: {
        visibility: media.access.visibility,
        downloadCount: media.access.downloadCount,
        lastAccessed: media.access.lastAccessed,
        expiresAt: media.access.expiresAt,
      },
      processing: {
        status: media.processing.status,
        thumbnailAvailable: media.processing.thumbnailGenerated,
      },
      security: {
        scanned: media.security.scanned,
        threatLevel: media.security.scanResults?.threatLevel || 'none',
      },
    };

    res.status(200).json({ media: mediaInfo });

  } catch (error) {
    console.error('Get media info error:', error);
    res.status(500).json({ error: 'Failed to get media information' });
  }
}

/**
 * Delete media file and cleanup
 * DELETE /api/media/:mediaId
 */
export async function deleteMedia(req: Request, res: Response): Promise<void> {
  try {
    const { mediaId } = req.params;
    const userId = req.user!._id;

    const media = await Media.findOne({ mediaId, isActive: true });
    if (!media) {
      res.status(404).json({ error: 'Media not found' });
      return;
    }

    // Check if user can delete (owner or admin)
    if (media.uploaderId.toString() !== (userId as any).toString() && !req.user!.isSuperuser) {
      res.status(403).json({ error: 'Access denied' });
      return;
    }

    // Delete physical files
    try {
      await fs.unlink(media.storage.encryptedPath);
      if (media.storage.thumbnailPath) {
        await fs.unlink(media.storage.thumbnailPath);
      }
    } catch (fileError) {
      console.warn('Failed to delete media files:', fileError);
    }

    // Mark as inactive instead of deleting record for audit
    media.isActive = false;
    await media.save();    // Log deletion
    securityLogger.logDataAccess(
      'media',
      'media_delete',
      userId as string,
      req,
      'success',
      { mediaId, fileName: media.file.originalName }
    );

    res.status(200).json({ message: 'Media deleted successfully' });

  } catch (error) {
    console.error('Delete media error:', error);
    res.status(500).json({ error: 'Failed to delete media' });
  }
}

/**
 * List user's media with filtering and pagination
 * GET /api/media
 */
export async function listMedia(req: Request, res: Response): Promise<void> {
  try {
    const userId = req.user!._id;
    const {
      page = 1,
      limit = 20,
      category,
      visibility,
      status,
      search,
    } = req.query;

    const query: any = { 
      uploaderId: userId,
      isActive: true,
    };

    // Apply filters
    if (category) query['file.category'] = category;
    if (visibility) query['access.visibility'] = visibility;
    if (status) query['processing.status'] = status;
    if (search) {
      query['file.originalName'] = { $regex: search, $options: 'i' };
    }

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const [media, total] = await Promise.all([
      Media.find(query)
        .select('mediaId file.originalName file.mimeType file.size file.category access processing.status createdAt')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum),
      Media.countDocuments(query)
    ]);

    res.status(200).json({
      media,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum),
      },
    });

  } catch (error) {
    console.error('List media error:', error);
    res.status(500).json({ error: 'Failed to list media' });
  }
}

// Helper function to check media access permissions
async function checkMediaAccess(media: any, userId: string): Promise<boolean> {
  // Owner always has access
  if (media.uploaderId.toString() === userId.toString()) {
    return true;
  }

  // Superuser always has access
  const user = await require('../../models/User').default.findById(userId);
  if (user?.isSuperuser) {
    return true;
  }

  // Check visibility settings
  switch (media.access.visibility) {
    case 'private':
      return false;
    case 'superuser_only':
      return user?.isSuperuser || false;
    case 'chat_members':
      return media.access.allowedUsers.includes(userId);
    default:
      return false;
  }
}

// Background security scanning
async function performSecurityScan(mediaId: string): Promise<void> {
  try {
    const media = await Media.findById(mediaId);
    if (!media) return;    // Decrypt file for scanning
    const encryptedBuffer = await fs.readFile(media.storage.encryptedPath);
    const encryptionKey = Buffer.from(media.storage.encryptionKey, 'base64');
    const iv = media.storage.iv;
    const tag = media.storage.tag;
    const decryptedBuffer = await decryptFile({ encrypted: encryptedBuffer, iv, tag }, encryptionKey.toString('hex'));

    // Perform malware scan
    const scanResults = await scanFileForMalware(decryptedBuffer, media.file.originalName);

    // Update media record
    media.security.scanned = true;    media.security.scanResults = {
      malwareDetected: !scanResults.isClean,
      threatLevel: scanResults.threats.length > 0 ? 'high' : 'low',
      scanEngine: 'internal',
      scanDate: new Date(),
      findings: scanResults.threats,
    };
    media.processing.status = 'completed';
    media.processing.processedAt = new Date();

    await media.save();    // If malware detected, take action
    if (!scanResults.isClean) {
      console.warn(`Malware detected in media ${mediaId}:`, scanResults.threats);
      // Could implement quarantine or deletion here
    }

  } catch (error) {
    console.error('Security scan failed for media:', mediaId, error);
    
    // Mark scan as failed
    try {
      await Media.findByIdAndUpdate(mediaId, {
        'processing.status': 'failed',
        'processing.errorDetails': 'Security scan failed',
      });
    } catch (updateError) {
      console.error('Failed to update media scan status:', updateError);
    }
  }
}

// Initialize directories on module load
ensureDirectories();

// Configure routes
router.post('/upload', auth, requireUser, upload.array('files', 5), validate(uploadMediaSchema), uploadMedia);
router.get('/:mediaId/download', auth, requireUser, validate(getMediaSchema, 'query'), downloadMedia);
router.get('/:mediaId', auth, requireUser, getMediaInfo);
router.delete('/:mediaId', auth, requireUser, deleteMedia);
router.get('/', auth, requireUser, listMedia);

export default router;
