#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up an admin user with PPK authentication
 * This script will:
 * 1. Generate PPK key pair
 * 2. Create admin user in database
 * 3. Store public key in admin's record
 * 4. Save private key securely
 */

import path from 'path';
import { generatePPKKeyPair, saveKeyPair } from '../utils/ppk';
import { promises as fs } from 'fs';
import { connectDatabase } from '../config/database';
import { IAdmin } from '../models/Admin';
import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import readline from 'readline';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

interface AdminSetupOptions {
  username: string;
  email: string;
  password: string;
  role: 'admin' | 'superadmin';
  encrypted: boolean;
  passphrase?: string;
}

function question(prompt: string): Promise<string> {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    // Give a small delay to ensure the prompt is shown
    setTimeout(() => {
      rl.question('', (answer) => {
        resolve(answer);
      });
    }, 100);
  });
}

async function getAdminInfo(): Promise<AdminSetupOptions> {
  console.log('\n=== Admin User Setup ===\n');
  
  const username = await question('Enter admin username: ');
  const email = await question('Enter admin email: ');
  const password = await question('Enter admin password: ');
  const role = (await question('Enter role (admin/superadmin) [admin]: ')) || 'admin';
  const encrypted = (await question('Encrypt private key? (y/N): ')).toLowerCase() === 'y';
  
  let passphrase: string | undefined;
  if (encrypted) {
    passphrase = await question('Enter passphrase for key encryption: ');
    const confirm = await question('Confirm passphrase: ');
    if (passphrase !== confirm) {
      throw new Error('Passphrases do not match');
    }
  }

  if (!username || !email || !password) {
    throw new Error('All fields are required');
  }

  if (role !== 'admin' && role !== 'superadmin') {
    throw new Error('Invalid role. Must be either "admin" or "superadmin"');
  }

  return { username, email, password, role, encrypted, passphrase };
}

async function setupAdmin(options: AdminSetupOptions) {
  console.log('\n=== Generating Keys ===\n');

  // Create keys directory if it doesn't exist
  const keysDir = path.resolve(__dirname, '../../keys');
  await fs.mkdir(keysDir, { recursive: true });

  // Generate key pair
  const keyPair = generatePPKKeyPair(options.encrypted, options.passphrase);

  // Save keys with timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const prefix = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
  const publicKeyPath = path.join(keysDir, `${prefix}_${options.username}_public_${timestamp}.pem`);
  const privateKeyPath = path.join(keysDir, `${prefix}_${options.username}_private_${timestamp}.pem`);

  await saveKeyPair(keyPair, publicKeyPath, privateKeyPath);

  console.log('\n=== Creating Admin User ===\n');

  // Hash password
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(options.password, saltRounds);  // Get the Admin model and check if admin exists
  const Admin = mongoose.model<IAdmin>('Admin');
  let admin = await Admin.findOne({ username: options.username });
  
  if (admin) {
    console.log(`Updating existing admin user: ${options.username}`);
    admin.email = options.email;
    admin.password = passwordHash;
    admin.role = options.role;
    admin.ppkPublicKey = keyPair.publicKey;
    admin.ppkEnabled = true;
    admin.authMethod = 'both'; // Allow both password and PPK initially
    admin.ppkKeyVersion = timestamp;
    admin.ppkLastRotated = new Date();
    await admin.save();
  } else {
    console.log(`Creating new admin user: ${options.username}`);
    admin = new Admin({
      username: options.username,
      email: options.email,
      password: passwordHash,
      role: options.role,
      ppkPublicKey: keyPair.publicKey,
      ppkEnabled: true,
      authMethod: 'both', // Allow both password and PPK initially
      ppkKeyVersion: timestamp,
      ppkLastRotated: new Date()
    });
    await admin.save();
  }

  // Create key info file
  const keyInfo = {
    username: options.username,
    email: options.email,
    role: options.role,
    generatedAt: timestamp,
    encrypted: options.encrypted,
    environment: process.env.NODE_ENV || 'development',
    publicKeyPath: path.relative(process.cwd(), publicKeyPath),
    privateKeyPath: path.relative(process.cwd(), privateKeyPath)
  };

  const infoPath = path.join(keysDir, `${prefix}_${options.username}_keys_info_${timestamp}.json`);
  await fs.writeFile(infoPath, JSON.stringify(keyInfo, null, 2));

  console.log('\n=== Setup Complete ===\n');
  console.log('Admin user created successfully:');
  console.log(`Username: ${options.username}`);
  console.log(`Role: ${options.role}`);
  console.log(`Public key: ${publicKeyPath}`);
  console.log(`Private key: ${privateKeyPath}`);
  console.log(`Key info: ${infoPath}`);
  
  if (options.encrypted) {
    console.log('\nIMPORTANT: Store your passphrase securely!');
  }
  
  console.log('\nYou can now use these credentials to log in.');
}

async function main() {
  try {
    // Connect to database
    console.log('\n=== Connecting to Database ===\n');
    await connectDatabase();
    console.log('Database connected successfully');

    const options = await getAdminInfo();
    await setupAdmin(options);

  } catch (error) {
    console.error('\nError:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  } finally {
    rl.close();
    process.exit(0);
  }
}

// Run the script
main();
