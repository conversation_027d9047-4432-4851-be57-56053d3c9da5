import mongoose, { Document, Schema } from 'mongoose';

// Device model for fingerprinting and BLE management
export interface IDevice extends Document {
  userId: mongoose.Types.ObjectId;
  deviceId: string; // Unique device identifier
  
  // Device fingerprinting
  fingerprint: {
    hash: string; // Hashed fingerprint
    components: {
      userAgent?: string;
      screenResolution?: string;
      timezone?: string;
      language?: string;
      platform?: string;
      hardwareConcurrency?: number;
      deviceMemory?: number;
      colorDepth?: number;
      pixelRatio?: number;
    };
    confidence: number; // 0-100 confidence score
  };
  
  // BLE device information
  ble: {
    uuid: string; // Hashed BLE UUID
    advertisementData?: {
      localName?: string;
      serviceUUIDs?: string[];
      manufacturerData?: string;
      txPowerLevel?: number;
    };
    characteristics: {
      serviceUUID: string;
      characteristicUUID: string;
      authData: string; // Encrypted authentication data
    }[];
    lastSeen: Date;
    signalStrength?: number; // RSSI value
    verified: boolean;
  };
  
  // Device metadata
  metadata: {
    model?: string;
    os?: string;
    osVersion?: string;
    appVersion?: string;
    buildVersion?: string;
    registrationCoords?: {
      lat: number;
      lng: number;
      accuracy?: number;
    };
    registeredAt: Date;
    lastActiveAt: Date;
  };
  
  // Security status
  security: {
    status: 'trusted' | 'suspicious' | 'blocked' | 'pending';
    riskScore: number; // 0-100 risk assessment
    violations: {
      type: 'location_mismatch' | 'fingerprint_change' | 'ble_mismatch' | 'unauthorized_access';
      detectedAt: Date;
      details: string;
    }[];
    challenges: {
      challengeId: string;
      type: 'registration' | 'verification' | 'reset';
      status: 'pending' | 'completed' | 'expired';
      createdAt: Date;
      expiresAt: Date;
      attempts: number;
    }[];
  };
  
  // Network information
  network: {
    ipAddress: string;
    lastKnownIPs: string[];
    connectionHistory: {
      ip: string;
      connectedAt: Date;
      location?: {
        country?: string;
        region?: string;
        city?: string;
      };
    }[];
  };
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const DeviceSchema = new Schema<IDevice>(
  {
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    deviceId: { type: String, required: true, unique: true },
    
    fingerprint: {
      hash: { type: String, required: true },
      components: {
        userAgent: String,
        screenResolution: String,
        timezone: String,
        language: String,
        platform: String,
        hardwareConcurrency: Number,
        deviceMemory: Number,
        colorDepth: Number,
        pixelRatio: Number,
      },
      confidence: { type: Number, min: 0, max: 100, default: 0 },
    },
    
    ble: {
      uuid: { type: String, required: true },
      advertisementData: {
        localName: String,
        serviceUUIDs: [String],
        manufacturerData: String,
        txPowerLevel: Number,
      },
      characteristics: [{
        serviceUUID: { type: String, required: true },
        characteristicUUID: { type: String, required: true },
        authData: { type: String, required: true },
      }],
      lastSeen: { type: Date, default: Date.now },
      signalStrength: Number,
      verified: { type: Boolean, default: false },
    },
    
    metadata: {
      model: String,
      os: String,
      osVersion: String,
      appVersion: String,
      buildVersion: String,
      registrationCoords: {
        lat: Number,
        lng: Number,
        accuracy: Number,
      },
      registeredAt: { type: Date, default: Date.now },
      lastActiveAt: { type: Date, default: Date.now },
    },
    
    security: {
      status: {
        type: String,
        enum: ['trusted', 'suspicious', 'blocked', 'pending'],
        default: 'pending',
      },
      riskScore: { type: Number, min: 0, max: 100, default: 0 },
      violations: [{
        type: {
          type: String,
          enum: ['location_mismatch', 'fingerprint_change', 'ble_mismatch', 'unauthorized_access'],
          required: true,
        },
        detectedAt: { type: Date, default: Date.now },
        details: { type: String, required: true },
      }],
      challenges: [{
        challengeId: { type: String, required: true },
        type: {
          type: String,
          enum: ['registration', 'verification', 'reset'],
          required: true,
        },
        status: {
          type: String,
          enum: ['pending', 'completed', 'expired'],
          default: 'pending',
        },
        createdAt: { type: Date, default: Date.now },
        expiresAt: { type: Date, required: true },
        attempts: { type: Number, default: 0 },
      }],
    },
    
    network: {
      ipAddress: { type: String, required: true },
      lastKnownIPs: [String],
      connectionHistory: [{
        ip: { type: String, required: true },
        connectedAt: { type: Date, default: Date.now },
        location: {
          country: String,
          region: String,
          city: String,
        },
      }],
    },
    
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Indexes for performance
DeviceSchema.index({ userId: 1, isActive: 1 });
DeviceSchema.index({ deviceId: 1 });
DeviceSchema.index({ 'fingerprint.hash': 1 });
DeviceSchema.index({ 'ble.uuid': 1 });
DeviceSchema.index({ 'security.status': 1 });
DeviceSchema.index({ 'metadata.lastActiveAt': -1 });

export default mongoose.model<IDevice>('Device', DeviceSchema);
