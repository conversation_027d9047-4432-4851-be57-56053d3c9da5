import React, { ReactNode } from 'react';

interface Column<T> {
  header: string;
  accessor: keyof T | ((item: T) => ReactNode);
  className?: string;
}

interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[] | null | undefined;
  keyExtractor: (item: T) => string;
  emptyMessage?: string;
  isLoading?: boolean;
  onRowClick?: (item: T) => void;
}

function DataTable<T>({
  columns,
  data,
  keyExtractor,
  emptyMessage = 'No data available',
  isLoading = false,
  onRowClick
}: DataTableProps<T>) {
  // Ensure data is always an array
  const safeData = Array.isArray(data) ? data : [];
  
  const renderCell = (item: T, column: Column<T>) => {
    if (typeof column.accessor === 'function') {
      return column.accessor(item);
    }
    
    const value = item[column.accessor];
    return value as ReactNode;
  };
  
  return (
    <div className="data-table-wrapper">
      {isLoading ? (
        <div className="data-table-loading">
          <div className="loading-spinner"></div>
          <span>Loading data...</span>
        </div>
      ) : (
        <div className="data-table-container">
          <table className="data-table">
            <thead>
              <tr>
                {columns.map((column, index) => (
                  <th key={index} className={column.className}>
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>            <tbody>
              {safeData.length === 0 ? (
                <tr className="data-table-empty">
                  <td colSpan={columns.length}>{emptyMessage}</td>
                </tr>
              ) : (
                safeData.map(item => (
                  <tr 
                    key={keyExtractor(item)} 
                    onClick={onRowClick ? () => onRowClick(item) : undefined}
                    className={onRowClick ? 'clickable' : ''}
                  >
                    {columns.map((column, index) => (
                      <td key={index} className={column.className}>
                        {renderCell(item, column)}
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
      
      <style jsx>{`
        .data-table-wrapper {
          width: 100%;
          position: relative;
        }
        
        .data-table-container {
          width: 100%;
          overflow-x: auto;
        }
        
        .data-table {
          width: 100%;
          border-collapse: collapse;
          border-spacing: 0;
          font-size: 0.875rem;
        }
        
        .data-table th {
          background: #f9fafb;
          text-align: left;
          padding: var(--spacing-4) var(--spacing-5);
          font-weight: 600;
          color: var(--color-text);
          border-bottom: 1px solid var(--color-border);
          position: sticky;
          top: 0;
          z-index: 10;
          white-space: nowrap;
        }
        
        .data-table td {
          padding: var(--spacing-4) var(--spacing-5);
          border-bottom: 1px solid var(--color-border);
          color: var(--color-muted);
          transition: background var(--transition);
        }
        
        .data-table tr:last-child td {
          border-bottom: none;
        }
        
        .data-table tr:hover td {
          background: rgba(0, 0, 0, 0.01);
        }
        
        .data-table tr.clickable {
          cursor: pointer;
        }
        
        .data-table-empty td {
          text-align: center;
          padding: var(--spacing-10);
          color: var(--color-muted);
          font-style: italic;
        }
        
        .data-table-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: var(--spacing-10);
          gap: var(--spacing-4);
          color: var(--color-muted);
        }
        
        .loading-spinner {
          width: 2rem;
          height: 2rem;
          border: 2px solid rgba(0, 0, 0, 0.1);
          border-left-color: var(--color-accent);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
          .data-table th,
          .data-table td {
            padding: var(--spacing-3) var(--spacing-4);
          }
        }
      `}</style>
    </div>
  );
}

export default DataTable;
