const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema (simplified version for this script)
const UserSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true, trim: true },
  email: { type: String, trim: true },
  expressionHash: { type: String, required: true },
  unlockExpression: { type: String },
  expressionType: { 
    type: String, 
    enum: ['calculator', 'pattern'], 
    default: 'calculator' 
  },
  expressionUpdatedAt: { type: Date },
  profile: {
    displayName: { type: String, required: true },
  },
  deviceFingerprintHash: { type: String },
  deviceMetadata: {
    model: { type: String },
    os: { type: String },
    registeredAt: { type: Date },
    registrationCoords: {
      lat: { type: Number },
      lng: { type: Number },
    },
  },
  bleUUIDHash: { type: String },
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked', 'pending_device_registration'],
    default: 'pending_device_registration',
  },
  lastLoginAt: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  builds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Build' }],
  messageExpiry: { type: Date },
  isSuperuser: { type: Boolean, default: false },
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function createSimpleTestUser() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // Test user data with SIMPLE expression
    const testUsername = 'testuser2';
    const testExpression = '5+3'; // Simple: always evaluates to 8
    const testDisplayName = 'Test User 2';

    // Check if user already exists
    const existingUser = await User.findOne({ username: testUsername });
    if (existingUser) {
      console.log(`User '${testUsername}' already exists. Deleting first...`);
      await User.deleteOne({ username: testUsername });
    }

    // Hash the expression using bcrypt (to match authentication controller)
    const expressionHash = await bcrypt.hash(testExpression, 10);

    // Create the user
    const newUser = new User({
      username: testUsername,
      email: `${testUsername}@test.com`, // Add unique email to avoid index conflict
      expressionHash: expressionHash,
      unlockExpression: testExpression,
      expressionType: 'calculator',
      expressionUpdatedAt: new Date(),
      profile: {
        displayName: testDisplayName
      },
      status: 'pending_device_registration',
      failedLoginAttempts: 0,
      isSuperuser: false
    });

    const savedUser = await newUser.save();
    console.log('\n✅ Simple test user created successfully!');
    console.log('User details:');
    console.log(`  Username: ${savedUser.username}`);
    console.log(`  Display Name: ${savedUser.profile.displayName}`);
    console.log(`  Status: ${savedUser.status}`);
    console.log(`  Expression: ${savedUser.unlockExpression} (always = 8)`);
    console.log(`  Expression Type: ${savedUser.expressionType}`);

    console.log('\n📱 Mobile App Login Credentials:');
    console.log(`Username: ${testUsername}`);
    console.log(`Expression: ${testExpression}`);
    console.log('Result: 8 (no ambiguity)');

  } catch (error) {
    console.error('Error creating simple test user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

createSimpleTestUser();
