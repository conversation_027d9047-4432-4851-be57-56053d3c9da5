import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import PPKUpload from '../../components/PPKUpload';
import initPPKTestUtils from '../../utils/ppkTestUtils';
import { apiClient } from '../../utils/axiosClient';
import tokenManager from '../../utils/tokenManager';
import { useAdminAuth } from '../../contexts/AdminAuthContext';

export default function AdminLogin() {
  const { isAuthenticated, isLoading: authLoading, checkAuthStatus } = useAdminAuth();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);
  const [error, setError] = useState('');
  const [ppkChallenge, setPpkChallenge] = useState('');
  const [ppkSignature, setPpkSignature] = useState('');
  const [ppkStatus, setPpkStatus] = useState<'waiting' | 'generating' | 'ready' | 'error'>('waiting');
  const [requiresPPK, setRequiresPPK] = useState(false);
  
  const router = useRouter();
  
  // Check for session errors from query params
  useEffect(() => {
    if (router.query.session === 'expired') {
      setError('Your session has expired. Please log in again.');
    } else if (router.query.session === 'invalid') {
      setError('Invalid session. Please log in again.');
    } else if (router.query.expired === 'true') {
      setError('Your session has expired. Please log in again.');
    } else if (router.query.error) {
      setError(String(router.query.error));
    }
  }, [router.query]);

  // Check if already logged in using AdminAuthContext
  useEffect(() => {
    console.log('Login page: AuthContext state:', { isAuthenticated, authLoading, isReady: router.isReady });
    
    // Wait for both router and auth context to be ready
    if (!router.isReady || authLoading) {
      setCheckingAuth(true);
      return;
    }
    
    // If authenticated, redirect to dashboard
    if (isAuthenticated) {
      console.log('Login page: User already authenticated, redirecting to dashboard');
      router.replace('/admin/dashboard');
      return;
    }
    
    // Not authenticated, show login form
    console.log('Login page: User not authenticated, showing login form');
    setCheckingAuth(false);
  }, [isAuthenticated, authLoading, router.isReady]);

  // Initialize PPK test utilities in development mode
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      initPPKTestUtils();
      console.log('PPK testing utilities initialized for development');
    }
  }, []);
  
  // Fetch backend challenge when username changes
  useEffect(() => {
    if (!username) {
      setPpkChallenge('');
      setRequiresPPK(false);
      setPpkStatus('waiting');
      return;
    }
    
    const fetchChallenge = async () => {
      setPpkStatus('waiting');
      setPpkChallenge('');
      setRequiresPPK(false);
      
      try {
        // Use the backend API route
        const response = await apiClient.backend.post('/api/auth/admin/login/init', {
          username
        });
        
        const data = response.data;
        console.log('Challenge response:', data);
        
        if (data.requiresPPK && data.challenge) {
          setPpkChallenge(data.challenge);
          setRequiresPPK(true);
          console.log('PPK authentication required');
        } else if (!data.requiresPPK) {
          setPpkChallenge('');
          setRequiresPPK(false);
          console.log('PPK authentication not required');
        } else {
          throw new Error('Invalid response format from server');
        }
      } catch (err: any) {
        console.error('Failed to get challenge:', err);
        setError(err.response?.data?.error || err.message || 'Failed to contact backend for challenge');
      }
    };
    
    fetchChallenge();
  }, [username]);

  // Handle PPK authentication
  const handlePPKReady = (challenge: string, signature: string, privateKey: CryptoKey) => {
    setPpkSignature(signature);
    setPpkStatus('ready');
  };

  const handlePPKError = (errorMsg: string) => {
    setError(`PPK Error: ${errorMsg}`);
    setPpkStatus('error');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Prevent multiple submissions
    if (loading || checkingAuth) {
      return;
    }
    
    setLoading(true);
    setError('');

    if (requiresPPK && ppkStatus !== 'ready') {
      setError('Please complete PEM authentication first');
      setLoading(false);
      return;
    }

    try {
      // Clear any existing cookies first
      document.cookie = "admin-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie = "admin-token-access=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      
      console.log('Cookies before login:', document.cookie);
      
      // Use the backend admin login complete endpoint
      let endpoint = '/api/auth/admin/login/complete';
      let payload = {
        username,
        password,
        challenge: ppkChallenge,
        ppkSignature: ppkSignature,
        deviceFingerprint: 'web'
      };
      
      console.log('Login payload:', JSON.stringify(payload));
      
      const response = await apiClient.backend.post(endpoint, payload);
      const data = response.data;
      
      console.log('Login successful', data);
      
      // Save token using token manager
      if (data.token) {
        tokenManager.setToken(data.token, 'admin');
        console.log('Token saved securely using token manager');
        
        // Refresh the AdminAuthContext to update authentication state
        console.log('Refreshing admin auth context...');
        await checkAuthStatus();
      }

      // The useEffect will handle the redirect once isAuthenticated becomes true
      console.log('Login completed, waiting for auth context update...');
    } catch (err: any) {
      console.error('Login error:', err);
      setError(err.response?.data?.error || err.message || 'An error occurred during login');
      setPpkStatus('error');
    } finally {
      setLoading(false);
    }
  };

  const resetPpkStatus = () => {
    setPpkStatus('waiting');
    setPpkSignature('');
  };

  return (
    <>
      <Head>
        <title>CCALC Admin Portal - Secure Login</title>
        <meta name="description" content="Secure login for CCALC Admin Portal" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Show loading screen while checking authentication */}
      {checkingAuth && (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Checking authentication...</p>
          </div>
        </div>
      )}

      {/* Main login form */}
      {!checkingAuth && (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-bold text-gray-900">
              CCALC Admin Portal
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Secure Authentication Required
            </p>
          </div>

          {/* Security Badge */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-center justify-center space-x-2">
            <div className="text-blue-600">
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-sm font-medium text-blue-800">
              End-to-End Encrypted • Zero Trust
            </span>
          </div>

          {/* Login Form */}
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* Username */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  Username
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    autoComplete="username"
                    required
                    value={username}
                    onChange={(e) => {
                      setUsername(e.target.value);
                      resetPpkStatus();
                    }}
                    disabled={loading || checkingAuth}
                    className="appearance-none relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Enter your admin username"
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m6 0H9m6 0a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2v-8a2 2 0 012-2m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v0" />
                    </svg>
                  </div>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      resetPpkStatus();
                    }}
                    disabled={loading || checkingAuth}
                    className="appearance-none relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Enter your password"
                  />
                </div>
              </div>

              {/* PPK Section */}
              {requiresPPK && (
                <div className="bg-blue-50 border-2 border-blue-200 rounded-lg p-4">
                  <label className="block text-sm font-bold text-blue-700 mb-2">
                    PPK Authentication Required
                  </label>
                  <div className="mb-3">
                    {ppkStatus === 'ready' ? (
                      <div className="flex items-center text-green-600">
                        <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        <span className="font-medium">PPK Verified</span>
                      </div>
                    ) : (
                      <div className="text-blue-600 text-sm">
                        Please upload your private key file (.pem) to authenticate
                      </div>
                    )}
                  </div>
                  
                  <PPKUpload
                    challenge={ppkChallenge}
                    onPPKReady={handlePPKReady}
                    onPPKError={handlePPKError}
                    disabled={loading || !username || ppkStatus === 'generating' || ppkStatus === 'ready'}
                    className="w-full"
                  />
                  
                  {ppkStatus === 'ready' && (
                    <button
                      type="button"
                      onClick={resetPpkStatus}
                      className="mt-3 w-full inline-flex justify-center items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Reset PPK Authentication
                    </button>
                  )}
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                    <div className="ml-auto pl-3">
                      <div className="-mx-1.5 -my-1.5">
                        <button
                          type="button"
                          onClick={() => setError('')}
                          className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                        >
                          <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={loading || checkingAuth || (requiresPPK && ppkStatus !== 'ready') || !username || !password}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-blue-600 disabled:hover:to-indigo-600 transition-all duration-200"
              >
                {loading && (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {(loading || checkingAuth) ? (
                  checkingAuth ? 'Checking authentication...' : 'Authenticating...'
                ) : 'Secure Login'}
              </button>
            </div>
          </form>

          {/* Security Note */}
          <div className="text-center">
            <div className="bg-gray-50 rounded-lg p-4 text-xs text-gray-600 space-y-1">
              <p>This admin portal requires PPK (Personal Public Key) authentication in addition to username/password.</p>
              <p>All communications are end-to-end encrypted.</p>
            </div>
          </div>
        </div>
        </div>
      )}
    </>
  );
}
