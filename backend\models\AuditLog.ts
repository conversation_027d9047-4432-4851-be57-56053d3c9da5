import mongoose, { Document, Schema } from 'mongoose';

// Audit log model for security monitoring and compliance
export interface IAuditLog extends Document {
  logId: string;
  userId?: mongoose.Types.ObjectId;
  adminId?: mongoose.Types.ObjectId;
  
  // Event information
  event: {
    type: 'login' | 'logout' | 'api_access' | 'data_access' | 'system_action' | 'security_violation' | 'admin_action';
    action: string; // Specific action performed
    resource?: string; // Resource accessed (e.g., API endpoint, file)
    result: 'success' | 'failure' | 'blocked' | 'warning';
    severity: 'low' | 'medium' | 'high' | 'critical';
  };
  
  // Request context
  context: {
    ipAddress: string;
    userAgent: string;
    deviceFingerprint?: string;
    bleUUID?: string;
    sessionId?: string;
    requestId?: string;
    endpoint?: string;
    method?: string;
    statusCode?: number;
    responseTime?: number;
  };
  
  // Security details
  security: {
    riskScore?: number; // 0-100 risk assessment
    triggers?: string[]; // Security rules that fired
    blockedReason?: string;
    authMethod?: 'password' | 'ppk' | 'both';
    mfaUsed?: boolean;
    suspiciousActivity?: boolean;
    geoLocation?: {
      country?: string;
      region?: string;
      city?: string;
      lat?: number;
      lng?: number;
    };
  };
  
  // Event data
  data: {
    before?: Record<string, any>; // State before action
    after?: Record<string, any>; // State after action
    changes?: Record<string, any>; // What changed
    metadata?: Record<string, any>; // Additional event metadata
    errorDetails?: string;
    stackTrace?: string;
  };
  
  // Compliance and retention
  compliance: {
    category: 'authentication' | 'data_access' | 'system_admin' | 'security' | 'privacy';
    retention: 'short' | 'medium' | 'long' | 'permanent';
    piiIncluded: boolean;
    sensitiveData: boolean;
    exportable: boolean;
  };
  
  // Incident management
  incident?: {
    incidentId?: string;
    status: 'new' | 'investigating' | 'resolved' | 'false_positive';
    assignedTo?: mongoose.Types.ObjectId;
    priority: 'low' | 'medium' | 'high' | 'critical';
    resolution?: string;
    followUpRequired: boolean;
  };
  
  createdAt: Date;
}

const AuditLogSchema = new Schema<IAuditLog>(
  {
    logId: { type: String, required: true, unique: true },
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    adminId: { type: Schema.Types.ObjectId, ref: 'Admin' },
    
    event: {
      type: {
        type: String,
        enum: ['login', 'logout', 'api_access', 'data_access', 'system_action', 'security_violation', 'admin_action'],
        required: true,
      },
      action: { type: String, required: true },
      resource: String,
      result: {
        type: String,
        enum: ['success', 'failure', 'blocked', 'warning'],
        required: true,
      },
      severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'low',
      },
    },
    
    context: {
      ipAddress: { type: String, required: true },
      userAgent: { type: String, required: true },
      deviceFingerprint: String,
      bleUUID: String,
      sessionId: String,
      requestId: String,
      endpoint: String,
      method: String,
      statusCode: Number,
      responseTime: Number,
    },
    
    security: {
      riskScore: { type: Number, min: 0, max: 100 },
      triggers: [String],
      blockedReason: String,
      authMethod: {
        type: String,
        enum: ['password', 'ppk', 'both'],
      },
      mfaUsed: Boolean,
      suspiciousActivity: { type: Boolean, default: false },
      geoLocation: {
        country: String,
        region: String,
        city: String,
        lat: Number,
        lng: Number,
      },
    },
    
    data: {
      before: Schema.Types.Mixed,
      after: Schema.Types.Mixed,
      changes: Schema.Types.Mixed,
      metadata: Schema.Types.Mixed,
      errorDetails: String,
      stackTrace: String,
    },
    
    compliance: {
      category: {
        type: String,
        enum: ['authentication', 'data_access', 'system_admin', 'security', 'privacy'],
        required: true,
      },
      retention: {
        type: String,
        enum: ['short', 'medium', 'long', 'permanent'],
        default: 'medium',
      },
      piiIncluded: { type: Boolean, default: false },
      sensitiveData: { type: Boolean, default: false },
      exportable: { type: Boolean, default: true },
    },
    
    incident: {
      incidentId: String,
      status: {
        type: String,
        enum: ['new', 'investigating', 'resolved', 'false_positive'],
        default: 'new',
      },
      assignedTo: { type: Schema.Types.ObjectId, ref: 'Admin' },
      priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'low',
      },
      resolution: String,
      followUpRequired: { type: Boolean, default: false },
    },
  },
  { 
    timestamps: { createdAt: true, updatedAt: false } // Only track creation time
  }
);

// Indexes for performance and queries
AuditLogSchema.index({ createdAt: -1 });
AuditLogSchema.index({ userId: 1, createdAt: -1 });
AuditLogSchema.index({ adminId: 1, createdAt: -1 });
AuditLogSchema.index({ 'event.type': 1, createdAt: -1 });
AuditLogSchema.index({ 'event.severity': 1, createdAt: -1 });
AuditLogSchema.index({ 'context.ipAddress': 1 });
AuditLogSchema.index({ 'security.suspiciousActivity': 1 });
AuditLogSchema.index({ 'incident.status': 1 });

// TTL index for automatic cleanup based on compliance retention
AuditLogSchema.index({ 'compliance.retention': 1, createdAt: 1 });

export default mongoose.model<IAuditLog>('AuditLog', AuditLogSchema);
