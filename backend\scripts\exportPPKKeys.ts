/**
 * Export PPK Key Pair
 * 
 * This script exports a PPK key pair for an admin user, generating both public and private keys.
 * The private key is saved to a file that the admin should keep secure, while the public key
 * is added to the database for authentication.
 * 
 * Usage: npx ts-node exportPPKKeys.ts [username] [email]
 */

import { generatePPKKeyPair, savePrivate<PERSON>ey, exportPublic<PERSON>ey } from '../utils/ppk';
import { connectDatabase } from '../config/database';
import { AdminModel } from '../models/Admin';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get command line arguments
const username = process.argv[2];
const email = process.argv[3];

if (!username || !email) {
  console.error('Please provide username and email as arguments');
  console.error('Usage: npx ts-node exportPPKKeys.ts [username] [email]');
  process.exit(1);
}

// Ensure keys directory exists
const keysDir = path.join(__dirname, '../../keys');
if (!fs.existsSync(keysDir)) {
  fs.mkdirSync(keysDir, { recursive: true });
}

async function exportKeys() {
  try {
    // Connect to database
    await connectDatabase();
    console.log('Connected to MongoDB');

    // Check if admin exists
    const admin = await AdminModel.findOne({ username });
    if (!admin) {
      console.error(`Admin user '${username}' not found`);
      process.exit(1);
    }    console.log(`Generating PPK key pair for admin: ${username} (${email})`);
    
    // Generate key pair - using non-encrypted format for browser compatibility
    let publicKey: string;
    let privateKey: string;
    const keyPair = generatePPKKeyPair(false);
    // If the result is a KeyObject, export to PEM string
    if (typeof keyPair.publicKey === 'string' && typeof keyPair.privateKey === 'string') {
      publicKey = keyPair.publicKey;
      privateKey = keyPair.privateKey;
    } else {
      // Node.js KeyObject: export to PEM
      publicKey = keyPair.publicKey.export({ type: 'spki', format: 'pem' });
      privateKey = keyPair.privateKey.export({ type: 'pkcs8', format: 'pem' });
    }

    // Save private key
    const privateKeyPath = path.join(keysDir, `${username}_private.pem`);
    savePrivateKey(privateKey, privateKeyPath);
    console.log(`Private key saved to: ${privateKeyPath}`);
    console.log('⚠️ WARNING: Keep this file secure and do not share it! ⚠️');
    
    // Format public key for storage
    const formattedPublicKey = exportPublicKey(publicKey);
    
    // Save public key to database
    admin.ppkPublicKey = formattedPublicKey;
    await admin.save();
    console.log('Public key saved to database');
    
    // Also save public key to file for reference
    const publicKeyPath = path.join(keysDir, `${username}_public.pem`);
    fs.writeFileSync(publicKeyPath, formattedPublicKey);
    console.log(`Public key also saved to: ${publicKeyPath}`);
    
    console.log('\nPPK key export completed successfully');
    console.log('\nInstructions for the admin:');
    console.log('1. Keep the private key file secure');
    console.log('2. Use the private key file when logging into the admin portal');
    console.log('3. Never share your private key with anyone');
    
    process.exit(0);
  } catch (error) {
    console.error('Error exporting PPK keys:', error);
    process.exit(1);
  }
}

exportKeys();
