/**
 * End-to-End Encryption Utilities
 * Provides encryption/decryption for chat messages and attachments
 */

import CryptoJS from 'crypto-js';
import * as Crypto from 'expo-crypto';

export interface EncryptedData {
  data: string;
  iv: string;
  salt: string;
  isEncrypted: true;
}

export interface DecryptedData {
  data: any;
  isEncrypted: false;
}

export class ChatEncryption {
  private static readonly ALGORITHM = 'AES-256-GCM';
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly IV_LENGTH = 16; // 128 bits
  
  /**
   * Generate a secure encryption key based on user credentials
   */
  static async generateKey(userId: string, deviceFingerprint: string): Promise<string> {
    try {
      const keyMaterial = `${userId}:${deviceFingerprint}:ccalc-chat-key`;
      const digest = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        keyMaterial,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
      return digest;
    } catch (error) {
      console.warn('Falling back to simple key generation:', error);
      // Fallback to simple hash if Expo.Crypto fails
      const keyMaterial = `${userId}:${deviceFingerprint}:ccalc-chat-key`;
      return CryptoJS.SHA256(keyMaterial).toString();
    }
  }

  /**
   * Generate secure random values with fallback
   */
  static async generateSecureRandom(purpose: string): Promise<string> {
    try {
      const randomString = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        `${Date.now()}:${Math.random()}:${purpose}:${Math.random()}`,
        { encoding: Crypto.CryptoEncoding.HEX }
      );
      return randomString.substring(0, 32);
    } catch (error) {
      console.warn('Falling back to simple random generation:', error);
      // Fallback to timestamp + random
      const fallback = `${Date.now()}${Math.random()}${purpose}${Math.random()}`;
      return CryptoJS.SHA256(fallback).toString().substring(0, 32);
    }
  }

  /**
   * Encrypt chat message content
   */
  static async encryptMessage(
    content: any,
    userId: string,
    deviceFingerprint: string
  ): Promise<EncryptedData> {
    try {
      const key = await this.generateKey(userId, deviceFingerprint);
      const salt = await this.generateSecureRandom('salt');
      const iv = await this.generateSecureRandom('iv');
      
      // Convert content to JSON string
      const plaintext = JSON.stringify(content);
      
      // Encrypt using AES-256-CBC (more compatible than GCM in React Native)
      const encrypted = CryptoJS.AES.encrypt(plaintext, key + salt, {
        iv: CryptoJS.enc.Hex.parse(iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      return {
        data: encrypted.toString(),
        iv,
        salt,
        isEncrypted: true
      };
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt message');
    }
  }

  /**
   * Decrypt chat message content
   */
  static async decryptMessage(
    encryptedData: EncryptedData,
    userId: string,
    deviceFingerprint: string
  ): Promise<any> {
    try {
      const key = await this.generateKey(userId, deviceFingerprint);
      
      // Decrypt using AES-256-CBC
      const decrypted = CryptoJS.AES.decrypt(encryptedData.data, key + encryptedData.salt, {
        iv: CryptoJS.enc.Hex.parse(encryptedData.iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      const plaintext = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!plaintext) {
        throw new Error('Failed to decrypt message - invalid key or corrupted data');
      }

      return JSON.parse(plaintext);
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt message');
    }
  }

  /**
   * Encrypt attachment file data
   */
  static async encryptAttachment(
    fileData: string | ArrayBuffer,
    userId: string,
    deviceFingerprint: string
  ): Promise<EncryptedData> {
    try {
      const key = await this.generateKey(userId, deviceFingerprint);
      const salt = await this.generateSecureRandom('att-salt');
      const iv = await this.generateSecureRandom('att-iv');
      
      // Convert to base64 if it's ArrayBuffer
      const dataStr = typeof fileData === 'string' 
        ? fileData 
        : btoa(String.fromCharCode(...new Uint8Array(fileData)));
      
      // Encrypt file data using CBC mode
      const encrypted = CryptoJS.AES.encrypt(dataStr, key + salt, {
        iv: CryptoJS.enc.Hex.parse(iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      return {
        data: encrypted.toString(),
        iv,
        salt,
        isEncrypted: true
      };
    } catch (error) {
      console.error('Attachment encryption error:', error);
      throw new Error('Failed to encrypt attachment');
    }
  }

  /**
   * Decrypt attachment file data
   */
  static async decryptAttachment(
    encryptedData: EncryptedData,
    userId: string,
    deviceFingerprint: string
  ): Promise<string> {
    try {
      const key = await this.generateKey(userId, deviceFingerprint);
      
      // Decrypt file data using CBC mode
      const decrypted = CryptoJS.AES.decrypt(encryptedData.data, key + encryptedData.salt, {
        iv: CryptoJS.enc.Hex.parse(encryptedData.iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      const plaintext = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!plaintext) {
        throw new Error('Failed to decrypt attachment - invalid key or corrupted data');
      }

      return plaintext;
    } catch (error) {
      console.error('Attachment decryption error:', error);
      throw new Error('Failed to decrypt attachment');
    }
  }

  /**
   * Generate secure chat session key
   */
  static async generateSessionKey(): Promise<string> {
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      `${Date.now()}:${Math.random()}:session-key`,
      { encoding: Crypto.CryptoEncoding.HEX }
    );
  }

  /**
   * Safe message decryption with validation
   */
  static async safeDecryptMessage(
    encryptedData: any,
    userId: string,
    deviceFingerprint: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // Validate input
      if (!encryptedData || typeof encryptedData !== 'object') {
        return { success: false, error: 'Invalid encrypted data format' };
      }

      if (!encryptedData.isEncrypted || !encryptedData.data || !encryptedData.iv || !encryptedData.salt) {
        return { success: false, error: 'Missing encryption metadata' };
      }

      const decryptedData = await this.decryptMessage(encryptedData, userId, deviceFingerprint);
      return { success: true, data: decryptedData };
    } catch (error) {
      console.warn('Safe decryption failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Decryption failed' 
      };
    }
  }

  /**
   * Check if data is encrypted
   */
  static isEncryptedData(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           data.isEncrypted === true && 
           typeof data.data === 'string' && 
           typeof data.iv === 'string' && 
           typeof data.salt === 'string';
  }
}
