# CCALC Private Repository - Security-First .gitignore
# Generated on: June 20, 2025

# ================================
# SECURITY & SECRETS
# ================================
# Environment files
.env
.env.local
.env.production
.env.staging
*.env

# Private keys and certificates
*.pem
*.key
*.p12
*.pfx
*.jks
keys/
certificates/
ssl/

# JWT secrets and auth tokens
jwt-secret.txt
auth-tokens/
session-keys/

# Database credentials
database.config.js
mongodb.conf

# API keys and configuration
api-keys.json
config/secrets/
secrets/

# Admin credentials
admin-credentials.json
ppk-keys/

# ================================
# NODE.JS & NPM
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.yarn-integrity
.pnpm-debug.log*

# Package-lock files (keep yarn.lock if using yarn)
package-lock.json

# ================================
# FRONTEND (Next.js)
# ================================
frontend/.next/
frontend/out/
frontend/build/
frontend/dist/
frontend/.vercel
frontend/.env*

# Static generation
frontend/.cache/

# ================================
# BACKEND
# ================================
backend/dist/
backend/build/
backend/uploads/
backend/temp/
backend/logs/*.log
backend/public/uploads/

# Compiled TypeScript
*.tsbuildinfo

# ================================
# REACT NATIVE & MOBILE
# ================================
# iOS
app/ios/Pods/
app/ios/build/
app/ios/DerivedData/
app/ios/xcuserdata/
app/ios/*.xcworkspace/xcuserdata/
app/ios/*.xcodeproj/xcuserdata/
app/ios/*.ipa

# Android
app/android/app/build/
app/android/build/
app/android/.gradle/
app/android/local.properties
app/android/app/release/
app/android/*.apk
app/android/*.aab

# React Native
app/node_modules/
app/.expo/
app/.expo-shared/
app/expo-env.d.ts

# Metro bundler
app/.metro-cache/
app/metro-cache/

# ================================
# DOCKER & CONTAINERS
# ================================
docker/mongo-data/
docker/logs/
*.log

# Docker override files
docker-compose.override.yml
docker-compose.local.yml

# ================================
# DATABASES
# ================================
# MongoDB
dump/
*.dump
mongo-data/

# SQLite
*.sqlite
*.sqlite3
*.db

# ================================
# LOGS & DEBUGGING
# ================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
lerna-debug.log*
debug.log
error.log
access.log

# Audio processing temp files
audio-temp/
sox-temp/
voice-temp/

# ================================
# DEVELOPMENT TOOLS
# ================================
# IDE
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# ================================
# BUILD ARTIFACTS
# ================================
build/
dist/
*.tgz
*.tar.gz

# Coverage reports
coverage/
*.lcov
.nyc_output

# ================================
# PRODUCTION DEPLOYMENT
# ================================
# PM2
ecosystem.config.js
.pm2/

# Nginx logs
nginx-logs/

# SSL certificates (production)
ssl-certs/
letsencrypt/

# ================================
# CCALC SPECIFIC
# ================================
# Voice modulation temporary files
voice-modulation-temp/
sox-processing/

# Mathematical expression cache
math-cache/

# Device fingerprints (development only)
dev-fingerprints.json

# Test data
test-data/
mock-data/

# Archive folder (if contains sensitive data)
archive/sensitive/

# ================================
# TESTING
# ================================
# Test results
test-results/
coverage/
*.spec.js.snap
__tests__/__snapshots__/

# E2E test artifacts
e2e/screenshots/
e2e/videos/

# ================================
# DEPENDENCIES & CACHE
# ================================
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Yarn
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ================================
# MISC
# ================================
# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Documentation build
docs/build/
docs/.docusaurus/

# Backup files
*.bak
*.backup
