/**
 * Browser Environment Utilities
 * Handles SSR compatibility and browser-only operations
 */

/**
 * Check if code is running in browser environment
 */
export const isBrowser = (): boolean => {
  return typeof window !== 'undefined';
};

/**
 * Check if localStorage is available
 */
export const isLocalStorageAvailable = (): boolean => {
  return isBrowser() && typeof localStorage !== 'undefined';
};

/**
 * Check if sessionStorage is available
 */
export const isSessionStorageAvailable = (): boolean => {
  return isBrowser() && typeof sessionStorage !== 'undefined';
};

/**
 * Check if document is available
 */
export const isDocumentAvailable = (): boolean => {
  return isBrowser() && typeof document !== 'undefined';
};

/**
 * Safely access localStorage
 */
export const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (!isLocalStorageAvailable()) return null;
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('LocalStorage access failed:', error);
      return null;
    }
  },

  setItem: (key: string, value: string): boolean => {
    if (!isLocalStorageAvailable()) return false;
    try {
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.warn('LocalStorage write failed:', error);
      return false;
    }
  },

  removeItem: (key: string): boolean => {
    if (!isLocalStorageAvailable()) return false;
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn('LocalStorage remove failed:', error);
      return false;
    }
  },

  clear: (): boolean => {
    if (!isLocalStorageAvailable()) return false;
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.warn('LocalStorage clear failed:', error);
      return false;
    }
  }
};

/**
 * Safely access sessionStorage
 */
export const safeSessionStorage = {
  getItem: (key: string): string | null => {
    if (!isSessionStorageAvailable()) return null;
    try {
      return sessionStorage.getItem(key);
    } catch (error) {
      console.warn('SessionStorage access failed:', error);
      return null;
    }
  },

  setItem: (key: string, value: string): boolean => {
    if (!isSessionStorageAvailable()) return false;
    try {
      sessionStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.warn('SessionStorage write failed:', error);
      return false;
    }
  },

  removeItem: (key: string): boolean => {
    if (!isSessionStorageAvailable()) return false;
    try {
      sessionStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn('SessionStorage remove failed:', error);
      return false;
    }
  }
};

/**
 * Execute function only in browser environment
 */
export const executeInBrowser = <T>(fn: () => T, fallback?: T): T | undefined => {
  if (isBrowser()) {
    try {
      return fn();
    } catch (error) {
      console.warn('Browser function execution failed:', error);
      return fallback;
    }
  }
  return fallback;
};

/**
 * Execute function with delay only in browser
 */
export const executeInBrowserWithDelay = (fn: () => void, delay: number = 0): void => {
  if (isBrowser()) {
    setTimeout(fn, delay);
  }
};

export default {
  isBrowser,
  isLocalStorageAvailable,
  isSessionStorageAvailable,
  isDocumentAvailable,
  safeLocalStorage,
  safeSessionStorage,
  executeInBrowser,
  executeInBrowserWithDelay
};
