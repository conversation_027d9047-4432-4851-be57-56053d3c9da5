import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import apiClient from '../../utils/apiClient';

interface AuditLog {
  _id: string;
  createdAt: string;
  event: {
    type: string;
    action: string;
    result: string;
    severity: string;
  };
  context: {
    ipAddress: string;
    userAgent: string;
    endpoint?: string;
  };
  userId?: { username: string; email: string };
  compliance?: { category: string };
}

const SystemLogsPage: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchLogs = async (pageNum = 1) => {
    setLoading(true);
    setError(null);
    try {
      const res = await apiClient.get(`/api/system/audit?page=${pageNum}&limit=25`);
      setLogs(res.data.auditLogs || []);
      setTotalPages(res.data.pagination?.pages || 1);
    } catch (e: any) {
      setError(e.response?.data?.error || 'Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs(page);
  }, [page]);

  return (
    <>
      <Head>
        <title>System Logs | CCALC Admin</title>
      </Head>
      <AdminLayout>
        <div className="container">
          <div className="card">
            <h2>System Logs & Audit Trail</h2>
            <p className="text-muted mb-3">View system logs and audit trails.</p>
            {loading ? (
              <div>Loading logs...</div>
            ) : error ? (
              <div className="alert alert-error">{error}</div>
            ) : logs.length === 0 ? (
              <div className="text-muted">No logs found.</div>
            ) : (
              <div className="table-container">
                <table className="data-table">
                  <thead>
                    <tr>
                      <th>Time</th>
                      <th>User</th>
                      <th>Event</th>
                      <th>Action</th>
                      <th>Result</th>
                      <th>Severity</th>
                      <th>IP</th>
                      <th>Endpoint</th>
                    </tr>
                  </thead>
                  <tbody>
                    {logs.map((log) => (
                      <tr key={log._id}>
                        <td>{new Date(log.createdAt).toLocaleString()}</td>
                        <td>{log.userId?.username || '-'}</td>
                        <td>{log.event.type}</td>
                        <td>{log.event.action}</td>
                        <td>{log.event.result}</td>
                        <td>{log.event.severity}</td>
                        <td>{log.context.ipAddress}</td>
                        <td>{log.context.endpoint || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            <div className="flex items-center gap-2 mt-3">
              <button className="btn btn--secondary" onClick={() => setPage((p) => Math.max(1, p - 1))} disabled={page === 1}>Prev</button>
              <span>Page {page} of {totalPages}</span>
              <button className="btn btn--secondary" onClick={() => setPage((p) => Math.min(totalPages, p + 1))} disabled={page === totalPages}>Next</button>
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default withAdminAuth(SystemLogsPage);
