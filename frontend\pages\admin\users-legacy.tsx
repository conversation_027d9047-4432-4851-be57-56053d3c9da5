import React, { useState, useEffect, useCallback } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import DataTable from '../../components/admin/DataTable';
import Button from '../../components/admin/Button';
import { apiClient } from '../../utils/axiosClient';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { useRouter } from 'next/router';
import Icon from '../../components/admin/Icon';
// import BleDeviceManager from '../../components/admin/BleDeviceManager';
// import WebBluetoothStatus from '../../components/admin/WebBluetoothStatus';
// import useBleManager from '../../hooks/useBleManager';
// import { BleDevice, BleConnectionOptions } from '../../types/ble';
// import { 
//   CALCULATOR_SERVICE_UUID,
//   CALCULATOR_KEY_CHARACTERISTIC_UUID,
//   DEV_CALCULATOR_SERVICE_UUID,
//   DEV_CALCULATOR_CHAR_UUID,
//   BLE_ERROR_MESSAGES
// } from '../../constants/ble';
// import { 
//   isWebBluetoothSupported,
//   validateBleEnvironment,
//   parseBleError,
//   retryWithBackoff
// } from '../../utils/ble';

// Enhanced BLE functionality with proper TypeScript support
// Using @types/web-bluetooth for proper type definitions

// --- AES-GCM Encryption Helpers ---
// Module-level variable to hold the key
let aesKey: CryptoKey | null = null;

// Function to generate or retrieve an AES-GCM key
async function getAesGcmKey(): Promise<CryptoKey> {
  if (aesKey) {
    return aesKey;
  }
  // Generate a new key.
  // IMPORTANT: For production, key management needs careful consideration.
  // This example generates a key per session.
  const generatedKey = await crypto.subtle.generateKey(
    {
      name: 'AES-GCM',
      length: 256, // Key length: 128, 192, or 256 bits
    },
    true, // Allow key to be exportable (use with caution)
    ['encrypt', 'decrypt'] // Key usages
  );
  aesKey = generatedKey;
  return aesKey;
}

// Encrypt data using AES-GCM
async function encryptAESGCM(key: CryptoKey, plaintext: string): Promise<{ ciphertext: ArrayBuffer, iv: Uint8Array }> {
  const iv = crypto.getRandomValues(new Uint8Array(12)); // Initialization Vector - 96 bits (12 bytes) is recommended for AES-GCM
  const encodedPlaintext = new TextEncoder().encode(plaintext);
  const ciphertext = await crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv: iv,
    },
    key,
    encodedPlaintext
  );
  return { ciphertext, iv };
}
// --- End AES-GCM Encryption Helpers ---

// Utility for autogenerating a valid mathematical expression
async function generateValidExpression(): Promise<string> {  try {
    // Request a valid mathematical expression from the backend
    const response = await apiClient.backend.post('/api/expressions/generate', {
      complexity: Math.floor(Math.random() * 5) + 1 // Random complexity 1-5
    });
    
    if (response.data.success && response.data.data.expression) {
      return response.data.data.expression;
    } else {
      // Fallback to local generation if backend fails
      return generateLocalExpression();
    }
  } catch (error) {
    console.error('Failed to generate expression from backend, using fallback:', error);
    return generateLocalExpression();
  }
}

// Fallback local expression generator
function generateLocalExpression(): string {
  const expressions = [
    '2+3*4',
    '(5+7)*2-1',
    '√(16)+3*2',
    '15/3+4*2',
    '6^2-10+4',
    '8*3+12/4',
    '√(25)*3-7',
    '(12-4)*2+1',
    '18/2+3^2',
    '7*4-20+6'
  ];
  
  return expressions[Math.floor(Math.random() * expressions.length)];
}

// Validate expression using backend
async function validateExpression(expression: string): Promise<{ isValid: boolean; error?: string; result?: number }> {  try {
    const response = await apiClient.backend.post('/api/expressions/validate', {
      expression
    });
    
    if (response.data.success) {
      return {
        isValid: response.data.data.isValid,
        error: response.data.data.error,
        result: response.data.data.result
      };
    } else {
      return { isValid: false, error: 'Validation failed' };
    }  } catch (error) {
    console.error('Expression validation failed:', error);
    return { isValid: false, error: 'Unable to validate expression' };
  }
}

type UserFormData = {
  username: string;
  displayName: string;
  expression: string;
  isSuperuser: boolean;
};

const UsersPage: React.FC = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [userFormData, setUserFormData] = useState<UserFormData>({
    username: '',
    displayName: '',
    expression: '',
    isSuperuser: false,
  });
  
  // BLE functionality temporarily disabled for debugging
  // const bleManager = useBleManager({
  //   autoReconnect: true,
  //   connectionTimeout: 10000,
  //   maxRetries: 3
  // } as BleConnectionOptions);
  
  const [generatedBleKey, setGeneratedBleKey] = useState<string>('');
  const [encryptionReady, setEncryptionReady] = useState(false);
  const [isDevMode, setIsDevMode] = useState(false);

  const router = useRouter();

  // Fetch users from API
  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await apiClient.frontend.get('/api/admin/users');
      const responseData = response.data;
      
      let userData: any[] = [];
      
      if (Array.isArray(responseData)) {
        userData = responseData;
      } else if (responseData && Array.isArray(responseData.users)) {
        userData = responseData.users;
      } else if (responseData && Array.isArray(responseData.data)) {
        userData = responseData.data;
      } else {
        console.warn('API returned unexpected data format:', responseData);
        userData = [];
      }

      // Transform user data to include display values
      const transformedUsers = userData.map(user => ({
        ...user,
        _id: user._id || user.id,
        displayName: user.profile?.displayName || user.displayName || user.username,
        expression: user.unlockExpression || user.expression || '***',
        isSuperuser: user.isSuperuser || false,
        deviceRegistered: user.deviceRegistered !== undefined ? user.deviceRegistered : 
                         (user.status !== 'pending_device_registration'),
        status: user.status || 'unknown'
      }));

      setUsers(transformedUsers);
    } catch (err: any) {
      console.error('Error fetching users:', err);
      setError('Failed to fetch users. Please try again later.');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  }, []);  useEffect(() => {
    fetchUsers();
    
    // BLE functionality temporarily disabled for debugging
    // Use standardized environment detection
    // validateBleEnvironment().then(validation => {
    //   if (!validation.success) {
    //     console.warn('BLE environment validation failed:', validation.error);
    //   }
    //   // Check if running in development mode based on hostname
    //   const hostname = window.location.hostname;
    //   const isDev = hostname === 'localhost' || 
    //                hostname === '127.0.0.1' || 
    //                hostname.includes('192.168.') ||
    //                hostname.includes('.local');
    //   setIsDevMode(isDev);
    //   if (isDev) {
    //     console.log('Running in development mode - BLE scanning will use fallback services');
    //   }
    // });
    
    getAesGcmKey().then((key) => {
      aesKey = key;
      setEncryptionReady(true);
      console.log("AES Encryption key initialized and ready.");
    }).catch(err => {
      console.error("Failed to initialize AES key:", err);
      setError("Critical: Failed to initialize encryption module. BLE configuration will be insecure or disabled.");
      setEncryptionReady(false);
    });
  }, [fetchUsers]);

  const handleUserSelect = (user: any) => {
    setSelectedUser(user);
    setIsEditMode(true);
    setUserFormData({
      username: user.username,
      displayName: user.displayName,
      expression: user.expression,
      isSuperuser: user.isSuperuser,
    });
  };

  const handleEditCancel = () => {
    setIsEditMode(false);
    setSelectedUser(null);
    setUserFormData({
      username: '',
      displayName: '',
      expression: '',
      isSuperuser: false,
    });
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const target = e.target as HTMLInputElement;
    const { name, value, type, checked } = target;
    setUserFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };  // --- Enhanced BLE Functionality (Temporarily Disabled) ---
  const handleConnectAndWriteBleKey = async () => {
    // BLE functionality temporarily disabled for debugging
    setError('BLE functionality is currently disabled for debugging.');
    return;
    
    /*
    if (bleManager.connectedDevices.length === 0 || !generatedBleKey) {
      setError(BLE_ERROR_MESSAGES.DEVICE_DISCONNECTED);
      return;
    }
    
    // Check Web Bluetooth support first using standardized utilities
    if (!isWebBluetoothSupported()) {
      setError(BLE_ERROR_MESSAGES.NOT_SUPPORTED);
      return;
    }
    
    // Validate BLE environment
    const envValidation = await validateBleEnvironment();
    if (!envValidation.success) {
      setError(envValidation.error || BLE_ERROR_MESSAGES.ENVIRONMENT_ERROR);
      return;
    }
    
    try {
      const characteristicUuid = CALCULATOR_KEY_CHARACTERISTIC_UUID;
      const serviceUuid = CALCULATOR_SERVICE_UUID;
      
      if (aesKey && generatedBleKey) {
        const { ciphertext } = await encryptAESGCM(aesKey, generatedBleKey);
        
        try {
          const writeResult = await bleManager.writeCharacteristic(serviceUuid, characteristicUuid, ciphertext);
          if (writeResult.success) {
            console.log('Successfully wrote encrypted BLE key to device.');
            bleManager.clearError();
            setError(''); // Clear any existing errors
          } else {
            throw new Error(writeResult.error || 'Write failed');
          }
        } catch (calculatorServiceError) {
          console.warn('Calculator service not found, trying development service:', calculatorServiceError);
          
          try {
            const devWriteResult = await bleManager.writeCharacteristic(DEV_CALCULATOR_SERVICE_UUID, DEV_CALCULATOR_CHAR_UUID, ciphertext);
            if (devWriteResult.success) {
              console.log('Successfully wrote encrypted BLE key to development device.');
              bleManager.clearError();
              setError(''); // Clear any existing errors
            } else {
              throw new Error(devWriteResult.error || 'Dev write failed');
            }
          } catch (devServiceError) {
            console.warn('Development calculator service not found:', devServiceError);
            setError('Could not write key: Calculator service not available on this device.');
          }
        }
      } else {
        setError('Encryption key or BLE key is missing.');
      }
    } catch (error: any) {
      console.error('Error writing BLE key:', error);
      
      // Use standardized error parsing
      const parsedError = parseBleError(error);
      setError(parsedError);
    }
    */
  };  const handleGenerateBleKey = () => {
    try {
      const newKey = crypto.getRandomValues(new Uint8Array(16));
      const keyString = Array.from(newKey).map(b => String.fromCharCode(b)).join('');
      setGeneratedBleKey(keyString);
      setUserFormData(prev => ({
        ...prev,
        bleUUID: keyString,
      }));
      console.log('Generated new BLE key:', keyString);
      // bleManager.clearError(); // Temporarily disabled
    } catch (error) {
      console.error('Error generating BLE key:', error);
      setError('Failed to generate BLE key. Please try again.');
    }
  };

  const handleSaveUser = async () => {
    try {
      // Validate form data
      if (!userFormData.username || !userFormData.displayName || !userFormData.expression) {
        setError('Please fill in all required fields');
        return;
      }

      // Validate expression
      const expressionValidation = await validateExpression(userFormData.expression);
      if (!expressionValidation.isValid) {
        setError(`Invalid expression: ${expressionValidation.error}`);
        return;
      }

      const existingSuperuser = Array.isArray(users) ? users.find(user => user.isSuperuser && user._id !== selectedUser?._id) : null;
      
      if (existingSuperuser && userFormData.isSuperuser) {
        setError("There can be only one superuser. Please remove the existing superuser status from the other user before proceeding.");
        return;
      }

      // Prepare user data without device fingerprint
      const userData = {
        ...userFormData
      };

      if (selectedUser) {
        // Update existing user
        const response = await apiClient.frontend.put(`/api/admin/users/${selectedUser._id}`, userData);
        console.log('User updated successfully:', response.data);
        
        // Update users list
        setUsers(prev => Array.isArray(prev) ? prev.map(user => 
          user._id === selectedUser._id ? { ...response.data, displayName: response.data.displayName } : user
        ) : [response.data]);
        
        handleEditCancel();
      } else {
        // Create new user
        const response = await apiClient.frontend.post('/api/admin/users', userData);
        console.log('User created successfully:', response.data);
        
        // Add to users list
        setUsers(prev => Array.isArray(prev) ? [...prev, { 
          ...response.data, 
          displayName: response.data.displayName || response.data.profile?.displayName 
        }] : [response.data]);
        
        handleEditCancel();
      }
    } catch (err: any) {
      console.error('Error saving user:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to save user. Please try again later.';
      setError(errorMessage);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      if (confirm('Are you sure you want to delete this user?')) {
        await apiClient.frontend.delete(`/api/admin/users/${userId}`);
        setUsers(prev => Array.isArray(prev) ? prev.filter(user => user._id !== userId) : []);
        if (selectedUser?._id === userId) {
          handleEditCancel();
        }
      }
    } catch (err: any) {
      console.error('Error deleting user:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to delete user. Please try again later.';
      setError(errorMessage);
    }
  };

  const existingSuperuser = Array.isArray(users) ? users.find(user => user.isSuperuser && user._id !== selectedUser?._id) : null;

  return (
    <AdminLayout>
      <Head>
        <title>Admin - Users</title>
      </Head>
      <div className="p-6 max-w-7xl mx-auto animate-fadeIn">
        <div className="mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            User Management
          </h1>
          <p className="text-gray-600">Manage users, expressions, and BLE device configurations</p>
        </div>        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>        )}

        {/* BLE components temporarily disabled for debugging */}
        {/* 
        {bleManager.lastError && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-800">{bleManager.lastError}</p>
              </div>
            </div>
          </div>
        )}

        <div className="mb-6">
          <WebBluetoothStatus showInstructions={true} />
        </div>
        */}

        <Button onClick={() => setIsEditMode(true)} className="mb-4">
          <Icon name="add-user" className="mr-2" />
          Add New User
        </Button>

        {loading ? (
          <div className="text-center py-4">Loading users...</div>
        ) : (
          <DataTable
            data={Array.isArray(users) ? users : []}
            onRowClick={handleUserSelect}
            columns={[
              { header: 'Username', accessor: 'username' },
              { header: 'Display Name', accessor: 'displayName' },
              { header: 'Expression', accessor: 'expression' },
              { header: 'Status', accessor: 'status' },
              { header: 'Device Registered', accessor: 'deviceRegistered' },
              { header: 'Superuser', accessor: 'isSuperuser' },
            ]}
            keyExtractor={(user) => user._id || user.id || `user-${Math.random()}`}
          />
        )}

        {isEditMode && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white/90 backdrop-blur-sm border border-white/20 rounded-xl shadow-2xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {selectedUser ? 'Edit User' : 'Add New User'}
              </h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Username</label>
                  <input
                    type="text"
                    name="username"
                    value={userFormData.username}
                    onChange={handleFormChange}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Display Name</label>
                  <input
                    type="text"
                    name="displayName"
                    value={userFormData.displayName}
                    onChange={handleFormChange}
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Expression</label>
                  <div className="flex">
                    <input
                      type="text"
                      name="expression"
                      value={userFormData.expression}
                      onChange={handleFormChange}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
                      placeholder="Enter mathematical expression or auto-generate"
                    />
                    <Button
                      onClick={async () => {
                        const validExpression = await generateValidExpression();
                        setUserFormData(prev => ({ ...prev, expression: validExpression }));
                      }}
                      className="ml-2"
                      variant="outline"
                    >
                      Auto Generate
                    </Button>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Complex mathematical expression (16-30 characters) with calculator-friendly operators
                  </p>
                </div>
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700">Superuser</label>
                  {!existingSuperuser && (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        name="isSuperuser"
                        checked={userFormData.isSuperuser}
                        onChange={handleFormChange}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-600">Grant superuser access</span>
                    </div>
                  )}
                  {existingSuperuser && (
                    <div className="text-sm text-gray-500">
                      Another superuser already exists. Only one superuser is allowed at a time.
                    </div>
                  )}
                </div>
              </div>
              
              {/* BLE Key Configuration Section */}
              {generatedBleKey && (
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">BLE Key Configuration</h3>
                  <p className="text-sm text-blue-600 mb-3">
                    Generated BLE key: <code className="bg-blue-100 px-2 py-1 rounded">{generatedBleKey}</code>
                  </p>                  <Button
                    onClick={handleConnectAndWriteBleKey}
                    disabled={!encryptionReady}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Write Key to Connected Device (Disabled)
                  </Button>
                  <p className="text-xs text-blue-500 mt-2">
                    BLE functionality is temporarily disabled for debugging.
                  </p>
                </div>
              )}
              
              <div className="mt-6 flex justify-end space-x-3">
                <Button onClick={handleEditCancel} variant="outline" className="px-6 py-2">
                  Cancel
                </Button>
                <Button onClick={handleSaveUser} className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                  {selectedUser ? 'Update User' : 'Create User'}
                </Button>
              </div>
            </div>
          </div>
        )}        {/* Enhanced BLE Device Manager Component - Temporarily Disabled */}
        {/* 
        <div className="mt-8">
          <BleDeviceManager 
            onDeviceSelected={(device) => {
              console.log('Device selected:', device);
            }}
            onDeviceDisconnected={(deviceId) => {
              console.log('Device disconnected:', deviceId);
            }}
            onError={(error) => {
              setError(error);
            }}
            selectedServiceUUIDs={[CALCULATOR_SERVICE_UUID, DEV_CALCULATOR_SERVICE_UUID]}
            className="bg-white rounded-lg shadow-md p-6"
          />
        </div>
        */}
      </div>
    </AdminLayout>
  );
};

export default UsersPage;
