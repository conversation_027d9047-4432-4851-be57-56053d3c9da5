/**
 * MediaStorageMigration - One-time migration from cache to document storage
 * Helps transition existing media from old cache dir to new persistent document storage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

const OLD_CACHE_DIR = `${FileSystem.documentDirectory}CCALC_Media_Cache/`;
const NEW_MEDIA_DIR = `${FileSystem.documentDirectory}CCALC_Media/`;
const NEW_THUMBS_DIR = `${FileSystem.documentDirectory}CCALC_Media/Thumbnails/`;

/**
 * Migrates media files from old cache directory to new persistent media storage
 * Returns the number of files migrated
 */
export const migrateMediaStorage = async (): Promise<number> => {
  try {
    console.log('🔄 Starting media storage migration...');
    
    // Check if we've already migrated
    const migrationComplete = await AsyncStorage.getItem('media_migration_completed');
    if (migrationComplete === 'true') {
      console.log('✅ Media migration already completed');
      return 0;
    }
    
    // Create new directories if they don't exist
    await ensureDirectoryExists(NEW_MEDIA_DIR);
    await ensureDirectoryExists(NEW_THUMBS_DIR);
    
    // Check if old cache directory exists
    const oldDirInfo = await FileSystem.getInfoAsync(OLD_CACHE_DIR);
    if (!oldDirInfo.exists) {
      // No old directory found, mark migration as complete
      console.log('✅ No old cache directory found, marking migration as complete');
      await AsyncStorage.setItem('media_migration_completed', 'true');
      return 0;
    }
    
    // Get list of files in old cache directory
    const oldFiles = await FileSystem.readDirectoryAsync(OLD_CACHE_DIR);
    console.log(`🔍 Found ${oldFiles.length} files in old cache directory`);
    
    if (oldFiles.length === 0) {
      // No files to migrate, mark migration as complete
      await AsyncStorage.setItem('media_migration_completed', 'true');
      return 0;
    }
    
    let migrationCount = 0;
    
    // Process each file
    for (const file of oldFiles) {
      try {
        const oldPath = `${OLD_CACHE_DIR}${file}`;
        
        // Determine if it's a thumbnail or regular media file
        const isThumb = file.includes('_thumb');
        const newPath = isThumb 
          ? `${NEW_THUMBS_DIR}${file}` 
          : `${NEW_MEDIA_DIR}${file}`;
        
        // Copy file to new location
        await FileSystem.copyAsync({
          from: oldPath,
          to: newPath,
        });
        
        migrationCount++;
        console.log(`✅ Migrated file: ${file}`);
      } catch (error) {
        console.error(`❌ Failed to migrate file ${file}:`, error);
      }
    }
    
    // Once all files are migrated, delete the old directory
    if (migrationCount > 0) {
      try {
        await FileSystem.deleteAsync(OLD_CACHE_DIR, { idempotent: true });
        console.log('🗑️ Deleted old cache directory after successful migration');
      } catch (error) {
        console.warn('⚠️ Failed to delete old cache directory:', error);
      }
    }
    
    // Mark migration as complete
    await AsyncStorage.setItem('media_migration_completed', 'true');
    
    console.log(`✅ Media migration completed: ${migrationCount} files migrated`);
    return migrationCount;
  } catch (error) {
    console.error('❌ Media migration failed:', error);
    return 0;
  }
};

/**
 * Helper to ensure a directory exists
 */
const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  try {
    const dirInfo = await FileSystem.getInfoAsync(dirPath);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(dirPath, { intermediates: true });
      console.log('📁 Created directory:', dirPath);
    }
  } catch (error) {
    console.error(`Failed to create directory ${dirPath}:`, error);
    throw error;
  }
};

/**
 * Run the migration and show result
 */
export const runMediaStorageMigration = async (): Promise<void> => {
  try {
    const migratedCount = await migrateMediaStorage();
    
    if (migratedCount > 0) {
      Alert.alert(
        'Media Storage Upgraded',
        `Successfully migrated ${migratedCount} media files to persistent storage. Your media will no longer disappear.`,
        [{ text: 'OK' }]
      );
    }
  } catch (error) {
    console.error('Media migration failed:', error);
  }
};
