import type { NextApiRequest, NextApiResponse } from 'next';
import { rateLimit } from '../../../utils/rateLimit';
import axios from 'axios';
import apiClient, { apiEndpoints } from '../../../utils/apiClient';

const limiter = rateLimit({
  interval: 60 * 1000, // 60 seconds
  uniqueTokenPerInterval: 500, // Max 500 users per second
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await limiter.check(res, 10, 'DASHBOARD_STATS_RATE_LIMIT'); // 10 requests per minute

    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Check if admin is logged in using Authorization header
    const authHeader = req.headers.authorization;
    console.log('Dashboard stats - Auth header:', authHeader ? 'Present' : 'Missing');
    console.log('Dashboard stats - Backend URL:', process.env.NEXT_PUBLIC_BACKEND_URL);
    console.log('Dashboard stats - Session endpoint:', apiEndpoints.auth.adminSession);
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('Dashboard stats - Missing or invalid auth header');
      return res.status(401).json({ error: 'Authorization Bearer token required' });
    }

    try {
      // Call the frontend session endpoint internally (same server)
      const sessionUrl = `${process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3005'}${apiEndpoints.auth.adminSession}`;
      console.log('Dashboard stats - Checking session at:', sessionUrl);
      
      await axios.get(sessionUrl, {
        headers: {
          'Authorization': authHeader,
        },
        timeout: 5000
      });
      
      console.log('Dashboard stats - Session verification successful');
    } catch (error) {
      console.log('Dashboard stats - Session verification failed:', axios.isAxiosError(error) ? error.response?.status : 'Unknown error', axios.isAxiosError(error) ? error.response?.data : error);
      if (axios.isAxiosError(error) && error.response) {
        return res.status(401).json({ error: 'Unauthorized', details: error.response.data });
      }
      throw error;
    }

    // Forward request to backend API
    try {
      const backendUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.dashboard.stats}`;
      console.log('Dashboard stats - Calling backend at:', backendUrl);
      
      const response = await axios.get(backendUrl, {
        headers: {
          'Authorization': authHeader,
        },
        timeout: 10000
      });

      // Transform backend response to match frontend interface
      const backendData = response.data;
      if (backendData.success && backendData.health) {
        const health = backendData.health;
        const transformedData = {
          totalUsers: health.statistics?.users || 0,
          activeUsers: health.active?.activeUsers || 0,
          totalChats: health.statistics?.chats || 0,
          totalCalls: health.statistics?.calls || 0,
          latestBuild: {
            version: 'v1.0.0', // Placeholder - could be extracted from system info
            platform: health.system?.platform || 'unknown',
            releaseDate: new Date().toISOString()
          },
          serverStatus: {
            status: health.status === 'healthy' ? 'online' : 'offline',
            uptime: health.system?.uptime ? `${Math.floor(health.system.uptime / 3600)}h ${Math.floor((health.system.uptime % 3600) / 60)}m` : 'N/A'
          }
        };
        return res.status(200).json(transformedData);
      } else {
        // If backend response format is unexpected, return default structure
        return res.status(200).json({
          totalUsers: 0,
          activeUsers: 0,
          totalChats: 0,
          totalCalls: 0,
          latestBuild: {
            version: 'N/A',
            platform: 'N/A',
            releaseDate: new Date().toISOString()
          },
          serverStatus: {
            status: 'offline',
            uptime: 'N/A'
          }
        });
      }
    } catch (error) {
      console.log('Dashboard stats - Backend call failed:', error);
      if (axios.isAxiosError(error)) {
        console.log('Dashboard stats - Backend error status:', error.response?.status);
        console.log('Dashboard stats - Backend error data:', error.response?.data);
        console.log('Dashboard stats - Backend error message:', error.message);
        return res.status(error.response?.status || 500).json({ 
          error: 'Backend error', 
          details: error.response?.data || error.message 
        });
      }
      console.log('Dashboard stats - Unknown backend error:', error);
      return res.status(500).json({ error: 'Unknown backend error', details: String(error) });
    }
  } catch (error) {
    console.error('Dashboard stats error:', error);
    return res.status(500).json({ error: 'Failed to fetch dashboard data' });
  }
}
