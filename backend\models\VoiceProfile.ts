import mongoose, { Schema, Document } from 'mongoose';

export interface IVoiceProfile extends Document {
  type: 'global' | 'user';
  userId?: mongoose.Types.ObjectId;
  profile: {
    enabled: boolean;
    profiles: {
      [key: string]: {
        pitch: number;
        speed: number;
        distortion: number;
        resonance: number;
      };
    };
    superuserDefaults?: {
      enabled: boolean;
      defaultProfile: string;
      forceModulation: boolean;
    };
    quality?: {
      sampleRate: number;
      bitDepth: number;
      bufferSize: number;
    };
    processing?: {
      noiseReduction: boolean;
      echoCancellation: boolean;
      autoGainControl: boolean;
    };
  };
  updatedBy: mongoose.Types.ObjectId;
  updatedAt: Date;
  createdAt: Date;
}

const VoiceProfileSchema: Schema = new Schema({
  type: {
    type: String,
    required: true,
    enum: ['global', 'user'],
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
    index: true
  },
  profile: {
    enabled: {
      type: Boolean,
      default: true
    },
    profiles: {
      type: Schema.Types.Mixed,
      default: {}
    },
    superuserDefaults: {
      enabled: { type: Boolean, default: true },
      defaultProfile: { type: String, default: 'robotic' },
      forceModulation: { type: Boolean, default: true }
    },
    quality: {
      sampleRate: { type: Number, default: 44100 },
      bitDepth: { type: Number, default: 16 },
      bufferSize: { type: Number, default: 2048 }
    },
    processing: {
      noiseReduction: { type: Boolean, default: true },
      echoCancellation: { type: Boolean, default: true },
      autoGainControl: { type: Boolean, default: false }
    }
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'voiceprofiles'
});

// Compound index for type and userId
VoiceProfileSchema.index({ type: 1, userId: 1 }, { unique: true });

// Update the updatedAt field on save
VoiceProfileSchema.pre('save', function(this: IVoiceProfile, next) {
  this.updatedAt = new Date();
  next();
});

export default mongoose.model<IVoiceProfile>('VoiceProfile', VoiceProfileSchema);
