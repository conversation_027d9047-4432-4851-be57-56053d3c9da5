import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get admin token from cookies
    const cookies = parse(req.headers.cookie || '');
    const token = cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid build ID' });
    }

    if (req.method === 'GET') {
      // Get build details
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/builds/${id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      return res.status(response.status).json(data);
    } 
    else if (req.method === 'DELETE') {
      // Delete/revoke build
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/builds/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      return res.status(response.status).json(data);
    } 
    else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Build API error:', error);
    return res.status(500).json({ error: 'Failed to process request' });
  }
}
