// JavaScript wrapper for the TypeScript App component
// This ensures proper module loading without Node.js TypeScript issues

import React from 'react';

// Import the TypeScript App component
// Metro bundler will handle the TypeScript compilation
let AppComponent;

try {
  AppComponent = require('./App.tsx').default;
} catch (error) {
  console.error('Failed to load TypeScript App component:', error);
  
  // Fallback component
  const { View, Text, StyleSheet } = require('react-native');
  
  AppComponent = () => (
    <View style={styles.container}>
      <Text style={styles.text}>Error loading app. Check console for details.</Text>
    </View>
  );
  
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#f5f5f5',
    },
    text: {
      fontSize: 16,
      color: '#ff0000',
      textAlign: 'center',
      margin: 20,
    },
  });
}

export default AppComponent;
