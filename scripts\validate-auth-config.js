/**
 * Authentication Configuration Validator
 * This script validates the authentication configuration and endpoints
 */
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

console.log(chalk.blue('🔍 Validating Authentication Configuration'));
console.log(chalk.blue('=======================================\n'));

// Configuration variables
const frontendPort = 3001;
const backendPort = 3000;
const frontendUrl = `http://localhost:${frontendPort}`;
const backendUrl = `http://localhost:${backendPort}`;

// Check frontend .env file
console.log(chalk.yellow('1. Checking frontend environment variables:'));
try {
  const envPath = path.join(__dirname, '../frontend/.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    console.log(chalk.green('✓ Frontend .env.local file exists'));
    
    // Check if BACKEND_URL is set correctly
    if (envContent.includes(`BACKEND_URL=${backendUrl}`)) {
      console.log(chalk.green(`✓ BACKEND_URL is correctly set to ${backendUrl}`));
    } else {
      console.log(chalk.red(`✗ BACKEND_URL is not set to ${backendUrl}`));
    }
    
    // Check if NEXT_PUBLIC_FRONTEND_URL is set correctly
    if (envContent.includes(`NEXT_PUBLIC_FRONTEND_URL=${frontendUrl}`)) {
      console.log(chalk.green(`✓ NEXT_PUBLIC_FRONTEND_URL is correctly set to ${frontendUrl}`));
    } else {
      console.log(chalk.red(`✗ NEXT_PUBLIC_FRONTEND_URL is not set to ${frontendUrl}`));
    }
  } else {
    console.log(chalk.red('✗ Frontend .env.local file does not exist'));
  }
} catch (error) {
  console.log(chalk.red(`Error checking frontend environment variables: ${error.message}`));
}

// Check API endpoints in frontend code
console.log(chalk.yellow('\n2. Checking frontend API endpoints:'));
try {
  const apiClientPath = path.join(__dirname, '../frontend/utils/apiClient.ts');
  if (fs.existsSync(apiClientPath)) {
    const apiClientContent = fs.readFileSync(apiClientPath, 'utf8');
    console.log(chalk.green('✓ API client file exists'));
    
    // Check if admin session endpoint is correctly defined
    if (apiClientContent.includes('adminSession: \'/api/auth/admin/session\'')) {
      console.log(chalk.green('✓ Admin session endpoint is correctly defined'));
    } else {
      console.log(chalk.red('✗ Admin session endpoint is not correctly defined'));
    }
  } else {
    console.log(chalk.red('✗ API client file does not exist'));
  }
} catch (error) {
  console.log(chalk.red(`Error checking API endpoints: ${error.message}`));
}

// Check session handling in frontend API routes
console.log(chalk.yellow('\n3. Checking admin session API route:'));
try {
  const sessionApiPath = path.join(__dirname, '../frontend/pages/api/auth/admin/session.ts');
  if (fs.existsSync(sessionApiPath)) {
    const sessionApiContent = fs.readFileSync(sessionApiPath, 'utf8');
    console.log(chalk.green('✓ Admin session API route exists'));
    
    // Check if it's making the correct request to the backend
    if (sessionApiContent.includes('/api/auth/admin/session')) {
      console.log(chalk.green('✓ Calls correct backend endpoint /api/auth/admin/session'));
    } else {
      console.log(chalk.red('✗ Does not call correct backend endpoint'));
    }
  } else {
    console.log(chalk.red('✗ Admin session API route does not exist'));
  }
} catch (error) {
  console.log(chalk.red(`Error checking session API route: ${error.message}`));
}

console.log(chalk.yellow('\n4. Summary:'));
console.log(`  Frontend: ${frontendUrl}`);
console.log(`  Backend: ${backendUrl}`);
console.log('  Expected authentication flow:');
console.log('    1. Client logs in at /api/auth/admin/login');
console.log('    2. Token is stored in HttpOnly cookie');
console.log('    3. Session is verified with backend at /api/auth/admin/session');
console.log('    4. On successful verification, redirected to dashboard');

console.log(chalk.blue('\n🔐 Authentication configuration validation complete'));
