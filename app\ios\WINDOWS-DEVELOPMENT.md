# CCALC iOS Development on Windows

Since you're developing on Windows but need to test on iOS, here are your options:

## 🔧 **Immediate Solutions for Your Crash**

### **The Root Cause**
Your Xcode crash is caused by:
1. **Missing CocoaPods dependencies** - No `Pods/` directory exists
2. **Workspace referencing non-existent files** - CocoaPods project missing
3. **Xcode version compatibility** - You're using Xcode 12.5 beta

### **Quick Fix for macOS User**
```bash
# Navigate to iOS directory
cd app/ios

# Install CocoaPods (if not installed)
brew install cocoapods
# OR
sudo gem install cocoapods

# Install dependencies
pod install

# Open the workspace (NOT the project)
open CCALC.xcworkspace
```

## 📱 **Development Options from Windows**

### **Option 1: EAS Build (Recommended)**
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios
```

### **Option 2: React Native CLI**
```bash
# Run iOS simulator (requires macOS connection)
npx react-native run-ios

# Or build for device
npx react-native run-ios --device
```

### **Option 3: Expo Go**
```bash
# Start development server
npm start

# Use Expo Go app on iPhone to test
```

## 🖥️ **Remote Development Solutions**

### **Cloud-Based macOS**
- **GitHub Codespaces** with macOS runner
- **MacStadium** cloud Mac rental
- **AWS EC2 Mac instances**

### **Local Network Mac**
- Use your Mac as a build server
- SSH tunneling for development
- Screen sharing for Xcode access

## 🔧 **Project Structure Fixes**

I've already implemented these fixes:

### **1. Xcode Compatibility**
- ✅ Updated to Xcode 9.3 compatibility (works with 12.5)
- ✅ Removed modern Xcode features causing crashes
- ✅ Fixed workspace configuration

### **2. CocoaPods Setup**
- ✅ Created automated installation script (`install-pods.sh`)
- ✅ Temporary workspace without CocoaPods for testing
- ✅ Proper Podfile configuration

### **3. File Structure**
- ✅ Removed redundant `iosproject/` references
- ✅ Added privacy manifest for App Store compliance
- ✅ Complete native file structure

## 📋 **Diagnostic Tools**

I've created diagnostic scripts to help identify issues:

```bash
# Run comprehensive diagnostics
./diagnose.sh

# Automated CocoaPods setup (macOS only)
./install-pods.sh
```

## 🚀 **Recommended Workflow**

### **For Immediate Testing**
1. **On Mac**: Run `cd ios && ./install-pods.sh`
2. **Open**: `CCALC.xcworkspace` (not `.xcodeproj`)
3. **Build**: Press Cmd+R in Xcode

### **For Windows Development**
1. **Develop**: Use VS Code on Windows
2. **Test**: Use Expo Go or EAS Build
3. **Deploy**: Use EAS Build for App Store

## ⚠️ **Critical Notes**

### **Always Use Workspace**
- ❌ Never open `CCALC.xcodeproj`
- ✅ Always open `CCALC.xcworkspace`

### **CocoaPods Required**
- The crash occurs because Xcode expects CocoaPods dependencies
- Run `pod install` before opening Xcode
- The `Pods/` directory must exist

### **Xcode Version**
- Your Xcode 12.5 beta should work after `pod install`
- Consider updating to a stable Xcode version
- Check compatibility with your macOS version

## 🐛 **Troubleshooting**

### **If Xcode Still Crashes**
```bash
# Clean everything
rm -rf ios/Pods ios/Podfile.lock

# Reinstall
cd ios && pod install

# Clean Xcode
# Product → Clean Build Folder
```

### **If Build Fails**
```bash
# Reset Metro cache
npm start -- --reset-cache

# Clear Xcode derived data
rm -rf ~/Library/Developer/Xcode/DerivedData
```

## 📞 **Getting Help**

1. **Run diagnostics**: `./ios/diagnose.sh`
2. **Check setup**: Review `ios/README-XCODE.md`
3. **EAS Build**: Use cloud building for iOS

The project is now properly configured - the main issue was missing CocoaPods dependencies causing Xcode workspace corruption.
