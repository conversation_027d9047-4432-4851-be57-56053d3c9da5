# CCALC Mobile App - Quick Reference

## 🚀 Essential Commands

### Setup & Verification
```bash
npm install                    # Install dependencies
npm run verify-setup          # Verify configuration
./setup-build.sh              # Interactive setup (macOS/Linux)
setup-build.bat               # Interactive setup (Windows)
```

### Development
```bash
npm start                     # Start Expo dev server
npm run ios                   # Run on iOS simulator
npm run android               # Run on Android emulator
```

### Building
```bash
npm run build:preview         # Build preview (recommended for testing)
npm run build:ios            # Build iOS production
npm run build:android        # Build Android production
npm run build:development    # Build development version
```

## 🔧 Environment Configuration

### Quick .env Setup
1. Copy template: `cp .env.template .env`
2. Update URLs in `.env`:
   ```properties
   EXPO_PUBLIC_BACKEND_URL=https://your-backend.ngrok.io
   EXPO_PUBLIC_FRONTEND_URL=https://your-frontend.ngrok.io
   ```

## 🤖 GitHub Actions (CI/CD)

### Prerequisites
1. Add `EXPO_TOKEN` to GitHub repository secrets
2. Ensure ngrok URLs are accessible

### Running Builds
1. Go to Actions → "Build Mobile Apps (iOS & Android)"
2. Click "Run workflow"
3. Enter your backend/frontend URLs
4. Select platforms (iOS, Android, or both)
5. Choose build profile (github-ci recommended)

### Available Workflows
- `ios-build.yml` - iOS only builds
- `android-build.yml` - Android only builds  
- `build-mobile-apps.yml` - Combined iOS/Android builds

## 📱 Build Profiles

| Profile | Use Case | Distribution |
|---------|----------|--------------|
| `development` | Local development | Internal |
| `preview` | Testing builds | Internal |
| `production` | App Store builds | Public |
| `github-ci` | CI/CD builds | Internal |

## 🆘 Troubleshooting

### Common Issues
```bash
# Environment not configured
npm run verify-setup

# Build fails - check dependencies
npm install
npm run verify-setup

# Network errors - check ngrok URLs
curl https://your-backend.ngrok.io/health

# Images disappearing - test on standalone build
npm run build:preview
```

### Getting Help
1. Check `BUILD_GUIDE.md` for detailed instructions
2. Run verification: `npm run verify-setup`
3. Review GitHub Actions logs for CI issues

## 📚 Documentation
- `README.md` - Main documentation
- `BUILD_GUIDE.md` - Detailed build guide
- `CCALC_MOBILE_BUILD_SYSTEM_COMPLETE.md` - Implementation summary

---
**Ready to build? Start with `npm run verify-setup`** ✅
