{"name": "ccalc-admin-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3005", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@types/js-cookie": "^3.0.6", "@types/web-bluetooth": "^0.0.21", "axios": "^1.6.0", "bcrypt": "^6.0.0", "body-parser": "^2.2.0", "chart.js": "^4.5.0", "cookie": "^0.6.0", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.1", "framer-motion": "^12.19.2", "joi": "^17.11.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "next": "latest", "react": "latest", "react-chartjs-2": "^5.3.0", "react-dom": "latest", "react-web-bluetooth": "^1.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cookie": "^0.6.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "latest", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.0.0"}}