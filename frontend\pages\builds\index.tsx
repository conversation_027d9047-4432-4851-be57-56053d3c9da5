import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { apiClient } from '../../utils/axiosClient';
import AdminLayout from '../../components/admin/AdminLayout';
import DashboardCard from '../../components/admin/DashboardCard';
import DataTable from '../../components/admin/DataTable';
import Button from '../../components/admin/Button';
import Icon from '../../components/admin/Icon';

interface Build {
  _id: string;
  version: string;
  releaseDate: Date;
  status: string;
  description: string;
  platform: string;
  downloadUrl?: string;
  changelog?: string;
}

export default function Builds() {
  const [builds, setBuilds] = useState<Build[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();
  
  useEffect(() => {
    // Check if admin is logged in and fetch builds
    const fetchBuilds = async () => {
      try {
        // First check session
        await apiClient.frontend.get('/api/auth/admin/session');

        // Then fetch builds
        const response = await apiClient.frontend.get('/api/builds');
        setBuilds(response.data.builds || []);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'An error occurred');
        // Redirect to login page if not authenticated
        if (err.response?.status === 401) {
          router.push('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchBuilds();
  }, [router]);

  const handleAddBuild = () => {
    router.push('/builds/new');
  };

  const handleEditBuild = (buildId: string) => {
    router.push(`/builds/${buildId}`);
  };
  
  const handleDeleteBuild = async (buildId: string) => {
    if (!confirm('Are you sure you want to delete this build?')) {
      return;
    }

    try {
      await apiClient.frontend.delete(`/api/builds/${buildId}`);
      // Remove build from list
      setBuilds(builds.filter(build => build._id !== buildId));
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to delete build');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'released':
        return 'var(--color-success)';
      case 'beta':
        return 'var(--color-accent)';
      case 'alpha':
        return 'var(--color-warning)';
      case 'development':
        return '#fa8c16'; // orange
      case 'deprecated':
        return 'var(--color-error)';
      default:
        return 'var(--color-muted)';
    }
  };

  const getBadgeStyle = (status: string) => {
    const color = getStatusColor(status);
    return {
      display: 'inline-block',
      padding: '0.25rem 0.6rem',
      borderRadius: '9999px',
      fontSize: '0.75rem',
      fontWeight: 500,
      backgroundColor: `${color}20`,
      color: color,
      textTransform: 'capitalize'
    } as React.CSSProperties;
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading builds...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>CCALC Admin - Builds</title>
      </Head>
      
      <div className="page-header">
        <h2 className="page-title">Build Management</h2>
        <Button 
          variant="primary"
          onClick={handleAddBuild}
          leftIcon={<Icon name="add" size={16} />}
        >
          Add New Build
        </Button>
      </div>
        {error && (
        <div className="alert alert-error">
          <Icon name="alert-circle" size={16} />
          <span>{error}</span>
          <button onClick={() => setError('')} className="alert-close">×</button>
        </div>
      )}
      
      <DashboardCard>
        <DataTable
          columns={[
            { header: 'Version', accessor: 'version' },
            { header: 'Platform', accessor: 'platform' },
            { 
              header: 'Status', 
              accessor: (build) => (
                <span style={getBadgeStyle(build.status)}>
                  {build.status}
                </span>
              ),
              className: 'text-center'
            },
            { 
              header: 'Release Date', 
              accessor: (build) => new Date(build.releaseDate).toLocaleDateString() 
            },
            { header: 'Description', accessor: 'description' },
            { 
              header: 'Actions', 
              accessor: (build) => (
                <div className="action-buttons">
                  <Button 
                    variant="secondary" 
                    size="sm" 
                    onClick={() => handleEditBuild(build._id)}
                  >
                    Edit
                  </Button>
                  <Button 
                    variant="danger" 
                    size="sm" 
                    onClick={() => handleDeleteBuild(build._id)}
                  >
                    Delete
                  </Button>
                  {build.downloadUrl && (
                    <Button 
                      variant="success" 
                      size="sm" 
                      onClick={() => window.open(build.downloadUrl, '_blank')}
                    >
                      Download
                    </Button>
                  )}
                </div>
              ),
              className: 'actions-cell'
            }
          ]}
          data={builds}
          keyExtractor={(build) => build._id}
          emptyMessage="No builds found"
          isLoading={loading}
        />
      </DashboardCard>
      
      <style jsx>{`
        .page-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-6);
        }
        
        .page-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--color-text);
          margin: 0;
        }
        
        .alert {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
          padding: var(--spacing-4) var(--spacing-5);
          border-radius: var(--radius);
          margin-bottom: var(--spacing-6);
        }
        
        .alert-error {
          background: rgba(220, 38, 38, 0.1);
          border: 1px solid rgba(220, 38, 38, 0.2);
          color: var(--color-error);
        }
        
        .alert-close {
          margin-left: auto;
          background: none;
          border: none;
          color: inherit;
          font-size: 1.25rem;
          cursor: pointer;
          padding: 0;
        }
        
        :global(.action-buttons) {
          display: flex;
          gap: var(--spacing-2);
        }
        
        :global(.actions-cell) {
          white-space: nowrap;
        }
        
        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 60vh;
          gap: var(--spacing-4);
        }
        
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(37, 99, 235, 0.1);
          border-top-color: var(--color-accent);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
          .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-4);
          }
          
          :global(.action-buttons) {
            flex-direction: column;
            gap: var(--spacing-2);
          }
        }
      `}</style>
    </AdminLayout>
  );
}
