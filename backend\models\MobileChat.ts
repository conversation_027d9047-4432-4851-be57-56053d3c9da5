import mongoose, { Document, Schema } from 'mongoose';

// Mobile Chat models for simplified mobile app architecture
export interface IMobileMessage extends Document {
  messageId: string;
  chatId: string;
  senderId: string;
  recipientId: string;
  content: {
    type: 'text' | 'media' | 'attachment';
    text: string;
    timestamp: number;
    attachment?: {
      originalName: string;
      fileName: string;
      filePath: string;
      mimeType: string;
      size: number;
      uploadedAt: Date;
    };
  };
  metadata: {
    sentAt: Date;
    platform: string;
    deviceType: string;
  };
  status: 'sent' | 'delivered' | 'read' | 'failed';
  isEncrypted: boolean;
}

export interface IMobileChat extends Document {
  chatId: string;
  participants: string[];
  type: 'direct';
  metadata: {
    createdAt: Date;
    lastActivity: Date;
    messageCount: number;
    lastMessage?: string;
  };
  isActive: boolean;
}

const MobileMessageSchema = new Schema<IMobileMessage>(
  {
    messageId: { type: String, required: true, unique: true },
    chatId: { type: String, required: true },
    senderId: { type: String, required: true },
    recipientId: { type: String, required: true },
    content: {
      type: { type: String, enum: ['text', 'media', 'attachment'], default: 'text' },
      text: { type: String, required: true },
      timestamp: { type: Number, required: true },
      attachment: {
        originalName: { type: String },
        fileName: { type: String },
        filePath: { type: String },
        mimeType: { type: String },
        size: { type: Number },
        uploadedAt: { type: Date },
      },
    },
    metadata: {
      sentAt: { type: Date, default: Date.now },
      platform: { type: String, default: 'mobile' },
      deviceType: { type: String, default: 'iOS' },
    },
    status: {
      type: String,
      enum: ['sent', 'delivered', 'read', 'failed'],
      default: 'sent',
    },
    isEncrypted: { type: Boolean, default: false },
  },
  { timestamps: true }
);

const MobileChatSchema = new Schema<IMobileChat>(
  {
    chatId: { type: String, required: true, unique: true },
    participants: [{ type: String, required: true }],
    type: { type: String, enum: ['direct'], default: 'direct' },
    metadata: {
      createdAt: { type: Date, default: Date.now },
      lastActivity: { type: Date, default: Date.now },
      messageCount: { type: Number, default: 0 },
      lastMessage: String,
    },
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Indexes
MobileMessageSchema.index({ chatId: 1, 'metadata.sentAt': -1 });
MobileMessageSchema.index({ senderId: 1, recipientId: 1 });
MobileChatSchema.index({ participants: 1 });
MobileChatSchema.index({ chatId: 1 });

export const MobileMessage = mongoose.model<IMobileMessage>('MobileMessage', MobileMessageSchema);
export const MobileChat = mongoose.model<IMobileChat>('MobileChat', MobileChatSchema);

export default { MobileMessage, MobileChat };
