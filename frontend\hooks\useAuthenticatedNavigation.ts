/**
 * Authenticated Navigation Hook
 * Integrates session validation with routing
 */

import { useRouter } from 'next/router';
import { useCallback } from 'react';
import { sessionManager } from '../utils/sessionManager';
import { tokenManager } from '../utils/tokenManager';

export const useAuthenticatedNavigation = () => {
  const router = useRouter();

  /**
   * Navigate with session validation
   */
  const navigateWithAuth = useCallback(async (
    path: string,
    options: { replace?: boolean; shallow?: boolean } = {}
  ) => {
    try {
      // Check if user is authenticated
      const isAuthenticated = tokenManager.isAuthenticated('admin') || tokenManager.isAuthenticated('user');
      
      if (!isAuthenticated) {
        // Redirect to login if not authenticated
        if (options.replace) {
          await router.replace('/?session=invalid');
        } else {
          await router.push('/?session=invalid');
        }
        return false;
      }

      // Validate current session with server
      const isSessionValid = await sessionManager.validateSession();
      
      if (!isSessionValid) {
        // Session is invalid, expire it and redirect
        sessionManager.expireSession('error');
        return false;
      }

      // Update activity on successful navigation
      sessionManager.updateActivity();

      // Navigate to the requested path
      if (options.replace) {
        await router.replace(path, undefined, { shallow: options.shallow });
      } else {
        await router.push(path, undefined, { shallow: options.shallow });
      }

      return true;
    } catch (error) {
      console.error('Navigation error:', error);
      
      // On error, redirect to login
      try {
        if (options.replace) {
          await router.replace('/?session=error');
        } else {
          await router.push('/?session=error');
        }
      } catch (redirectError) {
        // Fallback to window location
        window.location.href = '/?session=error';
      }
      
      return false;
    }
  }, [router]);

  /**
   * Check if current route requires authentication
   */
  const isProtectedRoute = useCallback((pathname: string) => {
    const protectedPaths = [
      '/admin',
      '/users',
      '/builds',
      '/security',
      '/controls'
    ];
    
    return protectedPaths.some(path => pathname.startsWith(path));
  }, []);

  /**
   * Validate current route access
   */
  const validateRouteAccess = useCallback(async () => {
    const currentPath = router.pathname;
    
    if (!isProtectedRoute(currentPath)) {
      return true;
    }

    const isAuthenticated = tokenManager.isAuthenticated('admin') || tokenManager.isAuthenticated('user');
    
    if (!isAuthenticated) {
      await router.replace('/?session=invalid');
      return false;
    }

    const isSessionValid = await sessionManager.validateSession();
    
    if (!isSessionValid) {
      sessionManager.expireSession('error');
      return false;
    }

    return true;
  }, [router, isProtectedRoute]);

  return {
    navigateWithAuth,
    isProtectedRoute,
    validateRouteAccess
  };
};

export default useAuthenticatedNavigation;
