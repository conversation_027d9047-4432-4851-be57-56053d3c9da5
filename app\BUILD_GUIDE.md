# CCALC Mobile App - Build Setup Guide

This guide explains how to build the CCALC mobile app for iOS and Android using GitHub Actions with proper environment configuration.

## 🚀 Quick Setup

### 1. Local Development Setup

```bash
# Navigate to the app directory
cd app

# Run the setup script (Windows)
setup-build.bat

# Or run the setup script (macOS/Linux)
chmod +x setup-build.sh
./setup-build.sh
```

### 2. Manual Environment Setup

Create a `.env` file in the `app/` directory:

```bash
# Backend URL - Your ngrok backend URL
EXPO_PUBLIC_BACKEND_URL=https://your-backend.ngrok.io

# Frontend URL - Your ngrok frontend URL  
EXPO_PUBLIC_FRONTEND_URL=https://your-frontend.ngrok.io

# Build Environment
EXPO_PUBLIC_BUILD_ENV=development

# App Version
EXPO_PUBLIC_APP_VERSION=1.0.0

# Debug flags
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_NETWORK=true
```

## 📱 Building the App

### Local Builds

```bash
# Install dependencies
npm install

# Build for iOS (requires Mac or EAS)
npm run build:ios

# Build for Android
npm run build:android

# Build preview version for both platforms
npm run build:preview
```

### GitHub Actions Builds

1. **Add Repository Secrets:**
   - Go to your GitHub repository settings
   - Navigate to "Secrets and variables" → "Actions"
   - Add the following secret:
     - `EXPO_TOKEN`: Your Expo access token (get from https://expo.dev/settings/access-tokens)

2. **Start a Build:**
   - Go to the "Actions" tab in your GitHub repository
   - Select "Build Mobile Apps (iOS & Android)"
   - Click "Run workflow"
   - Enter your ngrok URLs:
     - **Backend URL**: `https://your-backend.ngrok.io`
     - **Frontend URL**: `https://your-frontend.ngrok.io` (or same as backend)
   - Choose build profile and platforms
   - Click "Run workflow"

## 🔧 Configuration Files

### Environment Variables

The app uses the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `EXPO_PUBLIC_BACKEND_URL` | Backend API URL | `https://abc123.ngrok.io` |
| `EXPO_PUBLIC_FRONTEND_URL` | Frontend URL | `https://xyz789.ngrok.io` |
| `EXPO_PUBLIC_BUILD_ENV` | Build environment | `development`, `preview`, `production` |
| `EXPO_PUBLIC_APP_VERSION` | App version | `1.0.0` |
| `EXPO_PUBLIC_DEBUG_MODE` | Enable debug logging | `true`, `false` |
| `EXPO_PUBLIC_LOG_NETWORK` | Log network requests | `true`, `false` |

### Build Profiles

The app supports multiple build profiles in `eas.json`:

- **development**: Development builds with Expo dev client
- **preview**: Internal distribution builds for testing
- **production**: Production builds for app stores
- **github-ci**: Special profile for GitHub Actions builds

## 🌐 Ngrok Setup

### 1. Start your backend server
```bash
cd backend
npm start
```

### 2. Start ngrok tunnel
```bash
# Install ngrok if you haven't already
npm install -g ngrok

# Create tunnel for backend (port 3000)
ngrok http 3000
```

### 3. Update environment
Copy the ngrok URL (e.g., `https://abc123.ngrok.io`) and update your `.env` file or use it in the GitHub Actions workflow.

## 📋 Build Checklist

Before building, ensure:

- [ ] Backend server is running
- [ ] Ngrok tunnel is active and accessible
- [ ] `.env` file is configured with correct URLs
- [ ] `EXPO_TOKEN` is added to GitHub repository secrets
- [ ] EAS CLI is installed (`npm install -g eas-cli`)
- [ ] You're logged into Expo (`expo login`)

## 🛠️ Troubleshooting

### Common Issues

1. **"EXPO_TOKEN not found"**
   - Add `EXPO_TOKEN` to your GitHub repository secrets
   - Get token from https://expo.dev/settings/access-tokens

2. **"Network request failed"**
   - Check if your ngrok URL is accessible
   - Ensure backend server is running
   - Verify URLs in `.env` file

3. **"Build failed - iOS code signing"**
   - Configure iOS credentials with `eas credentials`
   - Ensure you have a valid Apple Developer account

4. **"Build timeout"**
   - Large builds may take 20-30 minutes
   - Use `--wait` flag to wait for completion

### Debug Commands

```bash
# Check Expo authentication
expo whoami

# List recent builds
eas build:list

# View build logs
eas build:view [BUILD_ID]

# Configure credentials
eas credentials

# Update EAS configuration
eas build:configure
```

## 📚 Additional Resources

- [Expo EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Ngrok Documentation](https://ngrok.com/docs)
- [React Native Environment Setup](https://reactnative.dev/docs/environment-setup)

## 🎯 Production Deployment

For production deployment:

1. Update `EXPO_PUBLIC_BUILD_ENV=production` in your environment
2. Use production backend URLs (not ngrok)
3. Configure app store credentials
4. Use the `production` build profile
5. Submit to app stores using `eas submit`

---

Need help? Check the [troubleshooting section](#troubleshooting) or create an issue in the repository.
