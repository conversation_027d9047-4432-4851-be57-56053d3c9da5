/**
 * Mathematical Expression Validator
 * Validates that expressions are valid mathematical expressions
 * and can be safely evaluated for authentication purposes
 */

interface ValidationResult {
  isValid: boolean;
  error?: string;
  normalizedExpression?: string;
  result?: number;
}

/**
 * Valid mathematical operators
 */
const VALID_OPERATORS = ['+', '-', '*', '/', '^', '(', ')', '√'];

/**
 * Valid mathematical functions
 */
const VALID_FUNCTIONS = ['sin', 'cos', 'tan', 'log', 'ln', 'sqrt', 'abs', 'floor', 'ceil', 'round'];

/**
 * Validates a mathematical expression
 * @param expression The mathematical expression to validate
 * @returns ValidationResult with isValid flag and details
 */
export function validateMathematicalExpression(expression: string): ValidationResult {
  try {
    // Basic validation
    if (!expression || typeof expression !== 'string') {
      return {
        isValid: false,
        error: 'Expression is required and must be a string'
      };
    }

    // Remove whitespace and normalize
    const normalizedExpression = expression.replace(/\s+/g, '');

    // Length validation
    if (normalizedExpression.length < 3) {
      return {
        isValid: false,
        error: 'Expression must be at least 3 characters long'
      };
    }

    if (normalizedExpression.length > 100) {
      return {
        isValid: false,
        error: 'Expression must be less than 100 characters'
      };
    }

    // Character validation - only allow numbers, operators, and valid functions
    const allowedCharsRegex = /^[0-9+\-*/^()√.,sincotaglbrfqud\s]+$/i;
    if (!allowedCharsRegex.test(normalizedExpression)) {
      return {
        isValid: false,
        error: 'Expression contains invalid characters'
      };
    }

    // Check for balanced parentheses
    if (!hasBalancedParentheses(normalizedExpression)) {
      return {
        isValid: false,
        error: 'Unbalanced parentheses in expression'
      };
    }

    // Check for valid operator placement
    if (!hasValidOperatorPlacement(normalizedExpression)) {
      return {
        isValid: false,
        error: 'Invalid operator placement'
      };
    }

    // Must contain at least one operator or function
    if (!containsOperatorOrFunction(normalizedExpression)) {
      return {
        isValid: false,
        error: 'Expression must contain at least one mathematical operator or function'
      };
    }

    // Must contain at least one number
    if (!/\d/.test(normalizedExpression)) {
      return {
        isValid: false,
        error: 'Expression must contain at least one number'
      };
    }

    // Try to safely evaluate the expression
    const evaluationResult = safeEvaluateExpression(normalizedExpression);
    if (!evaluationResult.success) {
      return {
        isValid: false,
        error: `Invalid mathematical expression: ${evaluationResult.error}`
      };
    }

    // Check if result is a valid number
    if (!isFinite(evaluationResult.result!)) {
      return {
        isValid: false,
        error: 'Expression result must be a finite number'
      };
    }

    return {
      isValid: true,
      normalizedExpression,
      result: evaluationResult.result
    };

  } catch (error) {
    return {
      isValid: false,
      error: `Expression validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Check if parentheses are balanced
 */
function hasBalancedParentheses(expression: string): boolean {
  let count = 0;
  for (const char of expression) {
    if (char === '(') count++;
    if (char === ')') count--;
    if (count < 0) return false;
  }
  return count === 0;
}

/**
 * Check if operators are placed correctly
 */
function hasValidOperatorPlacement(expression: string): boolean {
  // Cannot start or end with operators (except negative sign at start)
  const operators = ['+', '*', '/', '^'];
  
  // Check first character
  if (operators.includes(expression[0])) {
    return false;
  }
  
  // Check last character
  if ([...operators, '-'].includes(expression[expression.length - 1])) {
    return false;
  }
  
  // Check for consecutive operators
  for (let i = 0; i < expression.length - 1; i++) {
    const current = expression[i];
    const next = expression[i + 1];
    
    if (operators.includes(current) && operators.includes(next)) {
      return false;
    }
  }
  
  return true;
}

/**
 * Check if expression contains at least one operator or function
 */
function containsOperatorOrFunction(expression: string): boolean {
  // Check for basic operators
  if (/[+\-*/^√]/.test(expression)) {
    return true;
  }
  
  // Check for functions
  for (const func of VALID_FUNCTIONS) {
    if (expression.toLowerCase().includes(func)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Safely evaluate a mathematical expression
 * Uses a whitelist approach to prevent code injection
 */
function safeEvaluateExpression(expression: string): { success: boolean; result?: number; error?: string } {
  try {
    // Replace mathematical symbols with JavaScript equivalents
    let jsExpression = expression
      .replace(/\^/g, '**')  // Power operator
      .replace(/√/g, 'Math.sqrt');  // Square root
    
    // Replace mathematical functions
    jsExpression = jsExpression
      .replace(/sin/gi, 'Math.sin')
      .replace(/cos/gi, 'Math.cos')
      .replace(/tan/gi, 'Math.tan')
      .replace(/log/gi, 'Math.log10')
      .replace(/ln/gi, 'Math.log')
      .replace(/sqrt/gi, 'Math.sqrt')
      .replace(/abs/gi, 'Math.abs')
      .replace(/floor/gi, 'Math.floor')
      .replace(/ceil/gi, 'Math.ceil')
      .replace(/round/gi, 'Math.round');

    // Additional security check - ensure only safe characters
    const safeExpressionRegex = /^[0-9+\-*/().,\sMath\s]+$/;
    if (!safeExpressionRegex.test(jsExpression)) {
      return {
        success: false,
        error: 'Expression contains unsafe characters after conversion'
      };
    }

    // Evaluate using Function constructor (safer than eval)
    const result = new Function('Math', `"use strict"; return (${jsExpression})`)(Math);
    
    return {
      success: true,
      result: Number(result)
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Evaluation failed'
    };
  }
}

/**
 * Generate a valid mathematical expression for user authentication
 * @param complexity Level of complexity (1-5)
 * @returns A valid mathematical expression
 */
export function generateValidMathExpression(complexity: number = 3): string {
  const operators = ['+', '-', '*', '/'];
  const numbers = () => Math.floor(Math.random() * 20) + 1;
  
  switch (complexity) {
    case 1:
      // Simple: a + b
      return `${numbers()}+${numbers()}`;
    
    case 2:
      // Medium: a * b + c
      return `${numbers()}*${numbers()}+${numbers()}`;
    
    case 3:
      // Complex: (a + b) * c - d
      return `(${numbers()}+${numbers()})*${numbers()}-${numbers()}`;
    
    case 4:
      // Advanced: a^2 + b*c - d
      return `${numbers()}^2+${numbers()}*${numbers()}-${numbers()}`;
    
    case 5:
      // Expert: √(a*b) + (c-d)*e
      const a = numbers(), b = numbers(), c = numbers(), d = numbers(), e = numbers();
      return `√(${a*b})+(${c}-${d})*${e}`;
    
    default:
      return generateValidMathExpression(3);
  }
}

/**
 * Validate and normalize expression for storage
 * @param expression Raw expression from user
 * @returns Normalized expression suitable for storage and comparison
 */
export function normalizeExpression(expression: string): string {
  const validation = validateMathematicalExpression(expression);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }
  
  return validation.normalizedExpression!;
}

/**
 * Calculate the complexity level of a mathematical expression
 * Used for authentication - more complex expressions provide better security
 * @param expression The mathematical expression to analyze
 * @returns Complexity level (1-5)
 */
export function calculateExpressionComplexity(expression: string): number {
  try {
    const normalized = expression.replace(/\s+/g, '');
    let complexity = 1;

    // Base complexity factors
    const length = normalized.length;
    if (length > 10) complexity++;
    if (length > 20) complexity++;

    // Check for operators
    const operators = ['+', '-', '*', '/', '^'];
    const operatorCount = operators.reduce((count, op) => 
      count + (normalized.split(op).length - 1), 0);
    
    if (operatorCount >= 2) complexity++;
    if (operatorCount >= 4) complexity++;

    // Check for functions
    const functionMatches = normalized.match(/(sin|cos|tan|log|ln|sqrt|abs|floor|ceil|round)/g);
    if (functionMatches && functionMatches.length > 0) {
      complexity += Math.min(functionMatches.length, 2);
    }

    // Check for parentheses (nested operations)
    const parenCount = (normalized.match(/\(/g) || []).length;
    if (parenCount >= 1) complexity++;
    if (parenCount >= 3) complexity++;

    // Check for decimal numbers
    const decimalCount = (normalized.match(/\d+\.\d+/g) || []).length;
    if (decimalCount > 0) complexity++;

    // Cap at level 5
    return Math.min(complexity, 5);
  } catch (error) {
    return 1; // Default to lowest complexity on error
  }
}

export default {
  validateMathematicalExpression,
  generateValidMathExpression,
  normalizeExpression,
  calculateExpressionComplexity
};
