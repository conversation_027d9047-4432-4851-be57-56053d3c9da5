/**
 * Session Management System
 * Handles session timeouts, token refresh, and session validity
 */

import { tokenManager } from './tokenManager';
import apiClient from './apiClient';
import { clearCsrfToken } from './csrfProtection';
import { isBrowser, safeLocalStorage } from './browserUtils';

export interface SessionConfig {
  // Session timeout in minutes (default: 30 minutes)
  timeoutMinutes: number;
  // Warning before timeout in minutes (default: 5 minutes)
  warningMinutes: number;
  // Check interval for session validity in seconds (default: 60 seconds)
  checkIntervalSeconds: number;
  // Enable automatic token refresh
  autoRefresh: boolean;
}

export interface SessionStatus {
  isActive: boolean;
  timeRemaining: number; // in milliseconds
  isWarning: boolean;
  lastActivity: Date;
}

class SessionManagerImpl {
  private config: SessionConfig = {
    timeoutMinutes: 8 * 60, // 8 hours to match backend token expiry  
    warningMinutes: 15,
    checkIntervalSeconds: 60,
    autoRefresh: true,
  };

  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private lastActivity: Date = new Date();
  private isSessionActive: boolean = false;
  private sessionStartTime: Date = new Date();
  private listeners: Array<(status: SessionStatus) => void> = [];
  private warningShown: boolean = false;
  private crossTabListenerAdded: boolean = false;

  /**
   * Initialize session management
   */
  initialize(config?: Partial<SessionConfig>): void {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    this.updateActivity();
    this.startSessionMonitoring();
  }

  /**
   * Start session monitoring
   */
  private startSessionMonitoring(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
    }

    this.sessionCheckInterval = setInterval(() => {
      this.checkSessionValidity();
    }, this.config.checkIntervalSeconds * 1000);
  }

  /**
   * Update last activity timestamp
   */
  updateActivity(): void {
    this.lastActivity = new Date();
    this.warningShown = false;
    
    // If session wasn't active, mark it as active
    if (!this.isSessionActive) {
      this.isSessionActive = true;
      this.sessionStartTime = new Date();
    }

    // Store activity in localStorage for cross-tab sync (only in browser)
    if (isBrowser()) {
      safeLocalStorage.setItem('lastActivity', this.lastActivity.getTime().toString());
    }

    // Sync cross-tab activity
    this.syncCrossTabActivity();

    // Notify listeners of activity update
    this.notifyListeners();
  }

  /**
   * Check session validity and handle timeouts
   */
  private checkSessionValidity(): void {
    if (!this.isSessionActive) {
      return;
    }

    // Sync with cross-tab activity
    this.syncCrossTabActivity();

    const now = new Date();
    const timeSinceActivity = now.getTime() - this.lastActivity.getTime();
    const timeoutMs = this.config.timeoutMinutes * 60 * 1000;
    const warningMs = this.config.warningMinutes * 60 * 1000;

    // Check if session has expired
    if (timeSinceActivity >= timeoutMs) {
      this.expireSession('timeout');
      return;
    }

    // Check if we should show warning
    if (timeSinceActivity >= (timeoutMs - warningMs) && !this.warningShown) {
      this.warningShown = true;
      this.notifyListeners();
    }

    // Auto-refresh token if enabled and halfway to expiration
    if (this.config.autoRefresh && timeSinceActivity >= (timeoutMs / 2)) {
      this.refreshSession().catch(() => {
        // If refresh fails, let normal timeout handling continue
      });
    }
  }

  /**
   * Sync activity across tabs
   */
  private syncCrossTabActivity(): void {
    if (!isBrowser()) return;

    // Check if other tabs have more recent activity
    const storedActivity = safeLocalStorage.getItem('lastActivity');
    if (storedActivity) {
      const storedTime = parseInt(storedActivity, 10);
      if (!isNaN(storedTime) && storedTime > this.lastActivity.getTime()) {
        this.lastActivity = new Date(storedTime);
        this.warningShown = false;
      }
    }

    // Listen for storage changes from other tabs
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'lastActivity' && event.newValue) {
        const newTime = parseInt(event.newValue, 10);
        if (!isNaN(newTime) && newTime > this.lastActivity.getTime()) {
          this.lastActivity = new Date(newTime);
          this.warningShown = false;
          this.notifyListeners();
        }
      }
    };

    // Only add listener once
    if (!this.crossTabListenerAdded) {
      window.addEventListener('storage', handleStorageChange);
      this.crossTabListenerAdded = true;
    }
  }

  /**
   * Get current session status
   */
  getSessionStatus(): SessionStatus {
    const now = new Date();
    const timeSinceActivity = now.getTime() - this.lastActivity.getTime();
    const timeoutMs = this.config.timeoutMinutes * 60 * 1000;
    const warningMs = this.config.warningMinutes * 60 * 1000;

    return {
      isActive: this.isSessionActive,
      timeRemaining: Math.max(0, timeoutMs - timeSinceActivity),
      isWarning: timeSinceActivity >= (timeoutMs - warningMs),
      lastActivity: this.lastActivity,
    };
  }

  /**
   * Refresh session token
   */
  async refreshSession(): Promise<boolean> {
    try {
      // Check if we have a valid token
      const currentToken = tokenManager.getToken('admin');
      if (!currentToken) {
        return false;
      }      // Call session refresh endpoint
      const response = await apiClient.post('/api/auth/admin/refresh');
      
      if (response.data.success && response.data.token) {
        // Update token
        tokenManager.setToken(response.data.token, 'admin');
        
        // Update CSRF token if provided
        if (response.data.csrfToken) {
          const { setCsrfToken } = await import('./csrfProtection');
          setCsrfToken(response.data.csrfToken);
        }

        // Update activity
        this.updateActivity();
        
        return true;
      }

      return false;
    } catch (error) {
      console.error('Session refresh failed:', error);
      return false;
    }
  }

  /**
   * Expire the session
   */
  expireSession(reason: 'timeout' | 'logout' | 'error' = 'timeout'): void {
    this.isSessionActive = false;
    this.clearSessionData();
    
    // Notify listeners
    this.notifyListeners();

    // Redirect to login with appropriate message
    if (isBrowser()) {
      const params = new URLSearchParams();
      if (reason === 'timeout') {
        params.set('session', 'expired');
      } else if (reason === 'error') {
        params.set('session', 'error');
      }
      
      window.location.href = `/?${params.toString()}`;
    }
  }

  /**
   * Clear session data
   */
  private clearSessionData(): void {
    tokenManager.clearAllTokens();
    clearCsrfToken();
    
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
  }

  /**
   * Manually logout
   */
  async logout(): Promise<void> {
    try {      // Call logout endpoint
      await apiClient.post('/api/auth/admin/logout');
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      this.expireSession('logout');
    }
  }

  /**
   * Add session status listener
   */
  addListener(callback: (status: SessionStatus) => void): () => void {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners of session status change
   */
  private notifyListeners(): void {
    const status = this.getSessionStatus();
    this.listeners.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('Session listener error:', error);
      }
    });
  }

  /**
   * Validate current session with server
   */
  async validateSession(): Promise<boolean> {
    try {
      const response = await apiClient.get('/api/auth/admin/session');
      
      if (response.data.success && response.data.valid) {
        this.updateActivity();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  }

  /**
   * Start session monitoring
   */
  startSession(): void {
    this.isSessionActive = true;
    this.sessionStartTime = new Date();
    this.updateActivity();
    this.initialize();
  }

  /**
   * End session and cleanup
   */
  endSession(): void {
    this.expireSession('logout');
  }

  /**
   * Subscribe to session expiration events
   */
  onSessionExpired(callback: () => void): () => void {
    const listener = (status: SessionStatus) => {
      if (!status.isActive) {
        callback();
      }
    };
    
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Clean up session manager
   */
  destroy(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
    this.listeners = [];
    this.isSessionActive = false;
  }
}

// Export singleton instance
export const sessionManager = new SessionManagerImpl();

// Auto-initialize if we have a token (only in browser)
if (isBrowser()) {
  const hasToken = tokenManager.isAuthenticated('admin') || tokenManager.isAuthenticated('user');
  if (hasToken) {
    sessionManager.initialize();
  }
}

export default sessionManager;
