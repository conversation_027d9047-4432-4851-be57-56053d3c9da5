import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { apiClient } from '../../utils/axiosClient';
import AdminLayout from '../../components/admin/AdminLayout';

interface User {
  _id: string;
  username: string;
  email: string;
  isActive: boolean;
  lastLogin?: Date;
  role: string;
  createdAt: Date;
}

export default function Users() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const router = useRouter();
  useEffect(() => {
    // Check if admin is logged in and fetch users
    const fetchUsers = async () => {
      try {
        // First check session
        await apiClient.frontend.get('/api/auth/admin/session');

        // Then fetch users
        const response = await apiClient.frontend.get('/api/users');
        setUsers(response.data.users || []);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'An error occurred');
        // Redirect to login page if not authenticated
        if (err.response?.status === 401) {
          router.push('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [router]);

  const handleAddUser = () => {
    router.push('/users/new');
  };

  const handleEditUser = (userId: string) => {
    router.push(`/users/${userId}`);
  };
  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      await apiClient.frontend.delete(`/api/users/${userId}`);
      // Remove user from list
      setUsers(users.filter(user => user._id !== userId));
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to delete user');
    }
  };
  if (loading) {
    return (
      <AdminLayout>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading users...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Users - CCALC Admin</title>
      </Head>
      <div className="main-area">
        <div className="page-header">
          <h2 className="page-title">User Management</h2>
          <button className="btn-primary" onClick={handleAddUser}>
            Add User
          </button>
        </div>
        {error && (
          <div className="alert alert-error">
            {error}
          </div>
        )}
        <div className="card">
          <div className="table-container">
            <table className="table">
              <thead>
                <tr>
                  <th>Username</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th>Last Login</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="no-data">No users found</td>
                  </tr>
                ) : (
                  users.map(user => (
                    <tr key={user._id}>
                      <td className="font-medium">{user.username}</td>
                      <td>{user.email}</td>
                      <td>
                        <span className="badge badge-secondary">{user.role}</span>
                      </td>
                      <td>
                        <span className={`badge ${user.isActive ? 'badge-success' : 'badge-warning'}`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td>{new Date(user.createdAt).toLocaleDateString()}</td>
                      <td>{user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</td>
                      <td>
                        <div className="action-buttons">
                          <button 
                            onClick={() => handleEditUser(user._id)} 
                            className="btn-small btn-outline"
                          >
                            Edit
                          </button>
                          <button 
                            onClick={() => handleDeleteUser(user._id)} 
                            className="btn-small btn-danger"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <style jsx>{`
        .main-area {
          max-width: 1200px;
          margin: 0 auto;
          padding: 2rem;
        }

        .page-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .page-title {
          font-size: 1.875rem;
          font-weight: 600;
          color: #111827;
          margin: 0;
        }

        .btn-primary {
          background: #2563eb;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .btn-primary:hover {
          background: #1d4ed8;
        }

        .alert {
          padding: 1rem;
          border-radius: 0.5rem;
          margin-bottom: 1.5rem;
        }

        .alert-error {
          background: #fef2f2;
          border: 1px solid #fecaca;
          color: #dc2626;
        }

        .card {
          background: white;
          border-radius: 0.75rem;
          border: 1px solid #e5e7eb;
          overflow: hidden;
        }

        .table-container {
          overflow-x: auto;
        }

        .table {
          width: 100%;
          border-collapse: collapse;
        }

        .table th {
          background: #f9fafb;
          padding: 1rem;
          text-align: left;
          font-weight: 600;
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
        }

        .table td {
          padding: 1rem;
          border-bottom: 1px solid #f3f4f6;
          color: #6b7280;
        }

        .table tbody tr:hover {
          background: #f9fafb;
        }

        .font-medium {
          font-weight: 500;
          color: #111827;
        }

        .badge {
          display: inline-block;
          padding: 0.25rem 0.75rem;
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: 9999px;
        }

        .badge-success {
          background: #dcfce7;
          color: #166534;
        }

        .badge-warning {
          background: #fef3c7;
          color: #92400e;
        }

        .badge-secondary {
          background: #f3f4f6;
          color: #374151;
        }

        .action-buttons {
          display: flex;
          gap: 0.5rem;
        }

        .btn-small {
          padding: 0.375rem 0.75rem;
          font-size: 0.875rem;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .btn-outline {
          background: white;
          color: #374151;
          border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }

        .btn-danger {
          background: #dc2626;
          color: white;
          border: 1px solid #dc2626;
        }

        .btn-danger:hover {
          background: #b91c1c;
          border-color: #b91c1c;
        }

        .no-data {
          text-align: center;
          color: #9ca3af;
          font-style: italic;
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background: #fafafa;
        }

        .loading-spinner {
          width: 2rem;
          height: 2rem;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #2563eb;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }

        .loading-container p {
          color: #6b7280;
          font-weight: 500;
        }
      `}</style>
    </AdminLayout>
  );
}
