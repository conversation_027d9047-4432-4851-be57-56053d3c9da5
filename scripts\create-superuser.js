/**
 * Create Superuser for Chat System
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// User schema definition
const userSchema = new mongoose.Schema({
  username: String,
  email: String,
  expressionHash: String,
  unlockExpression: String,
  expressionType: { type: String, default: 'calculator' },
  profile: {
    displayName: String
  },
  deviceFingerprintHash: String,
  bleUUIDHash: String,
  status: { type: String, default: 'active' },
  isSuperuser: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

async function createSuperuser() {
  try {
    const mongoUri = '*************************************************************';
    
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    });

    console.log('Connected to MongoDB successfully');

    // Check if superuser already exists
    const existingSuperuser = await User.findOne({ isSuperuser: true });
    
    if (existingSuperuser) {
      console.log(`✅ Superuser already exists: ${existingSuperuser.username} (${existingSuperuser.profile?.displayName})`);
      console.log(`   Expression: ${existingSuperuser.unlockExpression}`);
      return;
    }

    // Create superuser
    const superuserExpression = '9+1*2';
    const expressionHash = await bcrypt.hash(superuserExpression, 10);
    
    const superuser = new User({
      username: 'superuser',
      email: '<EMAIL>',
      expressionHash: expressionHash,
      unlockExpression: superuserExpression,
      expressionType: 'calculator',
      profile: {
        displayName: 'Administrator'
      },
      status: 'active',
      isSuperuser: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await superuser.save();
    
    console.log('✅ Superuser created successfully!');
    console.log('================================');
    console.log(`Username: ${superuser.username}`);
    console.log(`Display Name: ${superuser.profile.displayName}`);
    console.log(`Expression: ${superuser.unlockExpression}`);
    console.log(`Status: ${superuser.status}`);
    console.log(`Is Superuser: ${superuser.isSuperuser}`);
    console.log('================================');
    console.log('');
    console.log('💡 To test:');
    console.log(`1. Use expression: ${superuser.unlockExpression}`);
    console.log('2. Login from mobile app');
    console.log('3. You should see all users in chat list');

  } catch (error) {
    console.error('Error creating superuser:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

createSuperuser();
