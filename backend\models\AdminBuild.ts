import mongoose, { Document, Schema } from 'mongoose';

// Admin Build Management model - for general app releases
export interface IAdminBuild extends Document {
  version: string;
  releaseDate?: Date;
  status: 'active' | 'archived' | 'draft';
  description: string;
  platform: 'web' | 'ios' | 'android' | 'desktop';
  downloadUrl?: string;
  changelog?: string;
  fileSize?: number;
  fileName?: string;
  downloads?: number;
  createdBy: mongoose.Types.ObjectId;
  updatedBy?: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const AdminBuildSchema = new Schema<IAdminBuild>({
  version: {
    type: String,
    required: true,
    trim: true
  },
  releaseDate: {
    type: Date,
    default: null
  },
  status: {
    type: String,
    enum: ['active', 'archived', 'draft'],
    default: 'draft',
    required: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  platform: {
    type: String,
    enum: ['web', 'ios', 'android', 'desktop'],
    required: true
  },
  downloadUrl: {
    type: String,
    default: null
  },
  changelog: {
    type: String,
    default: ''
  },
  fileSize: {
    type: Number,
    default: 0
  },
  fileName: {
    type: String,
    default: null
  },
  downloads: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
    default: null
  }
}, {
  timestamps: true
});

// Indexes
AdminBuildSchema.index({ version: 1, platform: 1 }, { unique: true });
AdminBuildSchema.index({ status: 1 });
AdminBuildSchema.index({ platform: 1 });
AdminBuildSchema.index({ createdAt: -1 });

export default mongoose.model<IAdminBuild>('AdminBuild', AdminBuildSchema);
