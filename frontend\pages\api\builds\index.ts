import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get admin token from cookies
    const cookies = parse(req.headers.cookie || '');
    const token = cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Query parameters
    const { userId, status, limit } = req.query;
    
    // Construct query string
    const queryParams = new URLSearchParams();
    if (userId) queryParams.append('userId', userId as string);
    if (status) queryParams.append('status', status as string);
    if (limit) queryParams.append('limit', limit as string);
    
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';

    // Forward request to backend
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/builds${queryString}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    return res.status(response.status).json(data);
  } catch (error) {
    console.error('Builds listing error:', error);
    return res.status(500).json({ error: 'Failed to fetch builds' });
  }
}
