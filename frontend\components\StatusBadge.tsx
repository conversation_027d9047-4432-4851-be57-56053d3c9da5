interface StatusBadgeProps {
  status: string;
  text?: string;
}

export default function StatusBadge({ status, text }: StatusBadgeProps) {
  const getStatusColor = (status: string) => {
    const statusLower = status.toLowerCase();
    
    if (statusLower === 'active' || statusLower === 'online' || statusLower === 'released' || statusLower === 'success') {
      return '#52c41a'; // green
    } else if (statusLower === 'pending' || statusLower === 'beta' || statusLower === 'processing') {
      return '#1890ff'; // blue
    } else if (statusLower === 'warning' || statusLower === 'alpha' || statusLower === 'review') {
      return '#faad14'; // yellow
    } else if (statusLower === 'development' || statusLower === 'staging') {
      return '#fa8c16'; // orange
    } else if (statusLower === 'inactive' || statusLower === 'offline' || statusLower === 'error' || statusLower === 'deprecated') {
      return '#ff4d4f'; // red
    } else {
      return '#8c8c8c'; // grey
    }
  };

  return (
    <span 
      className="status-badge"
      style={{ backgroundColor: getStatusColor(status) }}
    >
      {text || status}
      
      <style jsx>{`
        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 4px;
          color: white;
          font-size: 12px;
          font-weight: 500;
        }
      `}</style>
    </span>
  );
}
