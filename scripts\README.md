# CCALC Admin Key Management Scripts

This directory contains scripts for managing admin keys and authentication in the CCALC system.

## All-in-One Setup

- **setup-admin-ppk.sh** - One-step script to generate keys and update the database
  ```bash
  ./scripts/setup-admin-ppk.sh [username] [email]
  ```

## Key Generation Scripts

- **generate-admin-keys.js** - Recommended script for generating admin keys with comprehensive options
  ```bash
  node scripts/generate-admin-keys.js [username] [email]
  ```

- **generate-simple-keys.js** - Simple key generation without database connection
  ```bash
  node scripts/generate-simple-keys.js [username]
  ```

- **generate-keys.js** - Legacy key generation script with additional options
  ```bash
  node scripts/generate-keys.js [username] [email]
  ```

## Database Management Scripts

- **update-admin-ppk.js** - Update an admin's PPK public key in MongoDB
  ```bash
  node scripts/update-admin-ppk.js [username] [formatted-key-file-path]
  ```

- **setup-admin.js** - Set up a new admin user with PPK authentication (creates user and keys)
  ```bash
  node scripts/setup-admin.js
  ```

## Key Usage

After generating keys and updating the database, the admin can log in using:
1. Username and password
2. PPK private key authentication

For more information, see the [Admin Key Setup Guide](../docs/admin-key-setup.md).

## Security Notes

- Private keys should be kept secure
- For production environments, always use encrypted private keys
- Implement key rotation policies for enhanced security
- The system supports both password-only and PPK authentication methods

## Troubleshooting

If you encounter issues with key generation or authentication, refer to the [PPK Troubleshooting Guide](../docs/ppk-troubleshooting.md).
