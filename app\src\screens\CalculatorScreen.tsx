/**
 * Calculator Screen Component
 * Modern iOS calculator design with hidden authentication flow
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  SafeAreaView,
  Vibration,
} from 'react-native';
// Safe haptics import with fallback for Expo Go
let Haptics: any = null;
try {
  Haptics = require('expo-haptics');
} catch (error) {
  console.log('📱 expo-haptics not available, using fallback implementation');
  Haptics = {
    impactAsync: () => Promise.resolve(),
    ImpactFeedbackStyle: {
      Light: 'light',
      Medium: 'medium',
      Heavy: 'heavy'
    }
  };
}
import { theme } from '../utils/theme';
import { AuthService } from '../services/AuthService';

interface CalculatorScreenProps {
  onAuthenticationSuccess: () => void;
}

const { width: screenWidth } = Dimensions.get('window');
const buttonSize = (screenWidth - 5 * theme.spacing.md) / 4;

export const CalculatorScreen: React.FC<CalculatorScreenProps> = ({
  onAuthenticationSuccess,
}) => {
  const [display, setDisplay] = useState('0');
  const [previousValue, setPreviousValue] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [expression, setExpression] = useState('');
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const authService = AuthService.getInstance();
  // Haptic feedback function
  const hapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    switch (type) {
      case 'light':
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
      case 'medium':
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case 'heavy':
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
        break;
    }
  }, []);

  // Handle number input
  const inputNumber = useCallback((num: string) => {
    hapticFeedback('light');
    
    if (waitingForOperand) {
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? num : display + num);
    }
    
    // Update expression for potential authentication
    const newExpression = waitingForOperand 
      ? expression + num 
      : (display === '0' ? expression + num : expression + num);
    setExpression(newExpression);
  }, [display, waitingForOperand, expression, hapticFeedback]);

  // Handle decimal point
  const inputDecimal = useCallback(() => {
    hapticFeedback('light');
    
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
      setExpression(expression + '0.');
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
      setExpression(expression + '.');
    }
  }, [display, waitingForOperand, expression, hapticFeedback]);

  // Handle operations
  const inputOperation = useCallback((nextOperation: string) => {
    hapticFeedback('medium');
    
    const inputValue = parseFloat(display);
    const operatorSymbol = {
      '+': '+',
      '-': '-',
      '*': '×',
      '/': '÷',
    }[nextOperation] || nextOperation;

    if (previousValue === null) {
      setPreviousValue(inputValue);
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);
      
      setDisplay(String(newValue));
      setPreviousValue(newValue);
    }

    setWaitingForOperand(true);
    setOperation(nextOperation);
    
    // Update expression with the visual operator
    setExpression(expression + ` ${operatorSymbol} `);
  }, [display, previousValue, operation, expression, hapticFeedback]);

  // Calculate result
  const calculate = useCallback((firstValue: number, secondValue: number, operation: string): number => {
    switch (operation) {
      case '+':
        return firstValue + secondValue;
      case '-':
        return firstValue - secondValue;
      case '*':
        return firstValue * secondValue;
      case '/':
        return secondValue !== 0 ? firstValue / secondValue : 0;
      default:
        return secondValue;
    }
  }, []);

  // Handle equals - This is where authentication happens
  const performCalculation = useCallback(async () => {
    hapticFeedback('heavy');
    setIsAuthenticating(true);

    try {
      const inputValue = parseFloat(display);
      
      // Build complete expression for authentication
      let completeExpression = expression;
      if (!waitingForOperand && previousValue !== null && operation) {
        // Convert visual operators back to mathematical operators
        const mathExpression = expression
          .replace(/×/g, '*')
          .replace(/÷/g, '/');
        completeExpression = mathExpression;
      } else if (display && display !== '0') {
        completeExpression = display;
      }

      console.log('🧮 Performing calculation for expression:', completeExpression);

      // First, try authentication with the expression
      const authResult = await authService.authenticateExpression(completeExpression);
      
      if (authResult.success && authResult.shouldTransitionToChat) {
        // Authentication successful - transition to chat
        console.log('🎉 Authentication successful - transitioning to chat');
        hapticFeedback('heavy');
        onAuthenticationSuccess();
        return;
      }

      // Authentication failed or expression invalid - perform normal calculation
      if (previousValue !== null && operation) {
        const newValue = calculate(previousValue, inputValue, operation);
        const result = (newValue % 1 === 0) ? newValue.toString() : newValue.toFixed(8).replace(/\.?0+$/, '');
        
        setDisplay(result);
        setPreviousValue(null);
        setOperation(null);
        setWaitingForOperand(true);
        setExpression(result);
      }
      
    } catch (error) {
      console.error('Calculation error:', error);
      setDisplay('Error');
      setExpression('Error');
    } finally {
      setIsAuthenticating(false);
    }
  }, [
    display, 
    expression, 
    waitingForOperand, 
    previousValue, 
    operation, 
    authService, 
    calculate, 
    hapticFeedback, 
    onAuthenticationSuccess
  ]);

  // Clear display
  const clear = useCallback(() => {
    hapticFeedback('medium');
    setDisplay('0');
    setPreviousValue(null);
    setOperation(null);
    setWaitingForOperand(false);
    setExpression('');
  }, [hapticFeedback]);

  // Delete last character
  const deleteLastCharacter = useCallback(() => {
    hapticFeedback('light');
    
    if (display.length > 1) {
      setDisplay(display.slice(0, -1));
      setExpression(expression.slice(0, -1));
    } else {
      setDisplay('0');
      setExpression('');
    }
  }, [display, expression, hapticFeedback]);

  // Button component
  const CalculatorButton: React.FC<{
    title: string;
    onPress: () => void;
    type?: 'number' | 'operator' | 'special';
    size?: 'normal' | 'wide';
  }> = ({ title, onPress, type = 'number', size = 'normal' }) => {
    const buttonStyle = [
      styles.button,
      size === 'wide' && styles.buttonWide,
      type === 'operator' && styles.operatorButton,
      type === 'special' && styles.specialButton,
    ];

    const textStyle = [
      styles.buttonText,
      type === 'operator' && styles.operatorText,
      type === 'special' && styles.specialText,
    ];

    return (
      <TouchableOpacity
        style={buttonStyle}
        onPress={onPress}
        activeOpacity={0.8}
      >
        <Text style={textStyle}>{title}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={theme.colors.calculatorBackground} />
      
      {/* Display */}
      <View style={styles.displayContainer}>
        <Text style={styles.display} numberOfLines={1} adjustsFontSizeToFit>
          {isAuthenticating ? 'Authenticating...' : display}
        </Text>
        {expression && expression !== display && (
          <Text style={styles.expression} numberOfLines={1}>
            {expression}
          </Text>
        )}
      </View>

      {/* Button Grid */}
      <View style={styles.buttonContainer}>
        {/* Row 1 */}
        <View style={styles.buttonRow}>
          <CalculatorButton title="C" onPress={clear} type="special" />
          <CalculatorButton title="⌫" onPress={deleteLastCharacter} type="special" />
          <CalculatorButton title="±" onPress={() => {}} type="special" />
          <CalculatorButton title="÷" onPress={() => inputOperation('/')} type="operator" />
        </View>

        {/* Row 2 */}
        <View style={styles.buttonRow}>
          <CalculatorButton title="7" onPress={() => inputNumber('7')} />
          <CalculatorButton title="8" onPress={() => inputNumber('8')} />
          <CalculatorButton title="9" onPress={() => inputNumber('9')} />
          <CalculatorButton title="×" onPress={() => inputOperation('*')} type="operator" />
        </View>

        {/* Row 3 */}
        <View style={styles.buttonRow}>
          <CalculatorButton title="4" onPress={() => inputNumber('4')} />
          <CalculatorButton title="5" onPress={() => inputNumber('5')} />
          <CalculatorButton title="6" onPress={() => inputNumber('6')} />
          <CalculatorButton title="−" onPress={() => inputOperation('-')} type="operator" />
        </View>

        {/* Row 4 */}
        <View style={styles.buttonRow}>
          <CalculatorButton title="1" onPress={() => inputNumber('1')} />
          <CalculatorButton title="2" onPress={() => inputNumber('2')} />
          <CalculatorButton title="3" onPress={() => inputNumber('3')} />
          <CalculatorButton title="+" onPress={() => inputOperation('+')} type="operator" />
        </View>

        {/* Row 5 */}
        <View style={styles.buttonRow}>
          <CalculatorButton title="0" onPress={() => inputNumber('0')} size="wide" />
          <CalculatorButton title="." onPress={inputDecimal} />
          <CalculatorButton title="=" onPress={performCalculation} type="operator" />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.calculatorBackground,
  },
  displayContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  display: {
    fontSize: theme.typography.sizes.calculatorDisplay,
    fontWeight: theme.typography.weights.thin,
    color: theme.colors.calculatorDisplay,
    fontFamily: theme.typography.families.system,
    textAlign: 'right',
  },
  expression: {
    fontSize: theme.typography.sizes.title1,
    fontWeight: theme.typography.weights.light,
    color: theme.colors.systemGray,
    fontFamily: theme.typography.families.system,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },
  buttonContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.lg,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  button: {
    width: buttonSize,
    height: buttonSize,
    borderRadius: buttonSize / 2,
    backgroundColor: theme.colors.calculatorButton,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.button,
  },
  buttonWide: {
    width: buttonSize * 2 + theme.spacing.md,
    borderRadius: buttonSize / 2,
    paddingLeft: theme.spacing.lg,
    alignItems: 'flex-start',
  },
  operatorButton: {
    backgroundColor: theme.colors.calculatorOperator,
  },
  specialButton: {
    backgroundColor: theme.colors.systemGray3,
  },
  buttonText: {
    fontSize: theme.typography.sizes.calculatorButton,
    fontWeight: theme.typography.weights.regular,
    color: theme.colors.calculatorDisplay,
    fontFamily: theme.typography.families.system,
  },
  operatorText: {
    color: theme.colors.calculatorDisplay,
    fontWeight: theme.typography.weights.medium,
  },
  specialText: {
    color: theme.colors.label,
    fontWeight: theme.typography.weights.medium,
  },
});

export default CalculatorScreen;
