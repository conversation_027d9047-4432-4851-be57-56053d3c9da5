import asyncio
from bleak import B<PERSON>k<PERSON>can<PERSON>, BleakClient
import requests
import json
import uuid

BACKEND_API_URL = "http://localhost:3000/api/admin/users/{user_id}/add-ble-device"  # Update as needed
CUSTOM_SERVICE_UUID = "0000c0de-0000-1000-8000-00805f9b34fb"
CUSTOM_CHAR_UUID = "0000beef-0000-1000-8000-00805f9b34fb"

async def scan_and_register(user_id):
    print("Scanning for BLE devices...")
    devices = await BleakScanner.discover(timeout=5.0)
    for idx, d in enumerate(devices):
        print(f"[{idx}] {d.name} ({d.address}) RSSI={d.rssi}")
    if not devices:
        print("No BLE devices found.")
        return
    idx = int(input("Select device index to register: "))
    device = devices[idx]
    print(f"Connecting to {device.name} ({device.address})...")
    async with BleakClient(device.address) as client:
        ad_data = device.metadata
        services = await client.get_services()
        characteristics = {}
        for service in services:
            for char in service.characteristics:
                try:
                    value = await client.read_gatt_char(char.uuid)
                    characteristics[char.uuid] = value.hex()
                except Exception:
                    characteristics[char.uuid] = None
        # --- Custom signature write ---
        signature = str(uuid.uuid4())
        print(f"Writing unique signature to custom characteristic: {signature}")
        try:
            await client.write_gatt_char(CUSTOM_CHAR_UUID, signature.encode('utf-8'), response=True)
            print("Signature written successfully.")
        except Exception as e:
            print(f"Failed to write signature to characteristic {CUSTOM_CHAR_UUID}: {e}")
        ble_data = {
            "deviceId": device.address,
            "deviceName": device.name,
            "adData": ad_data,
            "characteristics": characteristics,
            "pairedAt": str(device.details.get('timestamp', '')),
            "isVerified": True,
            "signature": signature,
            "signatureCharUuid": CUSTOM_CHAR_UUID
        }
        print("Captured BLE data:", json.dumps(ble_data, indent=2))
        # Send to backend
        url = BACKEND_API_URL.format(user_id=user_id)
        resp = requests.post(url, json=ble_data)
        print("Backend response:", resp.status_code, resp.text)

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python ble_register.py <user_id>")
        sys.exit(1)
    user_id = sys.argv[1]
    asyncio.run(scan_and_register(user_id))
