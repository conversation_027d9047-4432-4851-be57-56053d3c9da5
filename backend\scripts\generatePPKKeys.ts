#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate PPK keys for admin authentication
 * Usage: 
 *   npm run generate-admin-keys
 *   npm run setup-dev-keys
 *   npm run setup-prod-keys
 */

import path from 'path';
import { generatePPKKeyPair, saveKeyPair } from '../utils/ppk';
import { promises as fs } from 'fs';
import readline from 'readline';
import { promisify } from 'util';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = promisify(rl.question).bind(rl);

interface Options {
  encrypted: boolean;
  production: boolean;
}

async function parseArgs(): Promise<[string, Options]> {
  const args = process.argv.slice(2);
  const username = args[0];
  const options: Options = {
    encrypted: args.includes('--encrypted'),
    production: args.includes('--production')
  };

  if (!username) {
    throw new Error('Username is required');
  }

  return [username, options];
}

async function getPassphrase(): Promise<string> {
  const passphrase = await question('Enter passphrase for key encryption: ');
  const confirm = await question('Confirm passphrase: ');

  if (passphrase !== confirm) {
    throw new Error('Passphrases do not match');
  }

  return passphrase;
}

async function main() {
  try {
    const [username, options] = await parseArgs();
    
    // Create keys directory if it doesn't exist
    const keysDir = path.resolve(__dirname, '../../keys');
    await fs.mkdir(keysDir, { recursive: true });

    let passphrase: string | undefined;
    if (options.encrypted) {
      passphrase = await getPassphrase();
    }

    // Generate key pair
    console.log('Generating PPK key pair...');
    const keyPair = generatePPKKeyPair(options.encrypted, passphrase);

    // Save keys with timestamp for versioning
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const prefix = options.production ? 'prod' : 'dev';
    const publicKeyPath = path.join(keysDir, `${prefix}_${username}_public_${timestamp}.pem`);
    const privateKeyPath = path.join(keysDir, `${prefix}_${username}_private_${timestamp}.pem`);

    await saveKeyPair(keyPair, publicKeyPath, privateKeyPath);

    // Create key info file
    const keyInfo = {
      username,
      generatedAt: new Date().toISOString(),
      encrypted: options.encrypted,
      environment: options.production ? 'production' : 'development',
      publicKeyPath: path.relative(process.cwd(), publicKeyPath),
      privateKeyPath: path.relative(process.cwd(), privateKeyPath),
    };

    const infoPath = path.join(keysDir, `${prefix}_${username}_keys_info_${timestamp}.json`);
    await fs.writeFile(infoPath, JSON.stringify(keyInfo, null, 2));

    console.log('\nPPK keys generated successfully:');
    console.log(`Public key: ${publicKeyPath}`);
    console.log(`Private key: ${privateKeyPath}`);
    console.log(`Key info: ${infoPath}`);
    
    if (options.production) {
      console.log('\nIMPORTANT: Store the private key and passphrase securely!');
    }

  } catch (error) {
    console.error('Error generating PPK keys:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  } finally {
    rl.close();
  }
}

main();
