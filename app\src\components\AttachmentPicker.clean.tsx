/**
 * Clean & Minimal Attachment Picker
 * Gallery, Documents, Audio Files only (NO voice notes)
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as Haptics from 'expo-haptics';
import { theme } from '../utils/theme';

export interface AttachmentOption {
  id: string;
  title: string;
  color: string;
}

export interface AttachmentPickerProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (attachment: any) => void;
}

const { width } = Dimensions.get('window');

export const AttachmentPicker: React.FC<AttachmentPickerProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  const slideAnim = React.useRef(new Animated.Value(300)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 65,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Clean, minimal options - NO voice notes
  const attachmentOptions: AttachmentOption[] = [
    {
      id: 'gallery',
      title: 'Photos & Videos',
      color: '#007AFF',
    },
    {
      id: 'document',
      title: 'Documents',
      color: '#34C759',
    },
    {
      id: 'audio',
      title: 'Audio Files',
      color: '#FF9500',
    },
  ];

  const handleOptionPress = async (option: AttachmentOption) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    try {
      let result;

      switch (option.id) {
        case 'gallery':
          const libraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (!libraryPermission.granted) {
            alert('Gallery permission is required');
            return;
          }

          result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ['images', 'videos'],
            allowsEditing: false,
            quality: 0.8,
            allowsMultipleSelection: false,
          });
          break;

        case 'document':
          result = await DocumentPicker.getDocumentAsync({
            type: [
              'application/pdf',
              'application/msword',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
              'application/vnd.ms-excel',
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              'text/plain',
              'text/csv',
            ],
            copyToCacheDirectory: true,
            multiple: false,
          });
          break;

        case 'audio':
          // Audio FILES only - NO voice note recording
          result = await DocumentPicker.getDocumentAsync({
            type: [
              'audio/mpeg',
              'audio/wav',
              'audio/mp4',
              'audio/m4a',
              'audio/aac',
            ],
            copyToCacheDirectory: true,
            multiple: false,
          });
          break;

        default:
          return;
      }

      if (result && !result.canceled) {
        let attachment;

        if ('assets' in result && result.assets && result.assets.length > 0) {
          const asset = result.assets[0];
          const isImagePickerResult = 'width' in asset && 'height' in asset;
          
          if (isImagePickerResult) {
            const fileName = asset.uri.split('/').pop() || 'media_file';
            const isVideo = asset.type === 'video' || asset.uri.includes('.mp4') || asset.uri.includes('.mov');
            
            attachment = {
              id: `attachment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              name: fileName,
              type: isVideo ? 'Video' : 'Image',
              size: asset.fileSize || 0,
              uri: asset.uri,
              mimeType: isVideo ? 'video/mp4' : 'image/jpeg',
              isImage: !isVideo,
              isVideo: isVideo,
              isAudio: false,
              width: asset.width,
              height: asset.height,
              duration: asset.duration || 0,
            };
          } else {
            attachment = {
              id: `attachment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              name: (asset as any).name || 'unknown_file',
              type: getFileCategory((asset as any).mimeType || ''),
              size: (asset as any).size || 0,
              uri: asset.uri,
              mimeType: (asset as any).mimeType,
              isImage: (asset as any).mimeType?.startsWith('image/'),
              isVideo: (asset as any).mimeType?.startsWith('video/'),
              isAudio: (asset as any).mimeType?.startsWith('audio/'),
            };
          }
        }

        if (attachment) {
          onSelect(attachment);
          onClose();
        }
      }
    } catch (error) {
      console.error('Attachment picker error:', error);
      alert('Failed to select attachment');
    }
  };

  const getFileCategory = (mimeType: string): string => {
    if (mimeType.startsWith('application/pdf')) return 'PDF';
    if (mimeType.startsWith('application/msword') || 
        mimeType.includes('wordprocessingml')) return 'Document';
    if (mimeType.startsWith('application/vnd.ms-excel') || 
        mimeType.includes('spreadsheetml')) return 'Spreadsheet';
    if (mimeType.startsWith('text/')) return 'Text File';
    if (mimeType.startsWith('audio/')) return 'Audio';
    if (mimeType.startsWith('video/')) return 'Video';
    if (mimeType.startsWith('image/')) return 'Image';
    return 'File';
  };

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        <Animated.View 
          style={[
            styles.container,
            { transform: [{ translateY: slideAnim }] }
          ]}
        >
          <View style={styles.handle} />
          
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Share</Text>
          </View>

          <View style={styles.content}>
            {attachmentOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[styles.option, { borderLeftColor: option.color }]}
                onPress={() => handleOptionPress(option)}
                activeOpacity={0.7}
              >
                <Text style={styles.optionTitle}>{option.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  container: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 20,
  },
  handle: {
    width: 32,
    height: 4,
    backgroundColor: '#C7C7CC',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: theme.colors.label,
    textAlign: 'center',
  },
  content: {
    paddingHorizontal: 20,
    paddingTop: 12,
  },
  option: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderLeftWidth: 4,
    marginBottom: 8,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.label,
  },
});
