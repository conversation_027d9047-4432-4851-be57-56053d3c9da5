import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../backend/config/database';
import Device from '../backend/models/Device';

// GET /api/devices - List all devices
// GET /api/devices?userId=... - List devices for a specific user
// GET /api/devices/fingerprint/:deviceId - Get fingerprint metadata for a device
// DELETE /api/devices/:deviceId - Revoke and deauthorize a device

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();
  const { method, query } = req;

  if (method === 'GET') {
    if (req.url?.startsWith('/api/devices/fingerprint/')) {
      // Get fingerprint metadata for a device
      const deviceId = req.query.deviceId || req.url.split('/').pop();
      if (!deviceId) res.status(400).json({ error: 'deviceId required' });
      const device = await Device.findOne({ deviceId });
      if (!device) res.status(404).json({ error: 'Device not found' });
      res.status(200).json({
        deviceId: device.deviceId,
        fingerprint: device.fingerprint,
        metadata: device.metadata,
        userId: device.userId,
        isActive: device.isActive,
        createdAt: device.createdAt,
        updatedAt: device.updatedAt
      });
    }
    // List all devices or by user
    const filter: any = {};
    if (query.userId) filter.userId = query.userId;
    const devices = await Device.find(filter).lean();
    res.status(200).json(devices.map(d => ({
      deviceId: d.deviceId,
      userId: d.userId,
      fingerprint: d.fingerprint,
      ble: d.ble,
      metadata: d.metadata,
      security: d.security,
      network: d.network,
      isActive: d.isActive,
      createdAt: d.createdAt,
      updatedAt: d.updatedAt
    })));
  }

  if (method === 'DELETE') {
    // Revoke and deauthorize a device
    const { deviceId } = query;
    if (!deviceId) res.status(400).json({ error: 'deviceId required' });
    const device = await Device.findOne({ deviceId });
    if (!device) res.status(404).json({ error: 'Device not found' });
    device.isActive = false;
    await device.save();
    res.status(200).json({ success: true });
  }

  res.setHeader('Allow', ['GET', 'DELETE']);
  res.status(405).end(`Method ${method} Not Allowed`);
}
