/**
 * PPK (Personal Public Key) Utilities for backend
 * 
 * This file implements utilities for PPK key pair generation, signature creation, and verification.
 * Used for secure admin authentication in the CCALC system.
 */

import crypto from 'crypto';
import fs from 'fs';

interface PPKKeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * Generate a new PPK key pair for admin authentication
 * @param encrypted Whether to encrypt the private key
 * @param passphrase Passphrase for encrypting the private key (required if encrypted is true)
 * @returns Object containing public and private keys in PEM format
 */
export function generatePPKKeyPair(encrypted = true, passphrase?: string): PPKKeyPair {
  if (encrypted && !passphrase) {
    throw new Error('Passphrase is required for encrypted keys');
  }

  const options: crypto.RSAKeyPairOptions<'pem', 'pem'> = {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem',
    }
  };

  if (encrypted) {
    options.privateKeyEncoding.cipher = 'aes-256-cbc';
    options.privateKeyEncoding.passphrase = passphrase;
  }

  try {
    return crypto.generateKeyPairSync('rsa', options);
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error generating key pair';
    throw new Error(`Failed to generate key pair: ${message}`);
  }
}

/**
 * Generate a challenge for PPK authentication
 * @returns A random challenge string
 */
export function generateChallenge(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Create a signature for a challenge using the private key
 * @param challenge The challenge to sign
 * @param privateKey The private key in PEM format
 * @param passphrase Optional passphrase for encrypted private key
 * @returns The signature as a base64 string
 */
export function signChallenge(challenge: string, privateKey: string, passphrase?: string): string {
  try {
    const sign = crypto.createSign('SHA256');
    sign.update(challenge);
    sign.end();
    return sign.sign({ 
      key: privateKey, 
      passphrase: passphrase || undefined 
    }, 'base64');
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error signing challenge';
    throw new Error(`Failed to sign challenge: ${message}`);
  }
}

/**
 * Verify a signature against a challenge using the public key
 * @param challenge The challenge that was signed
 * @param signature The signature to verify
 * @param publicKey The public key in PEM format
 * @returns boolean indicating if the signature is valid
 */
export function verifySignature(challenge: string, signature: string, publicKey: string): boolean {
  try {
    console.log('[DEBUG PPK] Verifying signature:', {
      challengeLength: challenge?.length,
      signatureLength: signature?.length,
      publicKeyStart: publicKey?.substring(0, 40),
      challengeStart: challenge?.substring(0, 20)
    });
    
    if (!challenge || !signature || !publicKey) {
      console.error('[DEBUG PPK] Missing required parameters for verification');
      return false;
    }

    // Ensure we have valid PEM format
    if (!publicKey.includes('-----BEGIN') || !publicKey.includes('-----END')) {
      console.error('[DEBUG PPK] Invalid PEM format for public key');
      return false;
    }
    
    const verify = crypto.createVerify('SHA256');
    verify.update(challenge);
    verify.end();
    
    const result = verify.verify(publicKey, signature, 'base64');
    console.log(`[DEBUG PPK] Verification result: ${result ? 'VALID' : 'INVALID'}`);
    
    if (!result) {
      console.error('[DEBUG PPK] Signature verification failed - possible causes:');
      console.error('  - Wrong private key used for signing');
      console.error('  - Algorithm mismatch between frontend and backend');
      console.error('  - Challenge/signature corruption during transmission');
      console.error('  - Public key mismatch');
    }
    
    return result;
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error verifying signature';
    console.error('[DEBUG PPK] Verification error:', error);
    console.error('[DEBUG PPK] Error details:', { 
      message, 
      challengeType: typeof challenge,
      signatureType: typeof signature,
      publicKeyType: typeof publicKey
    });
    throw new Error(`Failed to verify signature: ${message}`);
  }
}

/**
 * Save a PPK key pair to files
 * @param keyPair The key pair to save
 * @param publicKeyPath Path to save the public key
 * @param privateKeyPath Path to save the private key
 */
export async function saveKeyPair(keyPair: PPKKeyPair, publicKeyPath: string, privateKeyPath: string): Promise<void> {
  try {
    await fs.promises.writeFile(publicKeyPath, keyPair.publicKey);
    await fs.promises.writeFile(privateKeyPath, keyPair.privateKey);
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error saving key pair';
    throw new Error(`Failed to save key pair: ${message}`);
  }
}

/**
 * Load a public key from a file
 * @param publicKeyPath Path to the public key file
 * @returns The public key as a string
 */
export function loadPublicKey(publicKeyPath: string): string {
  try {
    return fs.readFileSync(publicKeyPath, 'utf8');
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error loading public key';
    throw new Error(`Failed to load public key: ${message}`);
  }
}

/**
 * Load a private key from a file
 * @param privateKeyPath Path to the private key file
 * @returns The private key as a string
 */
export function loadPrivateKey(privateKeyPath: string): string {
  try {
    return fs.readFileSync(privateKeyPath, 'utf8');
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : 'Unknown error loading private key';
    throw new Error(`Failed to load private key: ${message}`);
  }
}
