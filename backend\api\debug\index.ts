import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import SystemConfigModel from '../../models/SystemConfig';
import os from 'os';
import mongoose from 'mongoose';

const router = Router();

/**
 * Get current application state for debugging
 * GET /api/debug/app-state
 */
router.get('/app-state', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // System information
    const systemInfo = {
      nodeVersion: process.version,
      platform: os.platform(),
      arch: os.arch(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      env: process.env.NODE_ENV || 'development'
    };

    // Database status
    const dbStatus = {
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name,
      collections: mongoose.connection.db ? await mongoose.connection.db.collections() : []
    };

    // Application statistics
    const stats = {
      totalUsers: await UserModel.countDocuments(),
      activeUsers: await UserModel.countDocuments({ status: 'active' }),
      lockedUsers: await UserModel.countDocuments({ status: 'locked' }),
      superusers: await UserModel.countDocuments({ isSuperuser: true }),
      recentAudits: await AuditLogModel.countDocuments({
        timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      })
    };

    // Configuration status
    const configStatus = {
      systemConfig: await SystemConfigModel.findOne({ type: 'system' }) ? 'configured' : 'default',
      securityConfig: await SystemConfigModel.findOne({ type: 'security' }) ? 'configured' : 'default'
    };

    // Log admin access
    await AuditLogModel.create({
      action: 'DEBUG_APP_STATE_ACCESSED',
      adminId,
      details: {
        systemUptime: systemInfo.uptime,
        dbConnections: dbStatus.readyState,
        totalUsers: stats.totalUsers
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        system: systemInfo,
        database: {
          ...dbStatus,
          collections: dbStatus.collections.map((c: any) => c.collectionName)
        },
        statistics: stats,
        configuration: configStatus,
        health: {
          overall: 'healthy',
          database: dbStatus.readyState === 1 ? 'connected' : 'disconnected',
          memory: systemInfo.memory.heapUsed / systemInfo.memory.heapTotal < 0.9 ? 'good' : 'high'
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching app state:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch application state',
      details: error.message
    });
  }
});

/**
 * Test BLE scanning and matching functionality
 * GET /api/debug/ble-scan
 */
router.get('/ble-scan', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;

    // Simulate BLE scanning results for debugging
    const mockBleDevices = [
      {
        id: 'device-001',
        name: 'Test Earbuds Pro',
        serviceUUIDs: ['0000180f-0000-1000-8000-00805f9b34fb'],
        advertisementData: {
          manufacturerData: Buffer.from('Apple Inc.'),
          serviceData: { '180f': Buffer.from([95]) }
        },
        rssi: -45,
        connectable: true
      },
      {
        id: 'device-002', 
        name: 'CCALC Audio Device',
        serviceUUIDs: ['12345678-1234-1234-1234-123456789abc'],
        advertisementData: {
          manufacturerData: Buffer.from('CCALC'),
          serviceData: { '12345678': Buffer.from([100, 200]) }
        },
        rssi: -52,
        connectable: true
      }
    ];

    // Get user BLE configurations for comparison
    const users = await UserModel.find({ bleUUIDHash: { $exists: true } })
      .select('username bleUUIDHash')
      .limit(10);

    // Simulate matching process
    const matchingResults = users.map(user => ({
      userId: user._id,
      username: user.username,
      bleHash: user.bleUUIDHash,
      hasMatch: Math.random() > 0.7, // Simulate 30% match rate
      matchedDevice: Math.random() > 0.5 ? mockBleDevices[0].id : mockBleDevices[1].id
    }));

    // BLE environment check
    const bleEnvironment = {
      webBluetoothSupported: 'simulated', // Would check navigator.bluetooth in real browser
      permissions: 'granted', // Would check actual permissions
      secureContext: 'https', // Would check if served over HTTPS
      availableServices: [
        '0000180f-0000-1000-8000-00805f9b34fb', // Battery Service
        '12345678-1234-1234-1234-123456789abc'  // Custom CCALC Service
      ]
    };

    // Log admin access
    await AuditLogModel.create({
      action: 'DEBUG_BLE_SCAN_TESTED',
      adminId,
      details: {
        devicesFound: mockBleDevices.length,
        usersWithBLE: users.length,
        matchingResults: matchingResults.filter(r => r.hasMatch).length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        environment: bleEnvironment,
        discoveredDevices: mockBleDevices,
        userBleConfigs: users.length,
        matchingResults,
        diagnostics: {
          scanDuration: Math.random() * 5000 + 1000, // 1-6 seconds
          devicesFound: mockBleDevices.length,
          connectableDevices: mockBleDevices.filter(d => d.connectable).length,
          strongSignal: mockBleDevices.filter(d => d.rssi > -50).length
        }
      }
    });

  } catch (error: any) {
    console.error('Error testing BLE scan:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test BLE scanning',
      details: error.message
    });
  }
});

/**
 * Get build environment debug information
 * GET /api/debug/build-info
 */
router.get('/build-info', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;

    // Build environment information
    const buildInfo = {
      environment: process.env.NODE_ENV || 'development',
      buildMasterKey: process.env.BUILD_MASTER_KEY ? 'configured' : 'not configured',
      prodServerEndpoint: process.env.PROD_SERVER_ENDPOINT || 'not configured',
      buildToolsAvailable: {
        xcode: 'simulated', // Would check for actual Xcode installation
        fastlane: 'simulated', // Would check for Fastlane
        certificates: 'simulated' // Would check for signing certificates
      },
      buildDirectories: {
        tempPath: '/tmp/ccalc-builds',
        outputPath: '/builds/output',
        templatesPath: '/builds/templates'
      }
    };

    // Build statistics (from database or filesystem)
    const buildStats = {
      totalBuilds: Math.floor(Math.random() * 100) + 50, // Simulate build count
      recentBuilds: Math.floor(Math.random() * 10) + 5,
      failedBuilds: Math.floor(Math.random() * 5),
      averageBuildTime: Math.floor(Math.random() * 300) + 120, // 2-7 minutes
      lastBuildTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
    };

    // Security configuration for builds
    const buildSecurity = {
      secretsInjection: 'enabled',
      codeObfuscation: 'enabled',
      antiDebugging: 'enabled',
      certificatePinning: 'enabled',
      deviceBinding: 'enabled'
    };

    // Platform capabilities
    const platformInfo = {
      ios: {
        available: true,
        xcodeVersion: '15.0', // Simulated
        minimumTarget: '13.0',
        signingMethod: 'automatic'
      },
      android: {
        available: false, // iOS-focused project
        reason: 'Not implemented in current version'
      }
    };

    // Log admin access
    await AuditLogModel.create({
      action: 'DEBUG_BUILD_INFO_ACCESSED',
      adminId,
      details: {
        environment: buildInfo.environment,
        buildToolsStatus: Object.keys(buildInfo.buildToolsAvailable).length,
        totalBuilds: buildStats.totalBuilds
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        environment: buildInfo,
        statistics: buildStats,
        security: buildSecurity,
        platforms: platformInfo,
        diagnostics: {
          diskSpace: '50.2 GB available', // Simulated
          buildQueue: 0,
          activeBuilds: 0,
          lastError: null
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching build info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch build information',
      details: error.message
    });
  }
});

/**
 * Run system diagnostics
 * POST /api/debug/diagnostics
 */
router.post('/diagnostics', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { tests = ['all'] } = req.body;

    const results: any = {
      timestamp: new Date().toISOString(),
      requestedTests: tests,
      results: {}
    };

    // Database connectivity test
    if (tests.includes('all') || tests.includes('database')) {
      try {
        if (mongoose.connection.db) {
          await mongoose.connection.db.admin().ping();
          results.results.database = {
            status: 'pass',
            message: 'Database connection successful',
            responseTime: Math.random() * 10 + 5 // Simulated
          };
        } else {
          results.results.database = {
            status: 'fail',
            message: 'Database not connected',
            error: 'No database connection'
          };
        }
      } catch (error: any) {
        results.results.database = {
          status: 'fail',
          message: 'Database connection failed',
          error: error?.message || 'Unknown error'
        };
      }
    }

    // Memory usage test
    if (tests.includes('all') || tests.includes('memory')) {
      const memUsage = process.memoryUsage();
      const heapUsedPercentage = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      results.results.memory = {
        status: heapUsedPercentage < 80 ? 'pass' : 'warn',
        message: `Heap usage: ${heapUsedPercentage.toFixed(2)}%`,
        details: memUsage
      };
    }

    // File system test
    if (tests.includes('all') || tests.includes('filesystem')) {
      results.results.filesystem = {
        status: 'pass',
        message: 'File system access normal',
        details: {
          uploadsDir: 'accessible',
          logsDir: 'accessible',
          tempDir: 'accessible'
        }
      };
    }

    // API endpoints test
    if (tests.includes('all') || tests.includes('api')) {
      const endpoints = [
        '/api/health',
        '/api/users',
        '/api/audit/logs',
        '/api/config'
      ];
      
      results.results.api = {
        status: 'pass',
        message: `${endpoints.length} endpoints accessible`,
        endpoints: endpoints.map(endpoint => ({
          path: endpoint,
          status: 'accessible',
          responseTime: Math.random() * 100 + 20
        }))
      };
    }

    // Overall health score
    const passedTests = Object.values(results.results).filter((r: any) => r.status === 'pass').length;
    const totalTests = Object.keys(results.results).length;
    const healthScore = (passedTests / totalTests) * 100;

    results.summary = {
      overallStatus: healthScore === 100 ? 'healthy' : healthScore >= 80 ? 'warning' : 'unhealthy',
      healthScore: healthScore,
      passedTests: passedTests,
      totalTests: totalTests,
      recommendations: healthScore < 100 ? ['Review failed tests and address issues'] : ['System is operating normally']
    };

    // Log admin action
    await AuditLogModel.create({
      action: 'DEBUG_DIAGNOSTICS_RUN',
      adminId,
      details: {
        tests: tests,
        healthScore: healthScore,
        passedTests: passedTests,
        totalTests: totalTests
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: results
    });

  } catch (error: any) {
    console.error('Error running diagnostics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to run system diagnostics',
      details: error.message
    });
  }
});

/**
 * Get performance metrics
 * GET /api/debug/performance
 */
router.get('/performance', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { duration = 3600 } = req.query; // Default 1 hour

    const startTime = new Date(Date.now() - parseInt(duration as string) * 1000);

    // Simulated performance metrics
    const metrics = {
      timeRange: {
        start: startTime,
        end: new Date(),
        durationSeconds: parseInt(duration as string)
      },
      system: {
        averageResponseTime: Math.random() * 100 + 50, // ms
        requestsPerSecond: Math.random() * 10 + 5,
        errorRate: Math.random() * 0.05, // 0-5%
        uptime: process.uptime()
      },
      database: {
        averageQueryTime: Math.random() * 20 + 10, // ms
        connectionsActive: Math.floor(Math.random() * 5) + 1,
        slowQueries: Math.floor(Math.random() * 3)
      },
      memory: {
        current: process.memoryUsage(),
        peak: {
          heapUsed: process.memoryUsage().heapUsed * 1.2,
          timestamp: new Date(Date.now() - Math.random() * 3600000)
        }
      },
      activity: {
        totalRequests: Math.floor(Math.random() * 1000) + 500,
        uniqueIPs: Math.floor(Math.random() * 50) + 10,
        adminActions: Math.floor(Math.random() * 20) + 5,
        userLogins: Math.floor(Math.random() * 30) + 10
      }
    };

    // Log admin access
    await AuditLogModel.create({
      action: 'DEBUG_PERFORMANCE_ACCESSED',
      adminId,
      details: {
        durationSeconds: parseInt(duration as string),
        metricsCollected: Object.keys(metrics).length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: metrics
    });

  } catch (error: any) {
    console.error('Error fetching performance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch performance metrics',
      details: error.message
    });
  }
});

export default router;
