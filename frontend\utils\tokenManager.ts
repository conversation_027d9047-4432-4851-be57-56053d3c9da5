/**
 * Unified Token Management System
 * Handles authentication tokens securely using httpOnly cookies
 */

import Cookies from 'js-cookie';
import { isBrowser, safeLocalStorage } from './browserUtils';

export interface TokenManager {
  setToken: (token: string, type?: 'admin' | 'user') => void;
  getToken: (type?: 'admin' | 'user') => string | null;
  removeToken: (type?: 'admin' | 'user') => void;
  clearAllTokens: () => void;
  isAuthenticated: (type?: 'admin' | 'user') => boolean;
}

class TokenManagerImpl implements TokenManager {
  private readonly ADMIN_TOKEN_KEY = 'admin-token-access'; // Use the non-httpOnly cookie that <PERSON><PERSON> can read
  private readonly USER_TOKEN_KEY = 'user-token';
  
  // Cookie options for security
  private readonly cookieOptions = {
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: 'strict' as const, // CSRF protection
    expires: 1, // 1 day expiration
  };

  /**
   * Set authentication token
   */
  setToken(token: string, type: 'admin' | 'user' = 'admin'): void {
    const key = type === 'admin' ? this.ADMIN_TOKEN_KEY : this.USER_TOKEN_KEY;
    
    console.log('TokenManager.setToken called:', {
      type,
      key,
      tokenLength: token ? token.length : 0,
      tokenPrefix: token ? token.substring(0, 20) + '...' : 'null',
      isBrowser: isBrowser()
    });
    
    // For admin tokens in development, use multiple storage methods for persistence
    if (type === 'admin') {
      if (isBrowser()) {
        console.log('TokenManager.setToken - Storing admin token:', {
          key,
          legacyKey: 'admin-token',
          token: token ? 'present' : 'missing'
        });
        
        // Store in localStorage
        try {
          safeLocalStorage.setItem(key, token);
          safeLocalStorage.setItem('admin-token', token);
          console.log('TokenManager: localStorage storage successful');
        } catch (e) {
          console.error('TokenManager: localStorage storage failed:', e);
        }
        
        // Also store in sessionStorage to survive Fast Refresh
        try {
          sessionStorage.setItem(key, token);
          sessionStorage.setItem('admin-token', token);
          console.log('TokenManager: sessionStorage storage successful');
        } catch (e) {
          console.error('TokenManager: sessionStorage storage failed:', e);
        }
        
        // Try to set cross-origin cookie with relaxed settings
        try {
          document.cookie = `${key}=${token}; path=/; SameSite=None; Secure=false; max-age=${8 * 60 * 60}`;
          document.cookie = `admin-token=${token}; path=/; SameSite=None; Secure=false; max-age=${8 * 60 * 60}`;
          console.log('TokenManager: Direct cookie setting successful');
        } catch (e) {
          console.log('TokenManager: Direct cookie setting failed:', e);
        }
        
        // Verify storage immediately
        const stored = safeLocalStorage.getItem(key);
        const sessionStored = sessionStorage.getItem(key);
        console.log('TokenManager.setToken - Verification:', {
          localStorage: stored === token,
          sessionStorage: sessionStored === token,
          storedLength: stored ? stored.length : 0,
          sessionStoredLength: sessionStored ? sessionStored.length : 0
        });
      }
      // Still try to set cookie as backup
      try {
        Cookies.set(key, token, this.cookieOptions);
        console.log('TokenManager: js-cookie storage successful');
      } catch (e) {
        console.error('TokenManager: js-cookie storage failed:', e);
      }
      return;
    }
    
    // For user tokens, use both cookie and localStorage
    Cookies.set(key, token, this.cookieOptions);
    if (isBrowser()) {
      safeLocalStorage.setItem(key, token);
    }
  }

  /**
   * Get authentication token
   */
  getToken(type: 'admin' | 'user' = 'admin'): string | null {
    const key = type === 'admin' ? this.ADMIN_TOKEN_KEY : this.USER_TOKEN_KEY;
    
    if (type === 'admin') {
      // For admin tokens, check localStorage first, then sessionStorage, then cookies
      const localToken = safeLocalStorage.getItem(key) || safeLocalStorage.getItem('admin-token');
      const sessionToken = isBrowser() ? sessionStorage.getItem(key) || sessionStorage.getItem('admin-token') : null;
      
      console.log('TokenManager.getToken debug for admin:', {
        key,
        primaryKey: key,
        legacyKey: 'admin-token',
        localToken: localToken ? 'present' : 'missing',
        sessionToken: sessionToken ? 'present' : 'missing',
        cookieCheck: 'checking cookies as backup...',
        allLocalStorageKeys: isBrowser() ? Object.keys(localStorage).filter(k => k.includes('token')) : [],
        allSessionStorageKeys: isBrowser() ? Object.keys(sessionStorage).filter(k => k.includes('token')) : [],
        localTokenLength: localToken ? localToken.length : 0,
        sessionTokenLength: sessionToken ? sessionToken.length : 0
      });
      
      // Return first available token (localStorage has priority, then sessionStorage)
      if (localToken) {
        console.log('TokenManager: Returning localStorage token');
        return localToken;
      }
      
      if (sessionToken) {
        console.log('TokenManager: Using sessionStorage token (localStorage was cleared)');
        return sessionToken;
      }
      
      // Fallback to cookie
      const cookieToken = Cookies.get(key);
      console.log('TokenManager cookie fallback:', {
        cookieToken: cookieToken ? 'present' : 'missing'
      });
      
      return cookieToken || null;
    }
    
    // For user tokens, prefer cookies
    const cookieToken = Cookies.get(key);
    if (cookieToken) {
      return cookieToken;
    }
    
    return safeLocalStorage.getItem(key);
  }

  /**
   * Remove authentication token
   */
  removeToken(type: 'admin' | 'user' = 'admin'): void {
    const key = type === 'admin' ? this.ADMIN_TOKEN_KEY : this.USER_TOKEN_KEY;
    
    // Remove from cookie
    Cookies.remove(key);
    
    // For admin tokens, also remove both cookies that backend sets
    if (type === 'admin') {
      Cookies.remove('admin-token');
      Cookies.remove('admin-token-access');
    }
    
    // Remove from localStorage
    safeLocalStorage.removeItem(key);
    
    // Remove from sessionStorage
    if (isBrowser()) {
      sessionStorage.removeItem(key);
    }
    
    // Also remove legacy localStorage and sessionStorage keys
    if (type === 'admin') {
      safeLocalStorage.removeItem('admin-token');
      safeLocalStorage.removeItem('admin-data');
      if (isBrowser()) {
        sessionStorage.removeItem('admin-token');
        sessionStorage.removeItem('admin-data');
      }
    }
  }

  /**
   * Clear all tokens
   */
  clearAllTokens(): void {
    this.removeToken('admin');
    this.removeToken('user');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(type: 'admin' | 'user' = 'admin'): boolean {
    return !!this.getToken(type);
  }

  /**
   * Get any available token (admin first, then user)
   */
  getAnyToken(): string | null {
    return this.getToken('admin') || this.getToken('user');
  }

  /**
   * Migrate from localStorage to cookies (for existing users)
   */
  migrateFromLocalStorage(): void {
    if (!isBrowser()) return;
    
    // Migrate admin token
    const adminToken = safeLocalStorage.getItem(this.ADMIN_TOKEN_KEY);
    if (adminToken && !Cookies.get(this.ADMIN_TOKEN_KEY)) {
      Cookies.set(this.ADMIN_TOKEN_KEY, adminToken, this.cookieOptions);
    }
    
    // Migrate user token
    const userToken = safeLocalStorage.getItem(this.USER_TOKEN_KEY);
    if (userToken && !Cookies.get(this.USER_TOKEN_KEY)) {
      Cookies.set(this.USER_TOKEN_KEY, userToken, this.cookieOptions);
    }
  }
}

// Export singleton instance
export const tokenManager = new TokenManagerImpl();

// Auto-migrate existing tokens on import (only in browser)
if (isBrowser()) {
  tokenManager.migrateFromLocalStorage();
}

export default tokenManager;
