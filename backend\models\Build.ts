import mongoose, { Document, Schema } from 'mongoose';

// Build model for per-user app builds with embedded secrets
export interface IBuild extends Document {
  buildId: string; // Unique build identifier
  userId: mongoose.Types.ObjectId;
  
  // Build configuration
  config: {
    appName: string;
    bundleId: string;
    version: string;
    buildNumber: number;
    environment: 'development' | 'staging' | 'production';
    platform: 'ios' | 'android';
  };
  
  // Embedded secrets and user-specific data
  secrets: {
    encryptionKey: string; // Per-user encryption key
    authToken: string; // Embedded auth token
    serverEndpoint: string; // Backend server URL
    bleServiceUUID: string; // BLE service UUID for this user
    bleCharacteristicUUID: string; // BLE characteristic UUID
    deviceFingerprint: string; // Expected device fingerprint
    unlockExpression: string; // Hashed unlock expression
    userIdentifier: string; // Unique user identifier
  };
  
  // Build process information
  buildProcess: {
    status: 'pending' | 'building' | 'completed' | 'failed' | 'revoked';
    startedAt?: Date;
    completedAt?: Date;
    failedAt?: Date;
    revokedAt?: Date;
    buildDuration?: number; // in seconds
    buildLog?: string;
    errorLog?: string;
    warnings?: string[];
  };
  
  // Build artifacts
  artifacts: {
    ipaPath?: string; // iOS .ipa file path
    apkPath?: string; // Android .apk file path
    manifestPath?: string; // Manifest file for enterprise distribution
    symbolsPath?: string; // Debug symbols
    size: number; // File size in bytes
    checksum: string; // SHA256 checksum
    downloadUrl?: string; // Temporary download URL
    downloadExpiry?: Date; // Download link expiry
  };
  
  // Security and distribution
  security: {
    signed: boolean;
    certificate: string; // Signing certificate identifier
    provisioning: string; // Provisioning profile
    distributionMethod: 'adhoc' | 'enterprise' | 'development';
    deviceUDIDs?: string[]; // Allowed device UDIDs
    installCount: number; // Number of installations
    maxInstalls: number; // Maximum allowed installations
    revoked: boolean;
    revokeReason?: string;
  };
  
  // Usage tracking
  usage: {
    downloaded: boolean;
    downloadedAt?: Date;
    downloadCount: number;
    downloadIPs: string[];
    installed: boolean;
    installedAt?: Date;
    firstLaunch?: Date;
    lastActive?: Date;
    uninstalled?: boolean;
    uninstalledAt?: Date;
  };
  
  // Admin metadata
  metadata: {
    createdBy: mongoose.Types.ObjectId;
    notes?: string;
    tags?: string[];
    priority: 'low' | 'normal' | 'high' | 'urgent';
    retentionPolicy: 'auto' | 'manual' | 'permanent';
    expiresAt?: Date;
  };
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const BuildSchema = new Schema<IBuild>(
  {
    buildId: { type: String, required: true, unique: true },
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    
    config: {
      appName: { type: String, required: true },
      bundleId: { type: String, required: true },
      version: { type: String, required: true },
      buildNumber: { type: Number, required: true },
      environment: {
        type: String,
        enum: ['development', 'staging', 'production'],
        default: 'production',
      },
      platform: {
        type: String,
        enum: ['ios', 'android'],
        default: 'ios',
      },
    },
    
    secrets: {
      encryptionKey: { type: String, required: true },
      authToken: { type: String, required: true },
      serverEndpoint: { type: String, required: true },
      bleServiceUUID: { type: String, required: true },
      bleCharacteristicUUID: { type: String, required: true },
      deviceFingerprint: { type: String, required: true },
      unlockExpression: { type: String, required: true },
      userIdentifier: { type: String, required: true },
    },
    
    buildProcess: {
      status: {
        type: String,
        enum: ['pending', 'building', 'completed', 'failed', 'revoked'],
        default: 'pending',
      },
      startedAt: Date,
      completedAt: Date,
      failedAt: Date,
      revokedAt: Date,
      buildDuration: Number,
      buildLog: String,
      errorLog: String,
      warnings: [String],
    },
    
    artifacts: {
      ipaPath: String,
      apkPath: String,
      manifestPath: String,
      symbolsPath: String,
      size: { type: Number, default: 0 },
      checksum: String,
      downloadUrl: String,
      downloadExpiry: Date,
    },
    
    security: {
      signed: { type: Boolean, default: false },
      certificate: String,
      provisioning: String,
      distributionMethod: {
        type: String,
        enum: ['adhoc', 'enterprise', 'development'],
        default: 'adhoc',
      },
      deviceUDIDs: [String],
      installCount: { type: Number, default: 0 },
      maxInstalls: { type: Number, default: 1 },
      revoked: { type: Boolean, default: false },
      revokeReason: String,
    },
    
    usage: {
      downloaded: { type: Boolean, default: false },
      downloadedAt: Date,
      downloadCount: { type: Number, default: 0 },
      downloadIPs: [String],
      installed: { type: Boolean, default: false },
      installedAt: Date,
      firstLaunch: Date,
      lastActive: Date,
      uninstalled: { type: Boolean, default: false },
      uninstalledAt: Date,
    },
    
    metadata: {
      createdBy: { type: Schema.Types.ObjectId, ref: 'Admin', required: true },
      notes: String,
      tags: [String],
      priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'urgent'],
        default: 'normal',
      },
      retentionPolicy: {
        type: String,
        enum: ['auto', 'manual', 'permanent'],
        default: 'auto',
      },
      expiresAt: Date,
    },
    
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Indexes for performance
BuildSchema.index({ userId: 1, createdAt: -1 });
BuildSchema.index({ buildId: 1 });
BuildSchema.index({ 'buildProcess.status': 1 });
BuildSchema.index({ 'security.revoked': 1, isActive: 1 });
BuildSchema.index({ 'metadata.expiresAt': 1 }, { expireAfterSeconds: 0 });

export default mongoose.model<IBuild>('Build', BuildSchema);
