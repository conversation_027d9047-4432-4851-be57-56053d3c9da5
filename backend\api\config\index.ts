import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import SystemConfigModel from '../../models/SystemConfig';
import crypto from 'crypto';

const router = Router();

/**
 * Get current system configuration
 * GET /api/config
 */
router.get('/', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // Get system configuration
    let config = await SystemConfigModel.findOne({ type: 'system' });
    
    if (!config) {
      // Create default configuration
      config = await SystemConfigModel.create({
        type: 'system',
        settings: getDefaultSystemConfig(),
        updatedBy: adminId,
        updatedAt: new Date()
      });
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'SYSTEM_CONFIG_ACCESSED',
      adminId,
      details: {
        configType: 'system'
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        config: config.settings,
        lastUpdated: config.updatedAt,
        updatedBy: config.updatedBy
      }
    });

  } catch (error: any) {
    console.error('Error fetching system config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system configuration',
      details: error.message
    });
  }
});

/**
 * Update system configuration
 * PUT /api/config
 */
router.put('/', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Settings object is required'
      });
      return;
    }    // Validate settings
    const validationResult = validateSystemConfig(settings);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid configuration settings',
        details: validationResult.errors
      });
      return;
    }

    // Get current config
    let config = await SystemConfigModel.findOne({ type: 'system' });
    const oldSettings = config ? { ...config.settings } : {};

    if (!config) {
      // Create new config
      config = new SystemConfigModel({
        type: 'system',
        settings: settings,
        updatedBy: adminId,
        updatedAt: new Date()
      });
    } else {
      // Update existing config
      config.settings = { ...config.settings, ...settings };
      config.updatedBy = adminId;
      config.updatedAt = new Date();
    }

    await config.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'SYSTEM_CONFIG_UPDATED',
      adminId,
      details: {
        configType: 'system',
        changes: getConfigChanges(oldSettings, settings),
        affectedSettings: Object.keys(settings)
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'System configuration updated successfully',
      data: {
        config: config.settings,
        updatedAt: config.updatedAt,
        changes: getConfigChanges(oldSettings, settings)
      }
    });

  } catch (error: any) {
    console.error('Error updating system config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update system configuration',
      details: error.message
    });
  }
});

/**
 * Get security configuration
 * GET /api/config/security
 */
router.get('/security', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;

    // Get security configuration
    let config = await SystemConfigModel.findOne({ type: 'security' });
    
    if (!config) {
      // Create default security configuration
      config = await SystemConfigModel.create({
        type: 'security',
        settings: getDefaultSecurityConfig(),
        updatedBy: adminId,
        updatedAt: new Date()
      });
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'SECURITY_CONFIG_ACCESSED',
      adminId,
      details: {
        configType: 'security'
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        config: config.settings,
        lastUpdated: config.updatedAt,
        updatedBy: config.updatedBy
      }
    });

  } catch (error: any) {
    console.error('Error fetching security config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security configuration',
      details: error.message
    });
  }
});

/**
 * Update security configuration
 * PUT /api/config/security
 */
router.put('/security', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { settings } = req.body;

    if (!settings || typeof settings !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Settings object is required'
      });
      return;
    }

    // Validate security settings
    const validationResult = validateSecurityConfig(settings);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid security configuration',
        details: validationResult.errors
      });
      return;
    }

    // Get current config
    let config = await SystemConfigModel.findOne({ type: 'security' });
    const oldSettings = config ? { ...config.settings } : {};

    if (!config) {
      // Create new config
      config = new SystemConfigModel({
        type: 'security',
        settings: settings,
        updatedBy: adminId,
        updatedAt: new Date()
      });
    } else {
      // Update existing config
      config.settings = { ...config.settings, ...settings };
      config.updatedBy = adminId;
      config.updatedAt = new Date();
    }

    await config.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'SECURITY_CONFIG_UPDATED',
      adminId,
      details: {
        configType: 'security',
        changes: getConfigChanges(oldSettings, settings),
        affectedSettings: Object.keys(settings)
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Security configuration updated successfully',
      data: {
        config: config.settings,
        updatedAt: config.updatedAt,
        changes: getConfigChanges(oldSettings, settings)
      }
    });

  } catch (error: any) {
    console.error('Error updating security config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update security configuration',
      details: error.message
    });
  }
});

/**
 * Reset configuration to defaults
 * POST /api/config/reset
 */
router.post('/reset', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { configType = 'system' } = req.body;

    if (!['system', 'security'].includes(configType)) {
      res.status(400).json({
        success: false,
        error: 'Invalid config type. Must be "system" or "security"'
      });
      return;
    }

    // Get current config for backup
    const currentConfig = await SystemConfigModel.findOne({ type: configType });
    const backupSettings = currentConfig ? { ...currentConfig.settings } : {};

    // Reset to defaults
    const defaultSettings = configType === 'system' 
      ? getDefaultSystemConfig() 
      : getDefaultSecurityConfig();

    // Update or create config
    let config = await SystemConfigModel.findOneAndUpdate(
      { type: configType },
      {
        settings: defaultSettings,
        updatedBy: adminId,
        updatedAt: new Date()
      },
      { upsert: true, new: true }
    );

    // Log admin action
    await AuditLogModel.create({
      action: 'CONFIG_RESET_TO_DEFAULTS',
      adminId,
      details: {
        configType,
        backupSettings,
        defaultSettings
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: `${configType} configuration reset to defaults`,
      data: {
        config: config.settings,
        updatedAt: config.updatedAt,
        backup: backupSettings
      }
    });

  } catch (error: any) {
    console.error('Error resetting config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset configuration',
      details: error.message
    });
  }
});

/**
 * Get configuration history
 * GET /api/config/history
 */
router.get('/history', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { configType, limit = 20, skip = 0 } = req.query;

    // Build filter
    const filter: any = {
      action: {
        $in: ['SYSTEM_CONFIG_UPDATED', 'SECURITY_CONFIG_UPDATED', 'CONFIG_RESET_TO_DEFAULTS']
      }
    };

    if (configType) {
      filter['details.configType'] = configType;
    }

    // Get configuration history
    const history = await AuditLogModel.find(filter)
      .populate('adminId', 'username email')
      .sort({ timestamp: -1 })
      .limit(parseInt(limit as string))
      .skip(parseInt(skip as string))
      .exec();

    const totalCount = await AuditLogModel.countDocuments(filter);

    // Log admin access
    await AuditLogModel.create({
      action: 'CONFIG_HISTORY_ACCESSED',
      adminId,
      details: {
        configType,
        resultCount: history.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        history,
        pagination: {
          total: totalCount,
          limit: parseInt(limit as string),
          skip: parseInt(skip as string),
          pages: Math.ceil(totalCount / parseInt(limit as string))
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching config history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch configuration history',
      details: error.message
    });
  }
});

// Helper functions
function getDefaultSystemConfig() {
  return {
    appName: 'CCALC',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    features: {
      userRegistration: true,
      buildGeneration: true,
      mediaUpload: true,
      voiceCalls: true,
      bleIntegration: true
    },
    limits: {
      maxUsers: 1000,
      maxMediaSize: 100 * 1024 * 1024, // 100MB
      maxCallDuration: 3600, // 1 hour
      maxConcurrentCalls: 10
    },
    maintenance: {
      enabled: false,
      message: 'System is under maintenance. Please try again later.',
      allowedAdmins: true
    },
    notifications: {
      enabled: true,
      email: true,
      push: false
    }
  };
}

function getDefaultSecurityConfig() {
  return {
    authentication: {
      tokenTTL: 24 * 60 * 60 * 1000, // 24 hours
      refreshTokenTTL: 7 * 24 * 60 * 60 * 1000, // 7 days
      maxLoginAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      requirePPK: true,
      allowPasswordReset: false
    },
    encryption: {
      algorithm: 'AES-256-GCM',
      keyRotationInterval: 30 * 24 * 60 * 60 * 1000, // 30 days
      saltRounds: 12
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      skipSuccessfulRequests: false
    },
    cors: {
      origin: process.env.FRONTEND_URL || 'http://localhost:3000',
      credentials: true
    },
    session: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
    audit: {
      enabled: true,
      logLevel: 'info',
      retentionDays: 90
    }
  };
}

function validateSystemConfig(settings: any) {
  const result = {
    isValid: true,
    errors: [] as string[]
  };

  // Validate features
  if (settings.features && typeof settings.features !== 'object') {
    result.errors.push('Features must be an object');
    result.isValid = false;
  }

  // Validate limits
  if (settings.limits) {
    if (settings.limits.maxUsers && (!Number.isInteger(settings.limits.maxUsers) || settings.limits.maxUsers < 1)) {
      result.errors.push('maxUsers must be a positive integer');
      result.isValid = false;
    }
    
    if (settings.limits.maxMediaSize && (!Number.isInteger(settings.limits.maxMediaSize) || settings.limits.maxMediaSize < 1)) {
      result.errors.push('maxMediaSize must be a positive integer');
      result.isValid = false;
    }
  }

  return result;
}

function validateSecurityConfig(settings: any) {
  const result = {
    isValid: true,
    errors: [] as string[]
  };

  // Validate authentication settings
  if (settings.authentication) {
    const auth = settings.authentication;
    
    if (auth.tokenTTL && (!Number.isInteger(auth.tokenTTL) || auth.tokenTTL < 1)) {
      result.errors.push('tokenTTL must be a positive integer');
      result.isValid = false;
    }
    
    if (auth.maxLoginAttempts && (!Number.isInteger(auth.maxLoginAttempts) || auth.maxLoginAttempts < 1)) {
      result.errors.push('maxLoginAttempts must be a positive integer');
      result.isValid = false;
    }
  }

  // Validate rate limit settings
  if (settings.rateLimit) {
    const rateLimit = settings.rateLimit;
    
    if (rateLimit.max && (!Number.isInteger(rateLimit.max) || rateLimit.max < 1)) {
      result.errors.push('rateLimit.max must be a positive integer');
      result.isValid = false;
    }
  }

  return result;
}

function getConfigChanges(oldSettings: any, newSettings: any) {
  const changes: any = {};
  
  for (const key in newSettings) {
    if (JSON.stringify(oldSettings[key]) !== JSON.stringify(newSettings[key])) {
      changes[key] = {
        old: oldSettings[key],
        new: newSettings[key]
      };
    }
  }
  
  return changes;
}

export default router;
