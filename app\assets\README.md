# CCALC App Assets

This directory contains the required assets for Expo configuration.

For now, we're using placeholder references. In a production app, you would add:

- icon.png (1024x1024) - App icon
- splash.png (1284x2778) - Splash screen image  
- adaptive-icon.png (1024x1024) - Android adaptive icon
- favicon.png (48x48) - Web favicon

Since we're focusing on functionality testing, these placeholder references won't prevent the app from running in Expo Go.
