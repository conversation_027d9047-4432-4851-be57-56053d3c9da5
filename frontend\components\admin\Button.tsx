import React, { ButtonHTMLAttributes, ReactNode } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  className = '',
  ...props
}) => {
  const getVariantClass = () => {
    switch (variant) {
      case 'primary': return 'btn--primary';
      case 'secondary': return 'btn--secondary';
      case 'success': return 'btn--success';
      case 'danger': return 'btn--danger';
      case 'warning': return 'btn--warning';
      case 'ghost': return 'btn--ghost';
      case 'outline': return 'btn--outline';
      default: return 'btn--primary';
    }
  };
  
  const getSizeClass = () => {
    switch (size) {
      case 'sm': return 'btn--sm';
      case 'lg': return 'btn--lg';
      default: return '';
    }
  };
  
  const classes = [
    'btn',
    getVariantClass(),
    getSizeClass(),
    fullWidth ? 'btn--full-width' : '',
    isLoading ? 'btn--loading' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <button className={classes} disabled={isLoading || props.disabled} {...props}>
      {isLoading && (
        <span className="btn__spinner">
          <svg width="16" height="16" viewBox="0 0 24 24">
            <circle
              cx="12"
              cy="12"
              r="10"
              fill="none"
              stroke="currentColor"
              strokeWidth="4"
              strokeDasharray="32"
              strokeDashoffset="16"
              strokeLinecap="round"
            />
          </svg>
        </span>
      )}
      {leftIcon && !isLoading && <span className="btn__icon btn__icon--left">{leftIcon}</span>}
      <span className="btn__text">{children}</span>
      {rightIcon && !isLoading && <span className="btn__icon btn__icon--right">{rightIcon}</span>}
      
      <style jsx>{`
        .btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: var(--spacing-2);
          border-radius: var(--radius-sm);
          font-weight: 500;
          padding: 0.6rem 1.2rem;
          background: transparent;
          border: 1px solid transparent;
          cursor: pointer;
          font-size: 0.875rem;
          transition: all var(--transition);
          position: relative;
          white-space: nowrap;
        }
        
        .btn:focus {
          outline: none;
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
        }
        
        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        
        /* Variants */
        .btn--primary {
          background: var(--color-accent);
          color: white;
        }
        
        .btn--primary:hover:not(:disabled) {
          background: var(--color-accent-dark);
        }
        
        .btn--secondary {
          background: #f4f6fa;
          color: var(--color-text);
          border-color: var(--color-border);
        }
        
        .btn--secondary:hover:not(:disabled) {
          background: #ebeef2;
        }
        
        .btn--success {
          background: var(--color-success);
          color: white;
        }
        
        .btn--success:hover:not(:disabled) {
          background: #15803d;
        }
        
        .btn--danger {
          background: var(--color-error);
          color: white;
        }
        
        .btn--danger:hover:not(:disabled) {
          background: #b91c1c;
        }
        
        .btn--warning {
          background: var(--color-warning);
          color: white;
        }
        
        .btn--warning:hover:not(:disabled) {
          background: #ea580c;
        }
        
        .btn--ghost {
          background: transparent;
          color: var(--color-text);
        }
        
        .btn--ghost:hover:not(:disabled) {
          background: rgba(0, 0, 0, 0.04);
        }
        
        .btn--outline {
          background: transparent;
          color: var(--color-accent);
          border-color: var(--color-accent);
        }
        
        .btn--outline:hover:not(:disabled) {
          background: rgba(37, 99, 235, 0.1);
        }
        
        /* Sizes */
        .btn--sm {
          padding: 0.4rem 0.8rem;
          font-size: 0.75rem;
        }
        
        .btn--lg {
          padding: 0.8rem 1.6rem;
          font-size: 1rem;
        }
        
        /* Full width */
        .btn--full-width {
          width: 100%;
        }
        
        /* Loading state */
        .btn--loading {
          color: transparent !important;
        }
        
        .btn__spinner {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          color: currentColor;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          to { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .btn__icon {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      `}</style>
    </button>
  );
};

export default Button;
