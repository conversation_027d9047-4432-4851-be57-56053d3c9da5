import Admin, { IAdmin } from '../models/Admin';
import User, { IUser } from '../models/User';
import bcrypt from 'bcrypt';
import argon2 from 'argon2';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { promisify } from 'util';
import { Request } from 'express';
import fs from 'fs';
import path from 'path';
import { generateChallenge } from '../utils/ppk';

interface LoginResult {
  success: boolean;
  token?: string;
  admin?: IAdmin;
  user?: IUser;
  error?: string;
}

// Helper: verify password with bcrypt or argon2
export async function verifyPassword(hash: string, password: string): Promise<boolean> {
  if (hash.startsWith('$2')) {
    // bcrypt
    return bcrypt.compare(password, hash);
  } else if (hash.startsWith('$argon2')) {
    // argon2
    return argon2.verify(hash, password);
  }
  return false;
}

// Generate session token
export function generateSessionToken(userId: string, isAdmin = false): string {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET not configured');
  }

  return jwt.sign(
    { 
      [isAdmin ? 'adminId' : 'userId']: userId,
      timestamp: Date.now()
    },
    secret,
    { expiresIn: '8h' }
  );
}

// Admin login with support for PPK authentication
export async function adminLogin(
  username: string,
  password: string,
  deviceFingerprint: string
): Promise<LoginResult> {
  try {
    const admin = await Admin.findOne({ username });

    if (!admin || !admin.isActive) {
      return { success: false, error: 'Invalid credentials' };
    }    // Verify password
    const validPassword = await verifyPassword(admin.password, password);
    if (!validPassword) {
      return { success: false, error: 'Invalid credentials' };
    }

    // Generate session token
    const token = generateSessionToken(admin._id ? admin._id.toString() : '', true);

    // Store hashed token
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    if (!admin.sessionTokens) admin.sessionTokens = [];
    admin.sessionTokens.push(tokenHash);

    // Update admin record
    admin.lastLogin = new Date();
    await admin.save();

    return {
      success: true,
      token,
      admin
    };

  } catch (error) {
    console.error('Admin login error:', error);
    return { success: false, error: 'Authentication failed' };
  }
}

// User login (remains unchanged as it uses a different auth method)
export async function userLogin(
  username: string,
  expression: string,
  deviceFingerprint: string,
  bleUUID: string
): Promise<LoginResult> {
  try {
    const user = await User.findOne({ username });

    if (!user || user.status !== 'active') {
      return { success: false, error: 'Invalid credentials' };
    }

    // Hash the provided expression
    const expressionHash = crypto.createHash('sha256')
      .update(expression)
      .digest('hex');

    if (expressionHash !== user.expressionHash) {
      // Increment failed attempts
      user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
      
      // Lock account after too many failed attempts
      if (user.failedLoginAttempts >= 5) {
        user.status = 'locked';
      }
      
      await user.save();
      return { success: false, error: 'Invalid credentials' };
    }

    // Verify device fingerprint
    const fingerprintHash = crypto.createHash('sha256')
      .update(deviceFingerprint)
      .digest('hex');

    if (fingerprintHash !== user.deviceFingerprintHash) {
      return { success: false, error: 'Invalid device' };
    }

    // Verify BLE UUID
    const bleUUIDHash = crypto.createHash('sha256')
      .update(bleUUID)
      .digest('hex');

    if (bleUUIDHash !== user.bleUUIDHash) {
      return { success: false, error: 'Invalid BLE device' };
    }    // Reset failed attempts
    user.failedLoginAttempts = 0;
    
    // Generate token
    const token = generateSessionToken(user._id ? user._id.toString() : '');

    // Update user record
    user.lastLoginAt = new Date();
    await user.save();

    return {
      success: true,
      token,
      user
    };

  } catch (error) {
    console.error('User login error:', error);
    return { success: false, error: 'Authentication failed' };
  }
}

// Logout helper
export async function logout(token: string): Promise<boolean> {
  try {
    // Hash the token
    const hash = crypto.createHash('sha256').update(token).digest('hex');
    
    // Remove from admin sessions
    await Admin.updateMany(
      { sessionTokens: hash },
      { $pull: { sessionTokens: hash } }
    );

    // Add to blacklist
    const TokenBlacklist = require('../models/TokenBlacklist').default;
    await new TokenBlacklist({ token: hash }).save();

    return true;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
}
