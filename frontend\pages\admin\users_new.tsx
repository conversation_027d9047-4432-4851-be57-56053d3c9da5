import React from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { withAdminAuth } from '../../contexts/AdminAuthContext';
import UsersManagement from '../../components/admin/UsersManagement';

const UsersPage: React.FC = () => {
  return (
    <AdminLayout>
      <Head>
        <title>User Management - CCALC Admin Panel</title>
        <meta name="description" content="Comprehensive user monitoring and management" />
      </Head>
      
      <UsersManagement />
    </AdminLayout>
  );
};

export default withAdminAuth(UsersPage);
