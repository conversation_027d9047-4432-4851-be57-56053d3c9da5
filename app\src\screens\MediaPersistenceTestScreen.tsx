/**
 * Media Persistence Diagnostic Script
 * Explicitly tests file writing and reading for persistence
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, ScrollView, Image, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';

export default function MediaPersistenceTestScreen() {
  const [testLogs, setTestLogs] = useState<string[]>([]);
  const [testImage, setTestImage] = useState<string | null>(null);
  const [savedImageUri, setSavedImageUri] = useState<string | null>(null);
  const [isImageVisible, setIsImageVisible] = useState(true);

  const log = (message: string) => {
    console.log(message);
    setTestLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Ensure directories exist
  const ensureDirectories = async () => {
    try {
      const mediaDir = `${FileSystem.documentDirectory}CCALC_Media/`;
      const mediaInfo = await FileSystem.getInfoAsync(mediaDir);
      
      if (!mediaInfo.exists) {
        await FileSystem.makeDirectoryAsync(mediaDir, { intermediates: true });
        log(`Created media directory: ${mediaDir}`);
      } else {
        log(`Media directory exists: ${mediaDir}`);
      }
      
      return { mediaDir };
    } catch (error) {
      log(`Error ensuring directories: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  };

  // Test file persistence by writing and then reading a test file
  const testFilePersistence = async () => {
    try {
      log('Starting file persistence test...');
      const { mediaDir } = await ensureDirectories();
      
      // Create a test file with timestamp
      const timestamp = new Date().toISOString();
      const testFilePath = `${mediaDir}test_persistence_${Date.now()}.txt`;
      
      // Write to the file
      await FileSystem.writeAsStringAsync(testFilePath, `Test file created at ${timestamp}`);
      log(`Test file written: ${testFilePath}`);
      
      // Read the file back
      const fileContent = await FileSystem.readAsStringAsync(testFilePath);
      log(`File read success: ${fileContent}`);
      
      return true;
    } catch (error) {
      log(`❌ File persistence test failed: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  };

  // Pick and save an image
  const pickAndSaveImage = async () => {
    try {
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        log('❌ Permission to access media library denied');
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.8,
        allowsEditing: true,
      });
      
      if (result.canceled) {
        log('Image picker canceled');
        return;
      }
      
      // Got image
      const imageUri = result.assets[0].uri;
      log(`Image selected: ${imageUri}`);
      setTestImage(imageUri);
      
      // Save to persistent location
      const { mediaDir } = await ensureDirectories();
      const filename = `test_image_${Date.now()}.jpg`;
      const persistentPath = `${mediaDir}${filename}`;
      
      // Copy file
      await FileSystem.copyAsync({
        from: imageUri,
        to: persistentPath
      });
      
      log(`Image saved to persistent storage: ${persistentPath}`);
      
      // Check file exists
      const fileInfo = await FileSystem.getInfoAsync(persistentPath);
      if (fileInfo.exists) {
        log(`Verified file exists: ${fileInfo.size} bytes`);
        setSavedImageUri(persistentPath);
      } else {
        log('❌ Failed to verify file exists after save');
      }
    } catch (error) {
      log(`❌ Image save error: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Toggle image visibility (to simulate component unmount/remount)
  const toggleImageVisibility = () => {
    setIsImageVisible(!isImageVisible);
    log(`Image visibility toggled to: ${!isImageVisible}`);
  };

  // Check if previously saved image is still accessible
  const checkImageAccess = async () => {
    try {
      if (!savedImageUri) {
        log('No saved image to check');
        return;
      }
      
      const fileInfo = await FileSystem.getInfoAsync(savedImageUri);
      if (fileInfo.exists) {
        log(`✅ Image is still accessible: ${savedImageUri}`);
        log(`Size: ${fileInfo.size} bytes`);
      } else {
        log(`❌ Image no longer accessible: ${savedImageUri}`);
      }
    } catch (error) {
      log(`❌ Error checking image: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // List all files in the media directory
  const listMediaFiles = async () => {
    try {
      const { mediaDir } = await ensureDirectories();
      
      // Read directory
      const files = await FileSystem.readDirectoryAsync(mediaDir);
      log(`Found ${files.length} files in media directory:`);
      
      // Get details for each file
      for (const file of files) {
        const filePath = `${mediaDir}${file}`;
        const fileInfo = await FileSystem.getInfoAsync(filePath, { size: true });
        const fileSize = fileInfo.exists ? (fileInfo as any).size || 0 : 0;
        log(`- ${file}: ${fileSize} bytes`);
      }
    } catch (error) {
      log(`❌ Error listing files: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  useEffect(() => {
    // Run initial test on mount
    testFilePersistence();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Media Persistence Test</Text>
      
      <View style={styles.buttonContainer}>
        <Button title="Test File Persistence" onPress={testFilePersistence} />
        <Button title="Pick & Save Image" onPress={pickAndSaveImage} />
        <Button title="Toggle Image Visibility" onPress={toggleImageVisibility} />
        <Button title="Check Image Access" onPress={checkImageAccess} />
        <Button title="List Media Files" onPress={listMediaFiles} />
      </View>
      
      {isImageVisible && savedImageUri && (
        <View style={styles.imageContainer}>
          <Text style={styles.subtitle}>Saved Image</Text>
          <Image 
            source={{ uri: savedImageUri }} 
            style={styles.image}
            onError={() => log('❌ Error displaying saved image')}
            onLoad={() => log('✅ Image loaded successfully')}
          />
          <Text style={styles.imageUri}>{savedImageUri}</Text>
        </View>
      )}
      
      <Text style={styles.subtitle}>Test Logs</Text>
      <ScrollView style={styles.logs}>
        {testLogs.map((log, index) => (
          <Text key={index} style={styles.logLine}>{log}</Text>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  logs: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 8,
  },
  logLine: {
    fontSize: 12,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    marginBottom: 4,
  },
  imageContainer: {
    alignItems: 'center',
    marginVertical: 16,
    padding: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 8,
    backgroundColor: '#e0e0e0',
  },
  imageUri: {
    fontSize: 10,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
});
