name: Android Standalone Build

on:
  workflow_dispatch:
    inputs:
      backend_url:
        description: 'Backend URL (e.g., https://your-ngrok-url.ngrok.io)'
        required: true
        default: 'https://example.ngrok.io'
      frontend_url:
        description: 'Frontend URL (e.g., https://your-frontend-url.ngrok.io)'
        required: false
        default: 'https://example.ngrok.io'
      build_profile:
        description: 'EAS Build Profile'
        required: true
        default: 'github-ci'
        type: choice
        options:
          - github-ci
          - preview
          - production

jobs:
  build-android:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Setup Expo CLI
        run: npm install -g @expo/cli@latest eas-cli@latest

      - name: Install dependencies
        working-directory: ./app
        run: npm ci

      - name: Create .env file with build URLs
        working-directory: ./app
        run: |
          echo "EXPO_PUBLIC_BACKEND_URL=${{ github.event.inputs.backend_url }}" > .env
          echo "EXPO_PUBLIC_FRONTEND_URL=${{ github.event.inputs.frontend_url || github.event.inputs.backend_url }}" >> .env
          echo "EXPO_PUBLIC_BUILD_ENV=github-ci" >> .env
          echo "EXPO_PUBLIC_APP_VERSION=1.0.${{ github.run_number }}" >> .env
          echo "EXPO_PUBLIC_DEBUG_MODE=false" >> .env
          echo "EXPO_PUBLIC_LOG_NETWORK=false" >> .env
          echo "Created .env file:"
          cat .env

      - name: Authenticate with Expo
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        run: |
          if [ -z "$EXPO_TOKEN" ]; then
            echo "❌ EXPO_TOKEN secret not found. Please add it to your repository secrets."
            exit 1
          fi
          expo whoami

      - name: Build Android app with EAS
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          EXPO_PUBLIC_BACKEND_URL: ${{ github.event.inputs.backend_url }}
          EXPO_PUBLIC_FRONTEND_URL: ${{ github.event.inputs.frontend_url || github.event.inputs.backend_url }}
        run: |
          echo "🏗️ Building Android app with profile: ${{ github.event.inputs.build_profile }}"
          eas build --platform android --non-interactive --profile ${{ github.event.inputs.build_profile }} --wait

      - name: Get build info
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        run: |
          echo "📱 Getting latest build info..."
          eas build:list --platform android --limit 1 --json > build-info.json
          cat build-info.json

      - name: Upload build info
        uses: actions/upload-artifact@v4
        with:
          name: android-build-info
          path: app/build-info.json
          retention-days: 30
