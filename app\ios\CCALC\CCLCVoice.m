//
//  CCLCVoice.m
//  CCALC
//
//  Created on 2025-06-22.
//  Voice Processing Module Implementation
//

#import "CCLCVoice.h"
#import <React/RCTLog.h>

@implementation CCLCVoice

RCT_EXPORT_MODULE();

+ (BOOL)requiresMainQueueSetup
{
    return YES;
}

- (NSArray<NSString *> *)supportedEvents
{
    return @[@"onRecordingStarted", @"onRecordingStopped", @"onPlaybackStarted", @"onPlaybackStopped", @"onVoiceMorphingApplied"];
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setupAudioEngine];
    }
    return self;
}

- (void)setupAudioEngine
{
    self.audioEngine = [[AVAudioEngine alloc] init];
    self.playerNode = [[AVAudioPlayerNode alloc] init];
    self.pitchEffect = [[AVAudioUnitTimePitch alloc] init];
    self.distortionEffect = [[AVAudioUnitDistortion alloc] init];
    self.reverbEffect = [[AVAudioUnitReverb alloc] init];
    
    // Connect audio nodes
    [self.audioEngine attachNode:self.playerNode];
    [self.audioEngine attachNode:self.pitchEffect];
    [self.audioEngine attachNode:self.distortionEffect];
    [self.audioEngine attachNode:self.reverbEffect];
    
    // Create audio chain: playerNode -> pitch -> distortion -> reverb -> output
    [self.audioEngine connect:self.playerNode to:self.pitchEffect format:nil];
    [self.audioEngine connect:self.pitchEffect to:self.distortionEffect format:nil];
    [self.audioEngine connect:self.distortionEffect to:self.reverbEffect format:nil];
    [self.audioEngine connect:self.reverbEffect to:self.audioEngine.mainMixerNode format:nil];
}

RCT_EXPORT_METHOD(startRecording:(NSString *)outputPath
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        AVAudioSession *audioSession = [AVAudioSession sharedInstance];
        NSError *error;
        
        [audioSession setCategory:AVAudioSessionCategoryPlayAndRecord
                     withOptions:AVAudioSessionCategoryOptionDefaultToSpeaker
                           error:&error];
        
        if (error) {
            reject(@"AUDIO_SESSION_ERROR", error.localizedDescription, error);
            return;
        }
        
        [audioSession setActive:YES error:&error];
        if (error) {
            reject(@"AUDIO_SESSION_ACTIVATION_ERROR", error.localizedDescription, error);
            return;
        }
        
        NSURL *outputURL = [NSURL fileURLWithPath:outputPath];
        
        NSDictionary *recordSettings = @{
            AVFormatIDKey: @(kAudioFormatMPEG4AAC),
            AVSampleRateKey: @44100.0,
            AVNumberOfChannelsKey: @1,
            AVEncoderAudioQualityKey: @(AVAudioQualityHigh)
        };
        
        self.audioRecorder = [[AVAudioRecorder alloc] initWithURL:outputURL
                                                          settings:recordSettings
                                                             error:&error];
        
        if (error) {
            reject(@"RECORDER_INIT_ERROR", error.localizedDescription, error);
            return;
        }
        
        self.audioRecorder.delegate = self;
        [self.audioRecorder prepareToRecord];
        
        BOOL success = [self.audioRecorder record];
        if (success) {
            [self sendEventWithName:@"onRecordingStarted" body:@{@"outputPath": outputPath}];
            resolve(@{@"success": @YES, @"message": @"Recording started"});
        } else {
            reject(@"RECORDING_START_ERROR", @"Failed to start recording", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"RECORDING_EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(stopRecording:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (self.audioRecorder && self.audioRecorder.isRecording) {
            [self.audioRecorder stop];
            NSString *recordingPath = self.audioRecorder.url.path;
            [self sendEventWithName:@"onRecordingStopped" body:@{@"recordingPath": recordingPath}];
            resolve(@{@"success": @YES, @"recordingPath": recordingPath});
        } else {
            reject(@"NO_ACTIVE_RECORDING", @"No active recording to stop", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"STOP_RECORDING_EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(applyVoiceMorphing:(NSDictionary *)profile
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        NSNumber *pitchShift = profile[@"pitchShift"] ?: @0.0;
        NSNumber *formantShift = profile[@"formantShift"] ?: @0.0;
        NSNumber *distortion = profile[@"distortion"] ?: @0.0;
        NSNumber *reverb = profile[@"reverb"] ?: @0.0;
        
        // Apply pitch shifting
        self.pitchEffect.pitch = [pitchShift floatValue] * 1200.0; // Convert to cents
        
        // Apply distortion
        self.distortionEffect.preGain = [distortion floatValue] * 50.0;
        
        // Apply reverb
        [self.reverbEffect loadFactoryPreset:AVAudioUnitReverbPresetLargeChamber];
        self.reverbEffect.wetDryMix = [reverb floatValue] * 100.0;
        
        [self sendEventWithName:@"onVoiceMorphingApplied" body:profile];
        resolve(@{@"success": @YES, @"profile": profile});
    }
    @catch (NSException *exception) {
        reject(@"VOICE_MORPHING_ERROR", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(startPlayback:(NSString *)filePath
                  withProfile:(NSDictionary *)profile
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        NSError *error;
        NSURL *fileURL = [NSURL fileURLWithPath:filePath];
        
        // Apply voice morphing profile
        [self applyVoiceMorphing:profile resolver:^(id result) {
            // Voice morphing applied successfully
        } rejecter:^(NSString *code, NSString *message, NSError *error) {
            RCTLogWarn(@"Failed to apply voice morphing: %@", message);
        }];
        
        // Start audio engine
        if (!self.audioEngine.isRunning) {
            [self.audioEngine startAndReturnError:&error];
            if (error) {
                reject(@"AUDIO_ENGINE_START_ERROR", error.localizedDescription, error);
                return;
            }
        }
        
        // Load and schedule audio file
        AVAudioFile *audioFile = [[AVAudioFile alloc] initForReading:fileURL error:&error];
        if (error) {
            reject(@"AUDIO_FILE_ERROR", error.localizedDescription, error);
            return;
        }
        
        [self.playerNode scheduleFile:audioFile atTime:nil completionHandler:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self sendEventWithName:@"onPlaybackStopped" body:@{@"filePath": filePath}];
            });
        }];
        
        [self.playerNode play];
        [self sendEventWithName:@"onPlaybackStarted" body:@{@"filePath": filePath}];
        resolve(@{@"success": @YES, @"message": @"Playback started with voice morphing"});
    }
    @catch (NSException *exception) {
        reject(@"PLAYBACK_EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(stopPlayback:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        [self.playerNode stop];
        [self sendEventWithName:@"onPlaybackStopped" body:@{}];
        resolve(@{@"success": @YES, @"message": @"Playback stopped"});
    }
    @catch (NSException *exception) {
        reject(@"STOP_PLAYBACK_EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(testVoiceProfile:(NSDictionary *)profile
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    // Test voice profile by applying effects and playing a test tone
    @try {
        [self applyVoiceMorphing:profile resolver:^(id result) {
            resolve(@{
                @"success": @YES, 
                @"message": @"Voice profile tested successfully",
                @"profile": profile
            });
        } rejecter:reject];
    }
    @catch (NSException *exception) {
        reject(@"VOICE_PROFILE_TEST_ERROR", exception.reason, nil);
    }
}

// AVAudioRecorderDelegate methods
- (void)audioRecorderDidFinishRecording:(AVAudioRecorder *)recorder successfully:(BOOL)flag
{
    if (flag) {
        RCTLogInfo(@"Recording finished successfully");
    } else {
        RCTLogError(@"Recording failed");
    }
}

- (void)audioRecorderEncodeErrorDidOccur:(AVAudioRecorder *)recorder error:(NSError *)error
{
    RCTLogError(@"Recording encode error: %@", error.localizedDescription);
}

@end
