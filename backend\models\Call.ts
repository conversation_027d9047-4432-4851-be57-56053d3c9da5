import mongoose, { Document, Schema } from 'mongoose';

// Call model for voice calls with BLE authentication and recording
export interface ICall extends Document {
  callId: string;
  initiatorId: mongoose.Types.ObjectId;
  recipientId: mongoose.Types.ObjectId;
  status: 'initiated' | 'ringing' | 'active' | 'ended' | 'failed' | 'rejected';
  callType: 'voice' | 'emergency';
  
  // BLE Authentication
  bleAuth: {
    initiatorBLE: string; // BLE UUID of initiator's device
    recipientBLE?: string; // BLE UUID of recipient's device
    authStatus: 'pending' | 'verified' | 'failed';
    verificationTime?: Date;
  };
  
  // Call timing
  initiatedAt: Date;
  answeredAt?: Date;
  endedAt?: Date;
  duration?: number; // in seconds
  
  // Recording and modulation
  recording: {
    enabled: boolean;
    encryptedPath?: string;
    originalPath?: string;
    modulatedPath?: string; // For voice anonymization
    fileSize?: number;
    transcription?: string; // Optional transcription
  };
  
  // Voice modulation settings
  modulation: {
    enabled: boolean;
    type?: 'deepening' | 'pitch_shift' | 'robotic' | 'anonymize';
    strength?: number; // 1-10 scale
    presetName?: string;
  };
  
  // Call quality and metadata
  quality: {
    signalStrength?: number;
    bitrate?: number;
    packetLoss?: number;
    latency?: number;
  };
  
  // Security and audit
  deviceFingerprints: {
    initiator: string;
    recipient?: string;
  };
  
  ipAddresses: {
    initiator: string;
    recipient?: string;
  };
  
  endReason?: 'normal' | 'timeout' | 'error' | 'rejected' | 'ble_auth_failed';
  errorDetails?: string;
  
  createdAt: Date;
  updatedAt: Date;
}

const CallSchema = new Schema<ICall>(
  {
    callId: { type: String, required: true, unique: true },
    initiatorId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    recipientId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    status: {
      type: String,
      enum: ['initiated', 'ringing', 'active', 'ended', 'failed', 'rejected'],
      default: 'initiated',
    },
    callType: {
      type: String,
      enum: ['voice', 'emergency'],
      default: 'voice',
    },
    
    // BLE Authentication
    bleAuth: {
      initiatorBLE: { type: String, required: true },
      recipientBLE: String,
      authStatus: {
        type: String,
        enum: ['pending', 'verified', 'failed'],
        default: 'pending',
      },
      verificationTime: Date,
    },
    
    // Call timing
    initiatedAt: { type: Date, default: Date.now },
    answeredAt: Date,
    endedAt: Date,
    duration: Number,
    
    // Recording and modulation
    recording: {
      enabled: { type: Boolean, default: true },
      encryptedPath: String,
      originalPath: String,
      modulatedPath: String,
      fileSize: Number,
      transcription: String,
    },
    
    modulation: {
      enabled: { type: Boolean, default: false },
      type: {
        type: String,
        enum: ['deepening', 'pitch_shift', 'robotic', 'anonymize'],
      },
      strength: { type: Number, min: 1, max: 10 },
      presetName: String,
    },
    
    quality: {
      signalStrength: Number,
      bitrate: Number,
      packetLoss: Number,
      latency: Number,
    },
    
    deviceFingerprints: {
      initiator: { type: String, required: true },
      recipient: String,
    },
    
    ipAddresses: {
      initiator: { type: String, required: true },
      recipient: String,
    },
    
    endReason: {
      type: String,
      enum: ['normal', 'timeout', 'error', 'rejected', 'ble_auth_failed'],
    },
    errorDetails: String,
  },
  { timestamps: true }
);

// Indexes for performance and queries
CallSchema.index({ initiatorId: 1, createdAt: -1 });
CallSchema.index({ recipientId: 1, createdAt: -1 });
CallSchema.index({ callId: 1 });
CallSchema.index({ status: 1, createdAt: -1 });
CallSchema.index({ 'bleAuth.authStatus': 1 });

export default mongoose.model<ICall>('Call', CallSchema);
