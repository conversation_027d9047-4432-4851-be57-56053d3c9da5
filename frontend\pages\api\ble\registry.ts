// Proxy for BLE registry API to backend
import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  const url = `${backendUrl}/api/ble/registry`;
  try {
    if (req.method === 'GET') {
      const response = await axios.get(url, { headers: req.headers });
      return res.status(response.status).json(response.data);
    }
    if (req.method === 'POST') {
      const response = await axios.post(url, req.body, { headers: req.headers });
      return res.status(response.status).json(response.data);
    }
    if (req.method === 'DELETE') {
      const response = await axios.delete(url, { params: req.query, headers: req.headers });
      return res.status(response.status).json(response.data);
    }
    res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  } catch (error: any) {
    if (axios.isAxiosError(error) && error.response) {
      return res.status(error.response.status).json(error.response.data);
    }
    res.status(500).json({ error: 'Proxy error' });
  }
}
