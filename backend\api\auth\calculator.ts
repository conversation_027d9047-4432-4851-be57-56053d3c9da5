/**
 * Calculator Authentication API
 * Handles authentication through mathematical expressions + device fingerprinting
 */

import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import DeviceModel from '../../models/Device';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import mathValidator from '../../utils/mathValidator';

const router = Router();

interface CalculatorAuthRequest {
  expression: string;
  deviceFingerprint: string;
  timestamp: number;
}

interface AuthenticatedUser {
  id: string;
  username: string;
  isSuperuser: boolean;
  deviceId: string;
}

/**
 * Calculator Authentication Endpoint
 * POST /api/auth/calculator-auth
 * 
 * This is the main authentication flow for the mobile app
 * When user enters a valid mathematical expression in calculator and taps "="
 */
router.post('/calculator-auth', async (req: Request, res: Response): Promise<void> => {
  try {
    const { expression, deviceFingerprint, timestamp }: CalculatorAuthRequest = req.body;

    console.log('🧮 Calculator authentication request:', { 
      expression: expression?.substring(0, 50) + '...', 
      deviceFingerprint: deviceFingerprint?.substring(0, 20) + '...',
      timestamp 
    });

    // Validate input
    if (!expression || !deviceFingerprint || !timestamp) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: expression, deviceFingerprint, timestamp'
      });
      return;
    }

    // Check timestamp (prevent replay attacks)
    const now = Date.now();
    const timeDiff = Math.abs(now - timestamp);
    if (timeDiff > 300000) { // 5 minutes
      console.log('❌ Request timestamp too old:', timeDiff);
      res.status(400).json({
        success: false,
        error: 'Request timestamp invalid'
      });
      return;
    }

    // Step 1: Validate mathematical expression
    const validationResult = mathValidator.validateMathematicalExpression(expression);
    
    if (!validationResult.isValid) {
      console.log('❌ Expression validation failed:', validationResult.error);
      res.status(400).json({
        success: false,
        error: 'Invalid mathematical expression',
        details: validationResult.error
      });
      return;
    }

    console.log('✅ Expression validation passed');

    // Step 2: Check if device is registered and authorized
    const device = await DeviceModel.findOne({ fingerprint: deviceFingerprint });
    
    if (!device) {
      console.log('❌ Device not found:', deviceFingerprint.substring(0, 20));
      
      // Log unauthorized access attempt
      await AuditLogModel.create({
        action: 'CALCULATOR_AUTH_UNKNOWN_DEVICE',
        details: {
          deviceFingerprint: deviceFingerprint.substring(0, 20) + '...',
          expression: expression.substring(0, 50),
          timestamp,
          ipAddress: req.ip
        },
        severity: 'high',
        category: 'security'
      });

      res.status(401).json({
        success: false,
        error: 'Device not authorized'
      });
      return;
    }

    // Step 3: Check if device is active
    if (!device.isActive) {
      console.log('❌ Device is inactive:', device.deviceId);
      
      await AuditLogModel.create({
        action: 'CALCULATOR_AUTH_INACTIVE_DEVICE',
        userId: device.userId,
        details: {
          deviceId: device.deviceId,
          deviceFingerprint: deviceFingerprint.substring(0, 20) + '...',
          expression: expression.substring(0, 50)
        },
        severity: 'medium',
        category: 'security'
      });

      res.status(401).json({
        success: false,
        error: 'Device is not active'
      });
      return;
    }

    // Step 4: Get user associated with device
    const user = await UserModel.findById(device.userId);
    
    if (!user || user.status !== 'active') {
      console.log('❌ User not found or inactive:', device.userId);
      
      await AuditLogModel.create({
        action: 'CALCULATOR_AUTH_INVALID_USER',
        userId: device.userId,
        details: {
          deviceId: device.deviceId,
          userStatus: user?.status || 'not_found'
        },
        severity: 'high',
        category: 'security'
      });

      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    console.log('✅ Device and user validation passed');

    // Step 5: Check expression complexity (additional security layer)
    const complexity = mathValidator.calculateExpressionComplexity(expression);
    
    if (complexity < 2) {
      console.log('❌ Expression too simple, complexity:', complexity);
      res.status(400).json({
        success: false,
        error: 'Expression complexity insufficient for authentication'
      });
      return;
    }    // Step 6: Generate JWT token for authenticated session
    const tokenPayload: AuthenticatedUser = {
      id: (user._id as any).toString(),
      username: user.username,
      isSuperuser: user.isSuperuser || false,
      deviceId: device.deviceId
    };

    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || 'your-secret-key',
      { 
        expiresIn: '7d', // 7 days for mobile app
        issuer: 'ccalc-calculator-auth',
        audience: 'ccalc-mobile-app'
      }
    );

    // Step 7: Update device last activity
    await DeviceModel.findByIdAndUpdate(device._id, {
      'metadata.lastActiveAt': new Date(),
      'metadata.lastLoginMethod': 'calculator_expression',
      'metadata.lastAuthExpression': {
        complexity,
        timestamp: new Date()
      }
    });

    // Step 8: Log successful authentication
    await AuditLogModel.create({
      action: 'CALCULATOR_AUTH_SUCCESS',
      userId: user._id,
      details: {
        deviceId: device.deviceId,
        username: user.username,
        expressionComplexity: complexity,
        isSuperuser: user.isSuperuser,
        authMethod: 'calculator_expression'
      },
      severity: 'low',
      category: 'authentication',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    console.log('🎉 Calculator authentication successful for user:', user.username);    res.json({
      success: true,
      token,
      user: {
        id: (user._id as any).toString(),
        username: user.username,
        isSuperuser: user.isSuperuser || false
      },
      device: {
        id: device.deviceId,
        lastActive: device.metadata.lastActiveAt
      },
      session: {
        expiresIn: '7d',
        authMethod: 'calculator_expression'
      }
    });

  } catch (error: any) {
    console.error('Calculator authentication error:', error);
    
    // Log system error
    await AuditLogModel.create({
      action: 'CALCULATOR_AUTH_SYSTEM_ERROR',
      details: {
        error: error.message,
        stack: error.stack?.substring(0, 500)
      },
      severity: 'high',
      category: 'system'
    });

    res.status(500).json({
      success: false,
      error: 'Authentication system error'
    });
  }
});

/**
 * Token Verification Endpoint
 * GET /api/auth/verify
 * 
 * Verifies if the JWT token is still valid
 */
router.get('/verify', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
      return;
    }

    // Check if user still exists and is active
    const user = await UserModel.findById(userId);
    
    if (!user || user.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account no longer valid'
      });
      return;
    }    res.json({
      success: true,
      user: {
        id: (user._id as any).toString(),
        username: user.username,
        isSuperuser: user.isSuperuser || false
      },
      tokenValid: true
    });

  } catch (error: any) {
    console.error('Token verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Token verification failed'
    });
  }
});

/**
 * Device Registration Endpoint
 * POST /api/device/register
 * 
 * Registers a new device for calculator authentication
 */
router.post('/device/register', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { fingerprint, deviceInfo } = req.body;

    if (!adminId) {
      res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
      return;
    }

    if (!fingerprint || !deviceInfo) {
      res.status(400).json({
        success: false,
        error: 'Missing fingerprint or device info'
      });
      return;
    }

    // Check if device already exists
    const existingDevice = await DeviceModel.findOne({ fingerprint });
    
    if (existingDevice) {
      res.status(409).json({
        success: false,
        error: 'Device already registered'
      });
      return;
    }

    // Create new device
    const deviceId = crypto.randomUUID();
    const device = await DeviceModel.create({
      deviceId,
      fingerprint,
      userId: null, // Will be assigned when user logs in first time
      metadata: {
        registeredAt: new Date(),
        registeredBy: adminId,
        deviceInfo,
        isActive: true,
        lastActiveAt: new Date()
      }
    });

    await AuditLogModel.create({
      action: 'DEVICE_REGISTERED',
      adminId,
      details: {
        deviceId,
        deviceInfo
      },
      severity: 'low',
      category: 'device_management'
    });

    res.json({
      success: true,
      device: {
        id: deviceId,
        registered: true
      }
    });

  } catch (error: any) {
    console.error('Device registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Device registration failed'
    });
  }
});

export default router;
