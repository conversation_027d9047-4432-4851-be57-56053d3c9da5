# CCALC iOS - Xcode Setup Guide

This directory contains the iOS native code for the CCALC React Native application, configured for optimal Xcode development experience.

## 🏗️ Project Structure

```
ios/
├── CCALC.xcworkspace          # 👈 OPEN THIS IN XCODE
├── CCALC.xcodeproj/
│   ├── project.pbxproj
│   └── xcshareddata/
│       └── xcschemes/
│           └── CCALC.xcscheme # Shared build scheme
├── CCALC/                     # Main app target
│   ├── AppDelegate.h/.mm      # App entry point
│   ├── CCLCVoice.h/.m         # Custom voice processing module
│   ├── Info.plist             # App configuration
│   ├── LaunchScreen.storyboard
│   ├── Images.xcassets/       # App icons and assets
│   └── main.m                 # iOS app main function
├── CCCALCTests/               # Unit tests
├── Podfile                    # CocoaPods dependencies
└── .xcode.env                 # Environment variables for Xcode
```

## 🚀 Quick Start

### Prerequisites
- Xcode 14.0 or later
- CocoaPods (`sudo gem install cocoapods`)
- Node.js and npm/yarn

### Setup Steps

1. **Install dependencies:**
   ```bash
   cd ../  # Go to app root
   npm install
   cd ios
   pod install
   ```

2. **Open in Xcode:**
   ```bash
   open CCALC.xcworkspace
   ```
   ⚠️ **Always use `.xcworkspace`, never `.xcodeproj`**

3. **Run the app:**
   - Select a simulator or device
   - Press `Cmd+R` to build and run
   - Or use: `npx react-native run-ios`

## 🧪 Testing

- **Unit Tests:** Press `Cmd+U` in Xcode
- **Integration Tests:** Use `npx react-native run-ios` with Metro running

## 🔧 Development Features

### Custom Native Modules
- **CCLCVoice**: Voice processing and audio manipulation
  - Location: `CCALC/CCLCVoice.h/.m`
  - Features: Recording, playback, voice morphing
  - Dependencies: AVFoundation framework

### Build Configurations
- **Debug**: Development builds with debugging enabled
- **Release**: Production builds with optimizations

### Schemes
- **CCALC**: Main app scheme (shared for team development)
- **CCCALCTests**: Unit test scheme

## 📱 Device Testing

1. **Connect iOS device via USB**
2. **Select device in Xcode scheme selector**
3. **Configure code signing:**
   - Go to Project Settings → CCALC target → Signing & Capabilities
   - Select your Apple Developer account
   - Choose appropriate provisioning profile

## 🛠️ Troubleshooting

### Common Issues

1. **"No scheme found" error:**
   - Make sure you opened `.xcworkspace` not `.xcodeproj`
   - Check that `CCALC.xcscheme` exists in `xcshareddata/xcschemes/`

2. **Build errors related to Pods:**
   ```bash
   pod deintegrate
   pod install
   ```

3. **React Native bundle errors:**
   - Start Metro bundler: `npm start` in app root
   - Clear cache: `npx react-native start --reset-cache`

4. **Code signing issues:**
   - Check Apple Developer account in Xcode preferences
   - Verify bundle identifier matches your provisioning profile

### Clean Build
```bash
# Clean everything
rm -rf node_modules ios/Pods ios/Podfile.lock
npm install
cd ios && pod install
```

## 🔗 Integration with React Native

### Metro Bundler
- The app requires Metro bundler to be running
- Start with: `npm start` in the app root directory

### Hot Reloading
- Enabled by default in Debug configuration
- Shake device or press `Cmd+D` for developer menu

### Custom Native Bridge
The project includes a custom Objective-C bridge module (`CCLCVoice`) that provides:
- Audio recording capabilities
- Voice playback with effects
- Real-time voice morphing
- Integration with React Native JavaScript layer

## 📋 Build Settings

### Key Configurations
- **Deployment Target**: iOS 13.4+
- **Bundle Identifier**: `com.ccalc.app`
- **JavaScript Engine**: Hermes (enabled)
- **Architecture**: arm64 (device), x86_64 (simulator)

### Required Permissions
- Camera access (`NSCameraUsageDescription`)
- Microphone access (`NSMicrophoneUsageDescription`)
- Photo library access (`NSPhotoLibraryUsageDescription`)

## 🎯 EAS Build Compatibility

This project is configured for Expo EAS Build:
- Shared scheme ensures cloud building works
- All native dependencies properly configured
- Custom native code included in builds

## 📚 Additional Resources

- [React Native iOS Guide](https://reactnative.dev/docs/running-on-device)
- [Xcode Documentation](https://developer.apple.com/xcode/)
- [CocoaPods Guide](https://guides.cocoapods.org/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)

---

**Need Help?** Check the troubleshooting section above or consult the main project README.
