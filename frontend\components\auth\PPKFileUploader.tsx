import React, { useState, useRef } from 'react';
import { Box, Button, Typography, TextField, Alert, Paper, CircularProgress } from '@mui/material';
import { analyzePEMKey } from '../../utils/pemAnalyzer';
import { importPrivate<PERSON>ey, signChallenge } from '../../utils/ppkAuth';
import { formatErrorForUser } from '../../utils/ppkErrorHandler';

// IMMEDIATELY LOG TO CONFIRM CODE EXECUTION
console.log('PPK FILE UPLOADER COMPONENT LOADED - VERSION WITH DEBUG LOGS');

interface PPKFileUploaderProps {
  onKeyLoaded: (privateKey: CryptoKey) => void;
  onError: (error: string) => void;
  challenge?: string;
  onSignature?: (signature: string) => void;
}

const PPKFileUploader: React.FC<PPKFileUploaderProps> = ({ 
  onKeyLoaded, 
  onError, 
  challenge,
  onSignature 
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [passphrase, setPassphrase] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [keyInfo, setKeyInfo] = useState<{ isValid: boolean; message: string; isEncrypted: boolean } | null>(null);
  const [privateKey, setPrivateKey] = useState<CryptoKey | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setLoading(true);
    console.log('PPK DEBUG: File selection started');
    try {
      const selectedFile = event.target.files?.[0];
      if (!selectedFile) {
        setFile(null);
        setKeyInfo(null);
        console.log('PPK DEBUG: No file selected');
        return;
      }

      setFile(selectedFile);
      console.log('PPK DEBUG: File selected', { name: selectedFile.name, size: selectedFile.size });

      // Read file content
      const fileContent = await readFileAsText(selectedFile);
      console.log('PPK DEBUG: File content read, length:', fileContent.length);
      
      // Analyze the key
      const analysis = analyzePEMKey(fileContent);
      setKeyInfo(analysis);
      console.log('PPK DEBUG: PEM analysis', analysis);
      if (!analysis.isValid) {
        onError(analysis.message);
        return;
      }

      if (!analysis.isEncrypted) {        try {
          const key = await importPrivateKey(fileContent);
          setPrivateKey(key);
          onKeyLoaded(key);
          console.log('PPK DEBUG: Private key imported', key);
        } catch (error) {
          const errorMessage = error && typeof error === 'object' && 'userMessage' in error 
            ? formatErrorForUser(error as any)
            : `Failed to import key: ${error instanceof Error ? error.message : 'Unknown error'}`;
          onError(errorMessage);
        }
      }
    } catch (error) {
      onError(`Error processing file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePassphraseSubmit = async () => {
    if (!file) return;
    
    setLoading(true);
    try {
      const fileContent = await readFileAsText(file);
      const key = await importPrivateKey(fileContent, passphrase);
      setPrivateKey(key);
      onKeyLoaded(key);
    } catch (error) {
      onError(`Failed to decrypt key: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSignChallenge = async () => {
    if (!privateKey || !challenge || !onSignature) return;
    
    setLoading(true);
    try {
      console.log('PPK DEBUG: Signing challenge', { challenge, privateKey });
      const signature = await signChallenge(challenge, privateKey);
      console.log('PPK DEBUG: Signature generated', signature);
      onSignature(signature);
    } catch (error) {
      onError(`Failed to sign challenge: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const resetFile = () => {
    setFile(null);
    setKeyInfo(null);
    setPrivateKey(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        PPK Authentication
      </Typography>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="outlined"
          component="label"
          disabled={loading}
        >
          Upload Private Key
          <input
            ref={fileInputRef}
            type="file"
            accept=".pem,.key"
            hidden
            onChange={handleFileChange}
          />
        </Button>
        {file && (
          <Button 
            variant="text" 
            color="secondary" 
            onClick={resetFile} 
            sx={{ ml: 1 }}
            disabled={loading}
          >
            Reset
          </Button>
        )}
      </Box>

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress size={24} />
        </Box>
      )}

      {file && keyInfo && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2">
            File: {file.name}
          </Typography>
          <Alert severity={keyInfo.isValid ? "success" : "error"} sx={{ mt: 1 }}>
            {keyInfo.message}
          </Alert>
        </Box>
      )}

      {keyInfo?.isEncrypted && (
        <Box sx={{ mt: 2, mb: 3 }}>
          <TextField
            label="Passphrase"
            type="password"
            fullWidth
            value={passphrase}
            onChange={(e) => setPassphrase(e.target.value)}
            disabled={loading}
            sx={{ mb: 1 }}
          />
          <Button 
            variant="contained" 
            onClick={handlePassphraseSubmit}
            disabled={loading || !passphrase}
          >
            Decrypt Key
          </Button>
        </Box>
      )}

      {privateKey && challenge && onSignature && (
        <Box sx={{ mt: 3 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSignChallenge}
            disabled={loading}
          >
            Sign Challenge
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default PPKFileUploader;
