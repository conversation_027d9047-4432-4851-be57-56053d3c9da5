const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const crypto = require('crypto');

// Load environment variables
require('dotenv').config();

// MongoDB connection string
const mongoUri = process.env.MONGODB_URI || '*************************************************************';

// User schema (simplified version for this script)
const UserSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true, trim: true },
  email: { type: String, trim: true },
  expressionHash: { type: String, required: true },
  unlockExpression: { type: String },
  expressionType: { 
    type: String, 
    enum: ['calculator', 'pattern'], 
    default: 'calculator' 
  },
  expressionUpdatedAt: { type: Date },
  profile: {
    displayName: { type: String, required: true },
  },
  deviceFingerprintHash: { type: String },
  deviceMetadata: {
    model: { type: String },
    os: { type: String },
    registeredAt: { type: Date },
    registrationCoords: {
      lat: { type: Number },
      lng: { type: Number },
    },
  },
  bleUUIDHash: { type: String },
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked', 'pending_device_registration'],
    default: 'pending_device_registration',
  },
  lastLoginAt: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockUntil: { type: Date },
  builds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Build' }],
  messageExpiry: { type: Date },
  isSuperuser: { type: Boolean, default: false },
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function createTestUser() {
  try {
    console.log('Connecting to MongoDB...');
    console.log('URI:', mongoUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
    
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // Test user data
    const testUsername = 'testuser';
    const testExpression = '2+3*4'; // Should evaluate to 14
    const testDisplayName = 'Test User';

    // Check if user already exists
    const existingUser = await User.findOne({ username: testUsername });
    if (existingUser) {
      console.log(`User '${testUsername}' already exists. Deleting first...`);
      await User.deleteOne({ username: testUsername });
    }

    // Hash the expression using bcrypt (to match authentication controller)
    const expressionHash = await bcrypt.hash(testExpression, 10);

    // Create the user
    const newUser = new User({
      username: testUsername,
      expressionHash: expressionHash,
      unlockExpression: testExpression, // Store plain text for admin viewing
      expressionType: 'calculator',
      expressionUpdatedAt: new Date(),
      profile: {
        displayName: testDisplayName
      },
      status: 'pending_device_registration', // Default status for new users
      failedLoginAttempts: 0,
      isSuperuser: false
    });

    const savedUser = await newUser.save();
    console.log('\n✅ Test user created successfully!');
    console.log('User details:');
    console.log(`  ID: ${savedUser._id}`);
    console.log(`  Username: ${savedUser.username}`);
    console.log(`  Display Name: ${savedUser.profile.displayName}`);
    console.log(`  Status: ${savedUser.status}`);
    console.log(`  Expression: ${savedUser.unlockExpression}`);
    console.log(`  Expression Type: ${savedUser.expressionType}`);
    console.log(`  Created At: ${savedUser.createdAt}`);
    console.log(`  Device Fingerprint: ${savedUser.deviceFingerprintHash || 'Not set (pending registration)'}`);
    console.log(`  BLE UUID: ${savedUser.bleUUIDHash || 'Not set (pending registration)'}`);

    console.log('\n📱 Mobile App Login Credentials:');
    console.log(`Username: ${testUsername}`);
    console.log(`Expression: ${testExpression}`);
    console.log('\n💡 Next Steps:');
    console.log('1. Use these credentials to log in from the mobile app');
    console.log('2. During first login, the app should provide device fingerprint and BLE UUID');
    console.log('3. The backend will register the device and change status to "active"');
    console.log('4. Future logins will require the same device fingerprint and BLE UUID');

  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

createTestUser();
