const mongoose = require('mongoose');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema
const UserSchema = new mongoose.Schema({
  username: String,
  status: String,
  lockUntil: Date,
  failedLoginAttempts: Number,
  unlockExpression: String,
  expressionHash: String,
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function unlockAccount() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // Unlock the account
    const result = await User.updateOne(
      { username: 'mobileuser' },
      { 
        $unset: { 
          lockUntil: "",
          deviceFingerprintHash: "",
          bleUUIDHash: "",
          deviceMetadata: ""
        },
        $set: { 
          status: 'pending_device_registration',
          failedLoginAttempts: 0
        }
      }
    );

    console.log('✅ Account unlocked and reset');

    // Check current user state
    const user = await User.findOne({ username: 'mobileuser' });
    if (user) {
      console.log('\n📱 UNLOCKED - Ready for Testing:');
      console.log(`Username: ${user.username}`);
      console.log(`Expression: ${user.unlockExpression}`);
      console.log(`Status: ${user.status}`);
      console.log(`Failed Attempts: ${user.failedLoginAttempts}`);
      
      console.log('\n🎯 IMPORTANT: Enter this EXACT expression:');
      console.log(`"${user.unlockExpression}"`);
      console.log('\n💡 Expression breakdown:');
      console.log('12 + 5*7 - 3*2 + 8/4');
      console.log('= 12 + 35 - 6 + 2');
      console.log('= 43');
      console.log('\nBut authenticate with the EXPRESSION, not the result!');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

unlockAccount();
