const mongoose = require('mongoose');

// Connect to MongoDB
async function testAuditLog() {
  try {
    await mongoose.connect('mongodb://localhost:27017/ccalc_dev');
    console.log('Connected to MongoDB');    // Import AuditLog model
    const AuditLogModel = require('./models/AuditLog.ts').default;
    
    console.log('Testing AuditLog creation...');
    
    // Test creating a proper AuditLog
    const testLog = await AuditLogModel.create({
      logId: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      event: {
        type: 'system_action',
        action: 'test_action',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: '127.0.0.1',
        userAgent: 'test-script',
        endpoint: '/test',
        method: 'GET'
      },
      compliance: {
        category: 'system',
        retention: 'short',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { test: true }
      }
    });
    
    console.log('AuditLog created successfully:', testLog._id);
    
  } catch (error) {
    console.error('AuditLog creation failed:', error.message);
    console.error('Validation errors:', error.errors);
  } finally {
    await mongoose.disconnect();
  }
}

testAuditLog();
