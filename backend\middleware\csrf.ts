// CSRF Protection Middleware
import { Request, Response, NextFunction } from 'express';
import { validateCsrfToken } from '../utils/csrf-protection';
import securityLogger from '../utils/security-logger';

/**
 * CSRF protection middleware for authenticated routes
 * Requires a valid CSRF token in the request headers
 * 
 * Note: This middleware should be used after the auth middleware
 * as it depends on req.user or req.admin being set
 */
export const csrfProtection = (req: Request, res: Response, next: NextFunction): void => {
  // Skip for GET, HEAD, OPTIONS requests (they should be idempotent)
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    next();
    return;
  }
  
  const csrfToken = req.headers['x-csrf-token'] as string;
    if (!csrfToken) {
    securityLogger.logSecurityEvent({
      eventType: 'security_change',
      userType: req.admin ? 'admin' : 'user',
      username: req.admin?.username || req.user?.username || 'unknown',
      userId: req.admin?._id?.toString() || req.user?._id?.toString() || 'unknown',
      ipAddress: req.ip || req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      success: false,
      reason: 'Missing CSRF token',
      details: { 
        path: req.path,
        method: req.method
      }
    });
    
    res.status(403).json({ error: 'CSRF token missing' });
    return;
  }
  
  // Get user ID from the authenticated request
  const userId = req.admin?._id || req.user?._id;
  
  if (!userId) {
    res.status(403).json({ error: 'Authentication required' });
    return;
  }
  
  // Validate the token
  if (!validateCsrfToken(userId.toString(), csrfToken)) {
    securityLogger.logSecurityEvent({
      eventType: 'security_change',
      userType: req.admin ? 'admin' : 'user',
      username: req.admin?.username || req.user?.username || 'unknown',
      userId: userId.toString(),
      ipAddress: req.ip || req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      success: false,
      reason: 'Invalid CSRF token',
      details: { 
        path: req.path,
        method: req.method 
      }
    });
    
    res.status(403).json({ error: 'Invalid CSRF token' });
    return;
  }
  
  // Token is valid, proceed
  next();
};

export default csrfProtection;
