/**
 * Debug utility for PPK file formats
 */

/**
 * Analyze a key file content and provide detailed information
 * @param content The file content to analyze
 * @returns An analysis of the key file
 */
export function analyzeKeyFile(content: string): {
  format: string;
  isValid: boolean;
  details: string;
} {
  // Check for empty content
  if (!content || content.trim() === '') {
    return {
      format: 'unknown',
      isValid: false,
      details: 'File is empty'
    };
  }
  
  // Check file header to identify format
  const lines = content.split('\n');
  const firstLine = lines[0].trim();
  
  // PuTTY PPK format
  if (firstLine.includes('PuTTY-User-Key-File')) {
    return {
      format: 'putty-ppk',
      isValid: true,
      details: 'PuTTY Private Key format (.ppk) - Requires conversion to PEM using PuTTYgen'
    };
  }
  
  // OpenSSH format
  if (content.includes('-----BEGIN OPENSSH PRIVATE KEY-----')) {
    return {
      format: 'openssh',
      isValid: true,
      details: 'OpenSSH Private Key format - Requires conversion to PEM using ssh-keygen'
    };
  }
  
  // Standard PEM formats
  if (content.includes('-----BEGIN PRIVATE KEY-----')) {
    return {
      format: 'pem-pkcs8',
      isValid: true,
      details: 'PKCS#8 PEM format - Compatible with Web Crypto API'
    };
  }
  
  if (content.includes('-----BEGIN RSA PRIVATE KEY-----')) {
    return {
      format: 'pem-pkcs1',
      isValid: true,
      details: 'PKCS#1 PEM format - May require conversion to PKCS#8'
    };
  }
  
  if (content.includes('-----BEGIN ENCRYPTED PRIVATE KEY-----')) {
    return {
      format: 'pem-encrypted',
      isValid: true,
      details: 'Encrypted PEM format - Requires passphrase'
    };
  }
  
  // Try to identify if it's a base64 encoded block
  const hasBase64Chars = /^[A-Za-z0-9+/=\s]+$/g.test(content);
  if (hasBase64Chars) {
    return {
      format: 'raw-base64',
      isValid: false,
      details: 'Raw base64-encoded data without proper PEM headers - Not usable directly'
    };
  }
  
  // Unknown format
  return {
    format: 'unknown',
    isValid: false,
    details: 'Unrecognized key format'
  };
}

/**
 * Try to extract a PKCS8 compatible private key from various formats
 * @param content The key file content
 * @returns The extracted key in PKCS8 format, or null if extraction failed
 */
export function extractPKCS8Key(content: string): string | null {
  try {
    // Already PKCS#8
    if (content.includes('-----BEGIN PRIVATE KEY-----')) {
      return content;
    }
    
    // PKCS#1 RSA format
    if (content.includes('-----BEGIN RSA PRIVATE KEY-----')) {
      // We can't convert PKCS#1 to PKCS#8 in the browser easily
      // In a real implementation, this would require ASN.1 parsing
      console.warn('PKCS#1 format detected - should be converted server-side');
      return null;
    }
    
    // For all other formats, we can't easily extract the key
    return null;
  } catch (error) {
    console.error('Error extracting PKCS#8 key:', error);
    return null;
  }
}
