import React, { useState, useEffect } from 'react';
import { signChallenge, importPrivateKey } from '../utils/ppkAuth';

interface PPKUploadProps {
  challenge: string;
  onPPKReady: (challenge: string, signature: string, privateKey: CryptoKey) => void;
  onPPKError: (errorMsg: string) => void;
  disabled?: boolean;
  className?: string;
}

const PPKUpload: React.FC<PPKUploadProps> = ({ 
  challenge, 
  onPPKReady, 
  onPPKError, 
  disabled = false,
  className = ''
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);

  const processFile = async (file: File) => {
    if (!file) {
      onPPKError('No file selected');
      return;
    }

    if (!file.name.endsWith('.pem')) {
      onPPKError('Please select a .pem file');
      return;
    }

    setIsProcessing(true);
    setUploadedFileName(file.name);    try {
      const fileContent = await file.text();
      
      // Import the private key using the utility function
      const privateKey = await importPrivateKey(fileContent);
      
      // Sign the challenge if we have one
      if (challenge && challenge.trim()) {
        const signature = await signChallenge(challenge, privateKey);
        onPPKReady(challenge, signature, privateKey);
      } else {
        onPPKError('No challenge available for signing');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process key file';
      onPPKError(errorMessage);
      setUploadedFileName(null);
    } finally {
      setIsProcessing(false);
    }
  };
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    
    const file = event.dataTransfer.files[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  return (
    <div className={`ppk-upload ${className}`}>
      <div
        className={`ppk-upload-area ${isDragOver ? 'drag-over' : ''} ${disabled ? 'disabled' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          type="file"
          id="ppk-file-input"
          accept=".pem"
          onChange={handleFileSelect}
          disabled={disabled || isProcessing}
          style={{ display: 'none' }}
        />
        
        <label htmlFor="ppk-file-input" className="ppk-upload-label">
          {isProcessing ? (
            <div className="ppk-processing">
              <div className="spinner"></div>
              <span>Processing {uploadedFileName}...</span>
            </div>
          ) : uploadedFileName ? (
            <div className="ppk-uploaded">
              <span>📄 {uploadedFileName}</span>
              <small>Click to change file</small>
            </div>
          ) : (
            <div className="ppk-upload-prompt">
              <div className="ppk-upload-icon">🔑</div>
              <div className="ppk-upload-text">
                <strong>Upload Private Key (.pem)</strong>
                <small>Drag and drop or click to select your private key file</small>
              </div>
            </div>
          )}
        </label>
      </div>

      <style jsx>{`
        .ppk-upload {
          margin: 1rem 0;
        }

        .ppk-upload-area {
          border: 2px dashed #ddd;
          border-radius: 8px;
          padding: 2rem;
          text-align: center;
          transition: all 0.3s ease;
          background-color: #fafafa;
          cursor: pointer;
        }

        .ppk-upload-area:hover:not(.disabled) {
          border-color: #007bff;
          background-color: #f0f8ff;
        }

        .ppk-upload-area.drag-over {
          border-color: #007bff;
          background-color: #e3f2fd;
        }

        .ppk-upload-area.disabled {
          opacity: 0.6;
          cursor: not-allowed;
          background-color: #f5f5f5;
        }

        .ppk-upload-label {
          display: block;
          cursor: inherit;
        }

        .ppk-upload-prompt {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
        }

        .ppk-upload-icon {
          font-size: 3rem;
          opacity: 0.7;
        }

        .ppk-upload-text {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .ppk-upload-text strong {
          font-size: 1.1rem;
          color: #333;
        }

        .ppk-upload-text small {
          color: #666;
          font-size: 0.9rem;
        }

        .ppk-processing {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          color: #007bff;
        }

        .ppk-uploaded {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          color: #28a745;
        }

        .ppk-uploaded small {
          color: #666;
          font-size: 0.8rem;
        }

        .spinner {
          width: 24px;
          height: 24px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #007bff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default PPKUpload;
