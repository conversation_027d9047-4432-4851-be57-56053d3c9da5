{"name": "ccalc-backend", "version": "1.0.0", "private": true, "scripts": {"build": "tsc", "start": "node dist/backend/server.js", "dev": "nodemon --exec ts-node server.ts", "test": "jest", "lint": "eslint . --ext .js,.ts", "setup-admin": "ts-node scripts/setupAdminWithKeys.ts", "generate-admin-keys": "ts-node scripts/generatePPKKeys.ts admin --encrypted", "setup-dev-keys": "ts-node scripts/generatePPKKeys.ts admin", "setup-prod-keys": "ts-node scripts/generatePPKKeys.ts admin --encrypted --production", "rotate-admin-keys": "ts-node scripts/refreshAdminKey.ts --encrypted"}, "dependencies": {"@types/archiver": "^5.3.2", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.0", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/ua-parser-js": "^0.7.36", "archiver": "^5.3.2", "argon2": "^0.43.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "ffmpeg": "^0.0.4", "fluent-ffmpeg": "^2.1.2", "geoip-lite": "^1.4.7", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "mongoose": "^8.15.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "ua-parser-js": "^1.0.37", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/fluent-ffmpeg": "^2.1.21", "@types/geoip-lite": "^1.4.1", "@types/module-alias": "^2.0.4", "@types/multer": "^1.4.13", "@types/node": "^20.0.0", "@types/sharp": "^0.32.0", "@types/ws": "^8.5.5", "eslint": "^8.0.0", "jest": "^29.0.0", "nodemon": "^3.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18"}}