/**
 * Browser-compatible PPK (Personal Public Key) utilities
 * 
 * This file provides PPK functionality for the browser using the Web Crypto API
 * Supports PEM (.pem) format for key files
 */
import { analyzePEMKey } from './pemAnalyzer';
import { handlePPKError, createPPKError } from './ppkErrorHandler';

interface KeyImportResult {
  key: CryptoKey;
  algorithm: string;
  isExtractable: boolean;
  usages: KeyUsage[];
}

/**
 * Generate a random challenge for PPK authentication
 * @returns A random challenge string
 */
export function generateChallenge(): string {
  const array = new Uint8Array(32);
  window.crypto.getRandomValues(array);
  return Array.from(array)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Helper function to convert base64 to ArrayBuffer
 * @param base64 Base64 string
 * @returns ArrayBuffer
 */
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binary = window.atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Helper function to convert PEM to binary
 * @param pem PEM format key
 * @returns ArrayBuffer of the key data
 */
function pemToBinary(pem: string): ArrayBuffer {
  const keyType = pem.includes('PUBLIC KEY') ? 'PUBLIC KEY' : 'PRIVATE KEY';
  const pemContents = pem
    .replace(`-----BEGIN ${keyType}-----`, '')
    .replace(`-----END ${keyType}-----`, '')
    .replace(/\s/g, '');
  
  return base64ToArrayBuffer(pemContents);
}

/**
 * Import a public key from PEM format
 * @param pemKey The public key in PEM format
 * @returns A CryptoKey object
 */
export async function importPublicKey(pemKey: string): Promise<CryptoKey> {
  try {
    const analysis = analyzePEMKey(pemKey);
    if (!analysis.isValid || analysis.keyType !== 'public') {
      throw new Error('Invalid public key format');
    }

    const binaryKey = pemToBinary(pemKey);
    
    return await window.crypto.subtle.importKey(
      'spki',
      binaryKey,
      {
        name: 'RSASSA-PKCS1-v1_5',
        hash: { name: 'SHA-256' }
      },
      true,
      ['verify']
    );
  } catch (error) {
    throw new Error(`Failed to import public key: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Import a private key from PEM format
 * @param pemKey The private key in PEM format
 * @param passphrase Optional passphrase for encrypted keys
 * @returns A CryptoKey object
 */
export async function importPrivateKey(pemKey: string, passphrase?: string): Promise<CryptoKey> {
  try {
    const analysis = analyzePEMKey(pemKey);
    if (!analysis.isValid) {
      throw createPPKError(
        'INVALID_KEY_FORMAT',
        analysis.message,
        'Invalid private key format.',
        ['Ensure your key is in PEM format', 'Check that the key file is not corrupted']
      );
    }

    if (analysis.isEncrypted && !passphrase) {
      throw createPPKError(
        'PASSPHRASE_REQUIRED',
        'Passphrase required for encrypted key',
        'This private key is encrypted and requires a passphrase.',
        ['Enter the passphrase for your private key', 'Use an unencrypted key if you prefer']
      );
    }

    // Note: Web Crypto API doesn't support encrypted keys directly
    // For encrypted keys, they need to be decrypted first (typically server-side)
    if (analysis.isEncrypted) {
      throw createPPKError(
        'ENCRYPTED_KEY_NOT_SUPPORTED',
        'Encrypted private keys are not supported in the browser',
        'Encrypted private keys cannot be processed directly in the browser for security reasons.',
        [
          'Use an unencrypted private key instead',
          'Convert your encrypted key: openssl rsa -in encrypted.pem -out unencrypted.pem',
          'Keep unencrypted keys secure and delete them after use'
        ]
      );
    }

    const binaryKey = pemToBinary(pemKey);

    // Try different import formats and algorithms
    const algorithms = [
      {
        name: 'RSASSA-PKCS1-v1_5',
        hash: { name: 'SHA-256' }
      },
      {
        name: 'RSA-PSS',
        hash: { name: 'SHA-256' }
      }
    ];

    console.log('PPK DEBUG: Attempting to import private key...');

    // Try pkcs8 format first (most common for modern keys)
    for (const algorithm of algorithms) {
      try {
        console.log(`PPK DEBUG: Trying algorithm ${algorithm.name} with pkcs8 format`);
        const key = await window.crypto.subtle.importKey(
          'pkcs8',
          binaryKey,
          algorithm,
          false, // Not extractable for security
          ['sign']
        );
        console.log(`PPK DEBUG: Successfully imported key with ${algorithm.name}`);
        return key;
      } catch (error) {
        console.log(`PPK DEBUG: Failed to import with algorithm ${algorithm.name}:`, error);
        // Continue to next algorithm
      }
    }

    // If PKCS#8 fails, the key might be in PKCS#1 format
    // This is less common but we should handle it
    throw createPPKError(
      'UNSUPPORTED_KEY_FORMAT',
      'Could not import private key with any supported algorithm or format',
      'Your private key is in an unsupported format.',
      [
        'Ensure your key is in PKCS#8 format (-----BEGIN PRIVATE KEY-----)',
        'Convert PKCS#1 to PKCS#8: openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in pkcs1.pem -out pkcs8.pem',
        'Use a standard RSA 2048-bit key for maximum compatibility',
        'Contact support if you continue to have issues'
      ]
    );
  } catch (error) {
    // If it's already a PPK error, re-throw it
    if (error && typeof error === 'object' && 'code' in error) {
      throw error;
    }
    
    // Otherwise, analyze and wrap the error
    const ppkError = handlePPKError(error, 'Private Key Import');
    throw ppkError;
  }
}

/**
 * Sign a challenge with a private key
 * @param challenge The challenge to sign
 * @param privateKey The CryptoKey object for signing
 * @returns Base64-encoded signature
 */
export async function signChallenge(challenge: string, privateKey: CryptoKey): Promise<string> {
  try {
    if (!challenge || !privateKey) {
      throw createPPKError(
        'INVALID_PARAMETERS',
        'Challenge and private key are required',
        'Invalid parameters for signing.',
        ['Ensure you have loaded a valid private key', 'Check that the challenge was received correctly']
      );
    }

    // Convert challenge to ArrayBuffer
    const encoder = new TextEncoder();
    const data = encoder.encode(challenge);

    // Get the algorithm from the key
    const keyAlgorithm = privateKey.algorithm as RsaHashedKeyAlgorithm;
    console.log('PPK DEBUG: Key algorithm:', keyAlgorithm);    // Use the key's algorithm for signing
    let signAlgorithm: AlgorithmIdentifier | RsaPssParams;
    
    if (keyAlgorithm.name === 'RSASSA-PKCS1-v1_5') {
      signAlgorithm = 'RSASSA-PKCS1-v1_5';
    } else if (keyAlgorithm.name === 'RSA-PSS') {
      signAlgorithm = {
        name: 'RSA-PSS',
        saltLength: 32, // SHA-256 hash length
      } as RsaPssParams;
    } else {
      // Fallback to RSASSA-PKCS1-v1_5 (most compatible with backend)
      signAlgorithm = 'RSASSA-PKCS1-v1_5';
      console.log('PPK DEBUG: Using fallback algorithm RSASSA-PKCS1-v1_5');
    }

    console.log('PPK DEBUG: Using sign algorithm:', signAlgorithm);

    // Sign the challenge
    const signature = await window.crypto.subtle.sign(
      signAlgorithm,
      privateKey,
      data
    );

    // Convert signature to base64
    const signatureBase64 = btoa(String.fromCharCode(...Array.from(new Uint8Array(signature))));
    console.log('PPK DEBUG: Signature generated successfully, length:', signatureBase64.length);
    
    return signatureBase64;
  } catch (error) {
    console.error('PPK DEBUG: Sign challenge error:', error);
    
    // If it's already a PPK error, re-throw it
    if (error && typeof error === 'object' && 'code' in error) {
      throw error;
    }
    
    // Otherwise, analyze and wrap the error
    const ppkError = handlePPKError(error, 'Challenge Signing');
    throw ppkError;
  }
}

/**
 * Verify a signature
 * @param challenge The original challenge
 * @param signature The base64-encoded signature
 * @param publicKey The CryptoKey object for verification
 * @returns boolean indicating if the signature is valid
 */
export async function verifySignature(
  challenge: string,
  signature: string,
  publicKey: CryptoKey
): Promise<boolean> {
  try {
    if (!challenge || !signature || !publicKey) {
      throw createPPKError(
        'INVALID_PARAMETERS',
        'Challenge, signature, and public key are required',
        'Invalid parameters for verification.',
        ['Ensure all required parameters are provided', 'Check that the signature was generated correctly']
      );
    }

    // Convert challenge to ArrayBuffer
    const encoder = new TextEncoder();
    const data = encoder.encode(challenge);

    // Convert base64 signature to ArrayBuffer
    const signatureArray = base64ToArrayBuffer(signature);

    // Get the algorithm from the key to match what was used for signing
    const keyAlgorithm = publicKey.algorithm as RsaHashedKeyAlgorithm;
    console.log('PPK DEBUG: Verify using key algorithm:', keyAlgorithm);

    // Use the same algorithm logic as signing
    let verifyAlgorithm: AlgorithmIdentifier | RsaPssParams;
    
    if (keyAlgorithm.name === 'RSASSA-PKCS1-v1_5') {
      verifyAlgorithm = 'RSASSA-PKCS1-v1_5';
    } else if (keyAlgorithm.name === 'RSA-PSS') {
      verifyAlgorithm = {
        name: 'RSA-PSS',
        saltLength: 32, // SHA-256 hash length - must match signing
      } as RsaPssParams;
    } else {
      // Fallback to RSASSA-PKCS1-v1_5 (most common)
      verifyAlgorithm = 'RSASSA-PKCS1-v1_5';
    }

    console.log('PPK DEBUG: Using verify algorithm:', verifyAlgorithm);

    // Verify the signature
    const result = await window.crypto.subtle.verify(
      verifyAlgorithm,
      publicKey,
      signatureArray,
      data
    );
    
    console.log('PPK DEBUG: Verification result:', result);
    
    if (!result) {
      throw createPPKError(
        'SIGNATURE_VERIFICATION_FAILED',
        'Signature verification failed',
        'The signature could not be verified.',
        [
          'Ensure you\'re using the correct private key',
          'Make sure your private key matches the public key on the server',
          'Try generating a new signature',
          'Contact your administrator if the issue persists'
        ]
      );
    }
    
    return result;
  } catch (error) {
    console.error('PPK DEBUG: Verification error:', error);
    
    // If it's already a PPK error, re-throw it
    if (error && typeof error === 'object' && 'code' in error) {
      throw error;
    }
    
    // Otherwise, analyze and wrap the error
    const ppkError = handlePPKError(error, 'Signature Verification');
    throw ppkError;
  }
}