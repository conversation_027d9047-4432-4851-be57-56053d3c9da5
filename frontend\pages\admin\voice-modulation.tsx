import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import Button from '../../components/admin/Button';
import Icon from '../../components/admin/Icon';
import { apiClient } from '../../utils/axiosClient';

interface VoiceProfile {
  pitch: number;
  tempo: number;
  reverb: number;
  distortion: number;
  formant: number;
  chorus: boolean;
  normalize: boolean;
}

interface ServiceStatus {
  soxAvailable: boolean;
  status: string;
  message: string;
  profiles: string[];
  features: string[];
}

const VoiceModulationPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<string>('SECURE_MALE');
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus | null>(null);
  const [availableProfiles, setAvailableProfiles] = useState<Record<string, VoiceProfile>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [modulatedAudioUrl, setModulatedAudioUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchServiceStatus();
    fetchAvailableProfiles();
  }, []);
  const fetchServiceStatus = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.backend.get('/api/voice/service-status');
      if (response.data.success) {
        setServiceStatus(response.data.data);
        if (!response.data.data.soxAvailable) {
          setError('Voice modulation service is not available. SoX is not installed on the server.');
        }
      }
    } catch (error) {
      console.error('Failed to fetch service status:', error);
      setError('Failed to check voice modulation service status.');
    } finally {
      setIsLoading(false);
    }
  };
  const fetchAvailableProfiles = async () => {
    try {
      const response = await apiClient.backend.get('/api/voice/profiles');
      if (response.data.success) {
        setAvailableProfiles(response.data.data.profiles);
      }
    } catch (error) {
      console.error('Failed to fetch voice profiles:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('audio/')) {
        setAudioFile(file);
        setError(null);
        setSuccess(null);
        setModulatedAudioUrl(null);
      } else {
        setError('Please select an audio file (WAV, MP3, etc.)');
        setAudioFile(null);
      }
    }
  };

  const testVoiceModulation = async () => {
    if (!audioFile) {
      setError('Please select an audio file first');
      return;
    }

    if (!serviceStatus?.soxAvailable) {
      setError('Voice modulation service is not available');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setSuccess(null);    try {
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('profileName', selectedProfile);
      
      const response = await apiClient.backend.post('/api/voice/test-modulation', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob'
      });

      const audioBlob = new Blob([response.data], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      setModulatedAudioUrl(audioUrl);
      setSuccess(`Voice modulation applied successfully using ${selectedProfile} profile`);

    } catch (error: any) {
      console.error('Voice modulation failed:', error);
      setError(error.response?.data?.error || 'Voice modulation failed');
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <Head>
          <title>Voice Modulation | CCALC Admin Panel</title>
        </Head>
        <div className="container">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Icon name="refresh" className="animate-spin text-4xl mb-4 mx-auto" />
              <p>Loading voice modulation service...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Voice Modulation | CCALC Admin Panel</title>
      </Head>
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Voice Modulation System</h1>
          <p className="text-gray-600">
            Advanced voice morphing using SoX (Sound eXchange) for secure, non-reversible voice modulation.
          </p>
        </div>

        {serviceStatus && (
          <div className="mb-6">
            <div className={`p-4 rounded-lg border ${serviceStatus.soxAvailable 
              ? 'bg-green-50 border-green-200 text-green-800' 
              : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center">
                <Icon name={serviceStatus.soxAvailable ? 'check' : 'warning'} className="mr-3" />
                <div>
                  <h3 className="font-semibold">Service Status: {serviceStatus.status}</h3>
                  <p className="text-sm">{serviceStatus.message}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-800">
              <Icon name="warning" className="mr-3" />
              <span>{error}</span>
            </div>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center text-green-800">
              <Icon name="check" className="mr-3" />
              <span>{success}</span>
            </div>
          </div>
        )}

        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-6">Voice Modulation Testing</h2>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Voice Modulation Profile:
            </label>
            <select
              value={selectedProfile}
              onChange={(e) => setSelectedProfile(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isProcessing}
            >
              {Object.keys(availableProfiles).map((profileName) => (
                <option key={profileName} value={profileName}>
                  {profileName.replace(/_/g, ' ')}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload Audio File:
            </label>
            <input
              type="file"
              accept="audio/*"
              onChange={handleFileUpload}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              disabled={isProcessing}
            />
            {audioFile && (
              <div className="mt-2 text-sm text-gray-600">
                <span className="font-semibold">Selected:</span> {audioFile.name}
              </div>
            )}
          </div>

          <div className="flex gap-4 mb-6">
            <Button
              onClick={testVoiceModulation}
              disabled={!audioFile || isProcessing || !serviceStatus?.soxAvailable}
              variant="primary"
              className="flex-1"
            >
              {isProcessing ? (
                <>
                  <Icon name="refresh" className="animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <Icon name="check" className="mr-2" />
                  Apply Voice Modulation
                </>
              )}
            </Button>

            <Button
              onClick={() => {
                setAudioFile(null);
                setModulatedAudioUrl(null);
                setError(null);
                setSuccess(null);
              }}
              disabled={!audioFile || isProcessing}
              variant="outline"
              className="flex-1"
            >
              <Icon name="close" className="mr-2" />
              Clear
            </Button>
          </div>

          {modulatedAudioUrl && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold mb-4">Modulated Audio Result</h3>
              <audio controls className="w-full mb-4">
                <source src={modulatedAudioUrl} type="audio/wav" />
                Your browser does not support the audio element.
              </audio>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(VoiceModulationPage);
