import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import AdminModel from '../../models/Admin';
import DeviceModel from '../../models/Device';
import mongoose from 'mongoose';

const router = Router();

/**
 * Get full audit trail with advanced filtering
 * GET /api/audit/logs
 * Query params: startDate, endDate, userId, adminId, action, limit, skip
 */
router.get('/logs', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    
    // Parse query parameters with validation
    const {
      startDate,
      endDate,
      userId,
      adminId: filterAdminId,
      action,
      limit = 50,
      skip = 0,
      sortBy = 'timestamp',
      sortOrder = 'desc'
    } = req.query;

    // Validate numeric parameters
    const parsedLimit = Math.min(Math.max(parseInt(limit as string) || 50, 1), 1000);
    const parsedSkip = Math.max(parseInt(skip as string) || 0, 0);

    // Build filter object
    const filter: any = {};
    
    if (startDate || endDate) {
      filter.timestamp = {};
      if (startDate) {
        const start = new Date(startDate as string);
        if (isNaN(start.getTime())) {
          res.status(400).json({
            success: false,
            error: 'Invalid startDate format'
          });
          return;
        }
        filter.timestamp.$gte = start;
      }
      if (endDate) {
        const end = new Date(endDate as string);
        if (isNaN(end.getTime())) {
          res.status(400).json({
            success: false,
            error: 'Invalid endDate format'
          });
          return;
        }
        filter.timestamp.$lte = end;
      }
    }
    
    if (userId) {
      if (!mongoose.Types.ObjectId.isValid(userId as string)) {
        res.status(400).json({
          success: false,
          error: 'Invalid userId format'
        });
        return;
      }
      filter.userId = userId;
    }
    if (filterAdminId) {
      if (!mongoose.Types.ObjectId.isValid(filterAdminId as string)) {
        res.status(400).json({
          success: false,
          error: 'Invalid adminId format'
        });
        return;
      }
      filter.adminId = filterAdminId;
    }
    if (action) filter.action = { $regex: action, $options: 'i' };

    // Validate sort parameters
    const allowedSortFields = ['timestamp', 'action', 'userId', 'adminId'];
    const sortField = allowedSortFields.includes(sortBy as string) ? sortBy as string : 'timestamp';
    const sortDirection = sortOrder === 'asc' ? 1 : -1;

    // Get total count for pagination
    const totalCount = await AuditLogModel.countDocuments(filter);

    // Get audit logs with pagination
    const auditLogs = await AuditLogModel.find(filter)
      .populate('userId', 'username email status')
      .populate('adminId', 'username email')
      .sort({ [sortField]: sortDirection })
      .limit(parsedLimit)
      .skip(parsedSkip)
      .exec();

    // Log admin access
    await AuditLogModel.create({
      action: 'AUDIT_LOGS_ACCESSED',
      adminId,
      details: {
        filterCriteria: filter,
        resultCount: auditLogs.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        logs: auditLogs,
        pagination: {
          total: totalCount,
          limit: parsedLimit,
          skip: parsedSkip,
          pages: Math.ceil(totalCount / parsedLimit)
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit logs',
      details: error.message
    });
  }
});

/**
 * Get user-specific audit logs
 * GET /api/audit/user/:userId
 */
router.get('/user/:userId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;
    const { limit = 50, skip = 0, action } = req.query;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    // Validate numeric parameters
    const parsedLimit = Math.min(Math.max(parseInt(limit as string) || 50, 1), 1000);
    const parsedSkip = Math.max(parseInt(skip as string) || 0, 0);

    // Verify user exists
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Build filter
    const filter: any = { userId };
    if (action) filter.action = { $regex: action, $options: 'i' };

    // Get user audit logs
    const auditLogs = await AuditLogModel.find(filter)
      .populate('adminId', 'username email')
      .sort({ timestamp: -1 })
      .limit(parsedLimit)
      .skip(parsedSkip)
      .exec();

    const totalCount = await AuditLogModel.countDocuments(filter);

    // Log admin access
    await AuditLogModel.create({
      action: 'USER_AUDIT_ACCESSED',
      adminId,
      userId,
      details: {
        targetUser: user.username,
        resultCount: auditLogs.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          status: user.status
        },
        logs: auditLogs,
        pagination: {
          total: totalCount,
          limit: parseInt(limit as string),
          skip: parseInt(skip as string),
          pages: Math.ceil(totalCount / parseInt(limit as string))
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching user audit logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user audit logs',
      details: error.message
    });
  }
});

/**
 * Get device-specific audit logs
 * GET /api/audit/device/:deviceId
 */
router.get('/device/:deviceId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { deviceId } = req.params;
    const { limit = 50, skip = 0 } = req.query;    // Verify device exists
    const device = await DeviceModel.findById(deviceId).populate('userId', 'username email');
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Device not found'
      });
      return;
    }// Get device-related audit logs
    const auditLogs = await AuditLogModel.find({
      $or: [
        { 'details.deviceId': deviceId },
        { 'details.deviceFingerprint': device.fingerprint.hash }
      ]
    })
      .populate('userId', 'username email')
      .populate('adminId', 'username email')
      .sort({ timestamp: -1 })
      .limit(parseInt(limit as string))
      .skip(parseInt(skip as string))
      .exec();    const totalCount = await AuditLogModel.countDocuments({
      $or: [
        { 'details.deviceId': deviceId },
        { 'details.deviceFingerprint': device.fingerprint.hash }
      ]
    });

    // Log admin access
    await AuditLogModel.create({
      action: 'DEVICE_AUDIT_ACCESSED',
      adminId,      details: {
        deviceId,
        deviceFingerprint: device.fingerprint.hash,
        deviceOwner: device.userId,
        resultCount: auditLogs.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {        device: {
          id: device._id,
          fingerprint: device.fingerprint.hash,
          platform: device.fingerprint.components.platform,
          owner: device.userId,
          status: device.security.status,
          firstSeen: device.createdAt
        },
        logs: auditLogs,
        pagination: {
          total: totalCount,
          limit: parseInt(limit as string),
          skip: parseInt(skip as string),
          pages: Math.ceil(totalCount / parseInt(limit as string))
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching device audit logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch device audit logs',
      details: error.message
    });
  }
});

/**
 * Get admin action logs
 * GET /api/audit/actions
 */
router.get('/actions', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { limit = 50, skip = 0, adminId: filterAdminId, startDate, endDate } = req.query;

    // Build filter for admin actions
    const filter: any = {
      action: {
        $in: [
          'USER_CREATED', 'USER_UPDATED', 'USER_DELETED', 'USER_RESET',
          'BUILD_GENERATED', 'BUILD_REVOKED',
          'DEVICE_REVOKED', 'DEVICE_RESET',
          'SYSTEM_SHUTDOWN', 'SYSTEM_WIPE', 'BLE_RESET',
          'ADMIN_CREATED', 'ADMIN_UPDATED', 'ADMIN_DELETED',
          'CONFIG_UPDATED', 'SECURITY_UPDATED'
        ]
      }
    };

    if (filterAdminId) filter.adminId = filterAdminId;
    
    if (startDate || endDate) {
      filter.timestamp = {};
      if (startDate) filter.timestamp.$gte = new Date(startDate as string);
      if (endDate) filter.timestamp.$lte = new Date(endDate as string);
    }

    // Get admin action logs
    const actionLogs = await AuditLogModel.find(filter)
      .populate('adminId', 'username email')
      .populate('userId', 'username email')
      .sort({ timestamp: -1 })
      .limit(parseInt(limit as string))
      .skip(parseInt(skip as string))
      .exec();

    const totalCount = await AuditLogModel.countDocuments(filter);

    // Log admin access
    await AuditLogModel.create({
      action: 'ADMIN_ACTIONS_ACCESSED',
      adminId,
      details: {
        filterCriteria: filter,
        resultCount: actionLogs.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        logs: actionLogs,
        pagination: {
          total: totalCount,
          limit: parseInt(limit as string),
          skip: parseInt(skip as string),
          pages: Math.ceil(totalCount / parseInt(limit as string))
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching admin action logs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch admin action logs',
      details: error.message
    });
  }
});

/**
 * Get audit statistics
 * GET /api/audit/statistics
 */
router.get('/statistics', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;
    const { days = 30 } = req.query;
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days as string));

    // Get audit statistics
    const stats = await AuditLogModel.aggregate([
      { $match: { timestamp: { $gte: startDate } } },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 },
          lastOccurrence: { $max: '$timestamp' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get daily activity
    const dailyActivity = await AuditLogModel.aggregate([
      { $match: { timestamp: { $gte: startDate } } },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1 } }
    ]);

    // Get top active users
    const topUsers = await AuditLogModel.aggregate([
      { $match: { timestamp: { $gte: startDate }, userId: { $exists: true } } },
      {
        $group: {
          _id: '$userId',
          count: { $sum: 1 },
          lastActivity: { $max: '$timestamp' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Populate user information
    await AuditLogModel.populate(topUsers, {
      path: '_id',
      select: 'username email status'
    });

    // Log admin access
    await AuditLogModel.create({
      action: 'AUDIT_STATISTICS_ACCESSED',
      adminId,
      details: {
        period: `${days} days`,
        totalStats: stats.length
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        period: `${days} days`,
        actionStats: stats,
        dailyActivity: dailyActivity.map(item => ({
          date: item._id.date,
          count: item.count
        })),
        topUsers: topUsers.map(item => ({
          user: item._id,
          activityCount: item.count,
          lastActivity: item.lastActivity
        }))
      }
    });

  } catch (error: any) {
    console.error('Error fetching audit statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit statistics',
      details: error.message
    });
  }
});

export default router;
