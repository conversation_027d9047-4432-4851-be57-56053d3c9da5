import React, { useRef, useState, useEffect } from 'react';
import { Animated, Image, View, Text, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';

interface FadeInMediaProps {
  uri: string | null;
  style: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  onError?: (error: any) => void;
  onLoad?: () => void;
  fallbackIcon?: string;
  fallbackText?: string;
  fallbackStyle?: any;
}

export const FadeInMedia: React.FC<FadeInMediaProps> = ({
  uri,
  style,
  resizeMode = 'cover',
  onError,
  onLoad,
  fallbackIcon,
  fallbackText,
  fallbackStyle,
}) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const [loading, setLoading] = useState(!!uri);
  const [error, setError] = useState(false);
  const [retryKey, setRetryKey] = useState(0);
  const [retryAttempts, setRetryAttempts] = useState(0);

  const handleRetry = () => {
    if (retryAttempts >= 3) {
      console.log('⚠️ Maximum retry attempts reached for:', uri);
      return;
    }
    
    setError(false);
    setLoading(true);
    setRetryKey(prev => prev + 1);
    setRetryAttempts(prev => prev + 1);
    console.log('🔄 Retrying image load:', uri);
  };

  // Reset retry counter when URI changes
  useEffect(() => {
    setRetryAttempts(0);
    setError(false);
    setLoading(!!uri);
  }, [uri]);

  // Always enforce fixed size for both image and fallback
  const enforcedStyle = [
    { width: 240, height: 180, borderRadius: 12, overflow: 'hidden', backgroundColor: '#e1e1e1' },
    style
  ];

  if (!uri || error) {
    // Log/report missing/corrupt file with more details
    if (error) {
      console.warn('FadeInMedia: failed to load media:', uri);
    }
    if (!uri) {
      console.warn('FadeInMedia: no URI provided');
    }
    return (
      <TouchableOpacity
        style={[enforcedStyle, fallbackStyle, { justifyContent: 'center', alignItems: 'center' }]}
        onPress={handleRetry}
        activeOpacity={0.7}
        disabled={retryAttempts >= 3}
      >
        {fallbackIcon && <Text style={{ fontSize: 36, marginBottom: 4 }}>{fallbackIcon}</Text>}
        {fallbackText && <Text style={{ color: '#888', fontSize: 14, marginBottom: 4 }}>{fallbackText}</Text>}
        {retryAttempts < 3 ? (
          <Text style={{ color: '#007AFF', fontSize: 13 }}>Tap to retry</Text>
        ) : (
          <Text style={{ color: '#888', fontSize: 12 }}>Unable to load</Text>
        )}
      </TouchableOpacity>
    );
  }

  // Determine if this is a video thumbnail (should have _thumb in the filename)
  const isVideoThumbnail = uri.includes('_thumb') || uri.endsWith('.jpg') && uri.includes('attachment_');

  return (
    <View style={enforcedStyle}>
      {loading && (
        <ActivityIndicator size="small" color="#888" style={{ position: 'absolute', zIndex: 1, alignSelf: 'center', top: '45%' }} />
      )}
      <Animated.Image
        key={retryKey}
        source={{ 
          uri, 
          cache: 'force-cache',  // Force caching for better performance
          headers: {
            // Add cache control headers to maximize caching
            'Cache-Control': 'max-age=31536000', // 1 year
            'Pragma': 'cache'
          }
        }}
        style={[enforcedStyle, { opacity }]}
        resizeMode={resizeMode}
        onError={e => {
          console.warn('⚠️ Image load error:', e.nativeEvent.error, 'URI:', uri?.substring(0, 100));
          setError(true);
          setLoading(false);
          onError && onError(e);
        }}
        onLoad={() => {
          setLoading(false);
          console.log('✅ Image loaded successfully:', uri?.substring(0, 50));
          Animated.timing(opacity, {
            toValue: 1,
            duration: 350,
            useNativeDriver: true,
          }).start();
          onLoad && onLoad();
        }}
      />
    </View>
  );
};
