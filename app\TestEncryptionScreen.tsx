/**
 * Simple test to verify mobile app and backend integration
 */

import { StyleSheet, Text, View, Button, Alert } from 'react-native';
import React, { useState } from 'react';
import { ChatService } from './src/services/ChatService';
import { AuthService } from './src/services/AuthService';

export default function TestEncryptionScreen() {
  const [status, setStatus] = useState('Ready to test');
  const [lastMessage, setLastMessage] = useState('');

  const testAuthentication = async () => {
    try {
      setStatus('Testing authentication...');
      const authService = AuthService.getInstance();
      const result = await authService.authenticateExpression('2+2=4');
      
      if (result.success) {
        setStatus('✅ Authentication successful');
      } else {
        setStatus(`❌ Auth failed: ${result.error}`);
      }
    } catch (error) {
      setStatus(`❌ Auth error: ${error}`);
    }
  };

  const testSendMessage = async () => {
    try {
      setStatus('Testing encrypted message...');
      const chatService = ChatService.getInstance();
      const result = await chatService.sendMessage('Test encrypted message! 🔐');
      
      if (result.success) {
        setStatus('✅ Message sent successfully');
        setLastMessage(result.message?.text || '');
      } else {
        setStatus(`❌ Send failed: ${result.error}`);
      }
    } catch (error) {
      setStatus(`❌ Send error: ${error}`);
    }
  };

  const testGetMessages = async () => {
    try {
      setStatus('Getting messages...');
      const chatService = ChatService.getInstance();
      const result = await chatService.getMessages();
      
      if (result.success) {
        setStatus(`✅ Got ${result.messages?.length || 0} messages`);
        if (result.messages && result.messages.length > 0) {
          setLastMessage(result.messages[result.messages.length - 1].text);
        }
      } else {
        setStatus(`❌ Get failed: ${result.error}`);
      }
    } catch (error) {
      setStatus(`❌ Get error: ${error}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 Encryption Test</Text>
      
      <Text style={styles.status}>Status: {status}</Text>
      
      {lastMessage ? (
        <Text style={styles.message}>Last message: {lastMessage}</Text>
      ) : null}

      <View style={styles.buttonContainer}>
        <Button title="1. Test Auth" onPress={testAuthentication} />
        <Button title="2. Send Message" onPress={testSendMessage} />
        <Button title="3. Get Messages" onPress={testGetMessages} />
      </View>

      <Text style={styles.summary}>
        📋 Features Complete:{'\n'}
        • ✅ Instagram-like attachment picker{'\n'}
        • ✅ End-to-end encryption{'\n'}
        • ✅ Admin panel decryption{'\n'}
        • ✅ File type filtering{'\n'}
        • ✅ Minimal chat UI
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  status: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  buttonContainer: {
    gap: 10,
    marginBottom: 30,
  },
  summary: {
    fontSize: 14,
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
  },
});
