// CSRF Token Management Module (Legacy - use csrfManager for new code)
import { csrfManager } from './csrfManager';

/**
 * Set the CSRF token to be used in subsequent requests
 * @param token CSRF token from server
 */
export const setCsrfToken = (token: string): void => {
  csrfManager.setToken(token);
};

/**
 * Get the current CSRF token
 * @returns The current CSRF token or null if not set
 */
export const getCsrfToken = (): string | null => {
  return csrfManager.getToken();
};

/**
 * Clear the CSRF token (e.g., on logout)
 */
export const clearCsrfToken = (): void => {
  csrfManager.clearToken();
};

/**
 * Setup CSRF protection for an axios instance
 * @param axiosInstance The axios instance to configure
 */
export const setupCsrfProtection = (axiosInstance: any): void => {
  csrfManager.setupInterceptors(axiosInstance);
};

export default {
  setCsrfToken,
  getCsrfToken,
  clearCsrfToken,
  setupCsrfProtection
};
