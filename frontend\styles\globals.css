/* Tailwind CSS Base Styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Minimalist Global Styles for CCALC */
:root {
  --color-bg: #f7f9fc;
  --color-bg-alt: #fff;
  --color-text: #1a1a1a;
  --color-muted: #888;
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-success: #22c55e;
  --color-danger: #ef4444;
  --color-warning: #f59e42;
  --color-border: #e5e7eb;
  --color-accent: #6366f1;
  --color-purple: #9333ea;
  --color-orange: #ea580c;
  --color-primary-transparent-10: rgba(37, 99, 235, 0.1);
  --color-danger-transparent-10: rgba(239, 68, 68, 0.1);
  --color-warning-transparent-10: rgba(245, 158, 66, 0.1);
  --color-success-transparent-10: rgba(34, 197, 94, 0.1);
  --color-purple-transparent-10: rgba(147, 51, 234, 0.1);
  --color-orange-transparent-10: rgba(234, 88, 12, 0.1);
  --color-accent-transparent-10: rgba(99, 102, 241, 0.1); /* Assuming accent is 99,102,241 */
  --radius: 12px;
  --radius-sm: 6px;
  --shadow: 0 4px 24px rgba(0,0,0,0.07);
  --shadow-lg: 0 10px 30px rgba(0,0,0,0.1); /* Added for card hover */
  --transition: 0.18s cubic-bezier(.4,0,.2,1);
  --spacing-1: 6px;
  --spacing-2: 12px;
  --spacing-3: 20px;
  --spacing-4: 32px;
  --spacing-5: 48px;
  --spacing-6: 64px; /* Added */
  --spacing-8: 96px; /* Added */
  --font-title: 'Inter', 'Segoe UI', Arial, sans-serif;
  --font-body: 'Inter', 'Segoe UI', Arial, sans-serif;
}

* { box-sizing: border-box; }
html, body {
  font-family: var(--font-body);
  font-size: 17px;
  background: var(--color-bg);
  color: var(--color-text);
  min-height: 100vh;
  letter-spacing: 0.01em;
}
a { color: var(--color-primary); text-decoration: none; transition: color var(--transition); }
a:hover { color: var(--color-primary-hover); }
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-title);
  font-weight: 700;
  margin: 0 0 var(--spacing-2) 0;
  color: var(--color-text);
  letter-spacing: 0.01em;
}
h1 { font-size: 2.2rem; }
h2 { font-size: 1.6rem; }
h3 { font-size: 1.25rem; }
p, ul, ol {
  margin-bottom: var(--spacing-2);
  line-height: 1.7;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-4) var(--spacing-2);
}

/* Card */
.card {
  background: var(--color-bg-alt);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  /* padding: var(--spacing-4); Default padding removed, will be handled by card-body */
  margin-bottom: var(--spacing-4);
  border: 1px solid var(--color-border);
  overflow: hidden; /* Added to ensure child elements respect border radius */
  transition: box-shadow var(--transition); /* Added transition */
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

.card--full {
  width: 100%;
}

.card-header {
  padding: var(--spacing-3) var(--spacing-4); /* Adjusted padding */
  border-bottom: 1px solid var(--color-border);
  background-color: #f9fafb; /* Lighter background for header */
}

.card-title {
  font-size: 1.15rem; /* Adjusted size */
  font-weight: 600; /* semibold */
  color: var(--color-text);
  margin: 0; /* Remove default h3 margin */
}

.card-body {
  padding: var(--spacing-4);
}

.card-body--no-padding {
  padding: 0;
}

/* Sidebar & Topbar */
.sidebar {
  background: #1e293b; /* Darker blue-gray */
  color: #e2e8f0; /* Lighter text for contrast */
  border-right: 1px solid #334155; /* Subtle border */
  width: 260px; /* Slightly wider */
  height: 100vh;
  padding: var(--spacing-3) var(--spacing-3); /* Consistent padding */
  position: fixed;
  top: 0; left: 0;
  display: flex; flex-direction: column;
  z-index: 1000; /* Ensure sidebar is on top */
  box-shadow: 3px 0 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease-in-out, min-width 0.3s ease-in-out; /* Smooth transitions */
}
.sidebar-header { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
  padding-bottom: var(--spacing-3); /* Add some space below header */
  border-bottom: 1px solid #334155; /* Separator */
  margin-bottom: var(--spacing-3); 
}
.logo { 
  display: flex; 
  align-items: center; 
  gap: var(--spacing-2); 
  font-weight: bold; 
  font-size: 1.3rem; /* Slightly larger logo text */
  color: #fff; /* White logo text */
}
.logo-text { 
  font-size: 1.3rem; 
  font-weight: 700; 
  color: #fff; 
}
.sidebar-nav { 
  display: flex; 
  flex-direction: column; 
  gap: var(--spacing-1); /* Smaller gap between nav items */
  flex-grow: 1; /* Allow nav to take available space */
}
.nav-link { 
  color: #cbd5e1; /* Slightly brighter nav link text */
  text-decoration: none; 
  padding: var(--spacing-2) var(--spacing-3); 
  border-radius: var(--radius-sm); 
  display: flex; 
  align-items: center; 
  gap: var(--spacing-2); 
  font-weight: 500; 
  transition: background var(--transition), color var(--transition), padding-left var(--transition); /* Added padding-left transition */
  border-left: 3px solid transparent; /* For active indicator */
}
.nav-link:hover { 
  background: #334155; /* Darker hover */
  color: #fff; 
  padding-left: calc(var(--spacing-3) + 3px); /* Indent on hover */
}
.nav-link.active {
  background: #2563eb; /* Primary color for active */
  color: #fff;
  font-weight: 600;
  border-left-color: #60a5fa; /* Lighter blue accent for active */
  padding-left: calc(var(--spacing-3) + 3px); /* Indent active link */
}
.nav-link .icon { /* Targeting Icon component if it renders an svg or i tag */
  transition: transform 0.2s ease-in-out;
}
.nav-link:hover .icon {
  transform: scale(1.1);
}

.menu-close-btn { /* For mobile */
  display: none; /* Hidden by default, shown in mobile */
  background: transparent;
  border: none;
  color: #cbd5e1;
  padding: var(--spacing-1);
  cursor: pointer;
}
.menu-close-btn:hover {
  color: #fff;
}

.sidebar-footer { 
  margin-top: auto; 
  padding-top: var(--spacing-3);
  border-top: 1px solid #334155; /* Separator */
}
.user-profile-condensed {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  background-color: #334155; /* Slightly different background for profile */
  margin-bottom: var(--spacing-3);
}
.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}
.avatar--sm { /* Already exists, ensure it's used */
  width: 32px;
  height: 32px;
  font-size: 0.8rem;
}
.user-profile-condensed .username {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.9rem;
}
.logout-btn { /* Ensure this is styled via .btn and variants */
  width: 100%;
}

.main-wrapper {
  flex: 1;
  margin-left: 260px; /* Match sidebar width */
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--color-bg); /* Ensure main content has bg */
  transition: margin-left 0.3s ease-in-out;
}

.topbar {
  display: flex; align-items: center; justify-content: space-between;
  padding: var(--spacing-2) var(--spacing-4); /* Adjusted padding */
  background-color: var(--color-bg-alt); /* White background for topbar */
  border-bottom: 1px solid var(--color-border);
  position: sticky; /* Make topbar sticky */
  top: 0;
  z-index: 900; /* Below sidebar but above content */
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.topbar-left { display: flex; align-items: center; gap: var(--spacing-2); }
.menu-toggle-btn { /* For mobile */
  display: none; /* Hidden by default, shown in mobile */
  background: transparent;
  border: none;
  color: var(--color-text); /* Use text color */
  padding: var(--spacing-1);
  cursor: pointer;
}
.menu-toggle-btn:hover {
  color: var(--color-primary);
}
.page-title-main { /* Renamed from .page-title to avoid conflict with global .page-title */
  font-size: 1.3rem; /* Slightly smaller */
  font-weight: 600; 
  color: var(--color-text); 
  margin: 0; 
}
.topbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}
.user-profile-topbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color var(--transition);
}
.user-profile-topbar:hover {
  background-color: #f0f0f0; /* Light hover for profile */
}
.user-profile-topbar .username {
  font-weight: 500;
  font-size: 0.9rem;
  color: var(--color-text);
}

.main-content { 
  flex: 1; /* Allow content to grow */
  padding: var(--spacing-4); /* Consistent padding */
}

.main-footer {
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-bg-alt);
  margin-top: auto; /* Push footer to bottom */
}

/* Mobile-specific styles for AdminLayout */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999; /* Below sidebar, above content */
  display: none; /* Hidden by default */
}

/* Responsive adjustments for AdminLayout */
@media (max-width: 1024px) { /* Tablet and below */
  .sidebar {
    transform: translateX(-100%); /* Hide sidebar off-screen */
    box-shadow: none; /* Remove shadow when hidden */
  }
  .admin-layout.menu-is-open .sidebar {
    transform: translateX(0);
    box-shadow: 3px 0 15px rgba(0,0,0,0.2); /* Show shadow when open */
  }
  .main-wrapper {
    margin-left: 0; /* Full width when sidebar is hidden */
  }
  .menu-toggle-btn, .sidebar .menu-close-btn {
    display: inline-flex; /* Show hamburger and close button */
  }
  .admin-layout.menu-is-open .sidebar-overlay {
    display: block; /* Show overlay when menu is open */
  }
  .topbar-left .page-title-main {
    font-size: 1.2rem;
  }
}

@media (min-width: 1025px) { /* Desktop */
  .admin-layout.menu-is-closed .sidebar {
    width: 80px; /* Collapsed sidebar width */
  }
  .admin-layout.menu-is-closed .sidebar .logo-text,
  .admin-layout.menu-is-closed .sidebar .nav-link span,
  .admin-layout.menu-is-closed .sidebar .user-profile-condensed .username,
  .admin-layout.menu-is-closed .sidebar .logout-btn span:not(.icon) { /* Hide text in collapsed sidebar */
    display: none;
  }
  .admin-layout.menu-is-closed .sidebar .nav-link {
    justify-content: center; /* Center icons in collapsed sidebar */
    padding-left: var(--spacing-2);
    padding-right: var(--spacing-2);
  }
  .admin-layout.menu-is-closed .sidebar .logo {
    justify-content: center;
  }
   .admin-layout.menu-is-closed .sidebar .sidebar-footer .btn {
    font-size: 0; /* Hide button text, icon remains */
  }
  .admin-layout.menu-is-closed .sidebar .sidebar-footer .btn .icon {
    margin-right: 0; /* Remove margin if text is hidden */
  }
  .admin-layout.menu-is-closed .main-wrapper {
    margin-left: 80px; /* Adjust content margin for collapsed sidebar */
  }
  /* No need for menu-toggle-btn or menu-close-btn on desktop */
  .menu-toggle-btn, .sidebar .menu-close-btn {
    display: none;
  }
}

/* Ensure body doesn't scroll when mobile menu is open */
body.admin-menu-open {
  overflow: hidden;
}


/* Styles for .admin-layout itself, if needed */
.admin-layout { 
  display: flex; 
  min-height: 100vh; 
  background-color: var(--color-bg); /* Ensure layout bg is set */
}

/* Remove conflicting/old sidebar styles if any */
/* .sidebar { min-width: 220px; ... } -> Handled above */
/* .main-content { margin-left: 220px; ... } -> Handled by .main-wrapper */

/* @media (max-width: 900px) { ... } */ /* These specific queries are replaced by the new ones */
/* @media (max-width: 600px) { ... } */


/* Buttons */
.btn {
  display: inline-flex; align-items: center; justify-content: center;
  gap: var(--spacing-2);
  border-radius: var(--radius-sm);
  font-weight: 600;
  padding: 0.7rem 1.4rem;
  background: var(--color-primary);
  color: #fff;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  transition: background var(--transition), color var(--transition), box-shadow var(--transition);
  box-shadow: 0 2px 8px rgba(37,99,235,0.07);
}
.btn:hover:not(:disabled) { background: var(--color-primary-hover); }
.btn:disabled { opacity: 0.6; cursor: not-allowed; }
.btn--secondary { background: var(--color-bg-alt); color: var(--color-primary); border: 1px solid var(--color-border); }
.btn--secondary:hover { background: #e6f0fa; }
.btn--danger { background: var(--color-danger); }
.btn--danger:hover { background: #dc2626; }
.btn--success { background: var(--color-success); }
.btn--success:hover { background: #16a34a; }
.btn--ghost { background: transparent; color: var(--color-primary); border: 1px solid transparent; }
.btn--ghost:hover { background: #f0f6ff; }
.btn--sm { font-size: 0.9rem; padding: 0.4rem 0.8rem; }
.btn--lg { font-size: 1.1rem; padding: 0.8rem 1.6rem; }
.btn--full-width { width: 100%; }

/* Renamed .btn.btn-outline to .btn--outline to match component */
.btn--outline {
  background-color: transparent;
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
}
.btn--outline:hover {
  background-color: var(--color-primary);
  color: #fff;
}

.btn--ghost {
  background-color: transparent;
  border: 1px solid transparent;
  color: var(--color-primary);
}
.btn--ghost:hover {
  background-color: rgba(37, 99, 235, 0.1);
}

/* Forms */
input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  padding: 0.7rem 1.1rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: #fff;
  color: var(--color-text);
  transition: border-color var(--transition), box-shadow var(--transition);
  margin-bottom: var(--spacing-1);
}
input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(37,99,235,0.10);
}
label { font-weight: 600; margin-bottom: var(--spacing-1); display: block; }
.form-group { margin-bottom: var(--spacing-3); }
.form-actions { display: flex; gap: var(--spacing-2); justify-content: flex-end; }

/* Table */
table {
  width: 100%; border-collapse: separate; border-spacing: 0; background: var(--color-bg-alt);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow);
}
th, td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  font-size: 1rem;
}
th { background: #f3f4f6; font-weight: 700; letter-spacing: 0.01em; }
tr:last-child td { border-bottom: none; }
tr:hover { background: #f5f7fa; }

/* Alerts */
.alert {
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-3);
  font-size: 1rem;
  display: flex; align-items: center; gap: var(--spacing-2);
  box-shadow: 0 2px 8px rgba(239,68,68,0.04);
}
.alert-error { background: #fff1f0; color: var(--color-danger); border: 1px solid #ffccc7; }
.alert-success { background: #f6ffed; color: var(--color-success); border: 1px solid #b7eb8f; }
.alert-warning { background: #fffbe6; color: var(--color-warning); border: 1px solid #ffe58f; }
.alert-info { background: #e6f7ff; color: var(--color-primary); border: 1px solid #91d5ff; }
.alert-close {
  margin-left: auto;
  background: none;
  border: none;
  color: inherit;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}
.alert-close:hover {
  opacity: 0.7;
}

/* Utility */
.text-center { text-align: center; }
.text-muted { color: var(--color-muted); }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); } /* Added */
.mb-8 { margin-bottom: var(--spacing-8); } /* Added */
.mr-4 { margin-right: var(--spacing-4); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); } /* Added for consistency if needed */
.p-5 { padding: var(--spacing-5); } /* Added */
.py-5 { padding-top: var(--spacing-5); padding-bottom: var(--spacing-5); }
.rounded { border-radius: var(--radius); }
.rounded-full { border-radius: 9999px; }
.shadow { box-shadow: var(--shadow); }

/* Text Color Utilities */
.text-primary { color: var(--color-primary); }
.text-success { color: var(--color-success); }
.text-danger { color: var(--color-danger); }
.text-warning { color: var(--color-warning); }
.text-accent { color: var(--color-accent); }
.text-purple { color: var(--color-purple); }
.text-orange { color: var(--color-orange); }


/* Background Color Utilities with transparency */
.bg-primary-10 { background-color: var(--color-primary-transparent-10); }
.bg-danger-10 { background-color: var(--color-danger-transparent-10); }
.bg-warning-10 { background-color: var(--color-warning-transparent-10); }
.bg-success-10 { background-color: var(--color-success-transparent-10); }
.bg-purple-10 { background-color: var(--color-purple-transparent-10); }
.bg-orange-10 { background-color: var(--color-orange-transparent-10); }
.bg-accent-10 { background-color: var(--color-accent-transparent-10); }


/* Animations */
.fade-in { animation: fadeIn 0.3s ease; }
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: none; }
}

/* Added spin animation if not using full Tailwind */
.animate-spin {
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar { width: 8px; background: #e5e7eb; }
::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 8px; }

/* Grid Utilities */
.grid {
  display: grid;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.gap-3 {
  gap: var(--spacing-3);
}
.gap-4 {
  gap: var(--spacing-4);
}
.gap-5 {
  gap: var(--spacing-5);
}

/* Page Header Style (if not already global) */
.page-header {
  display: flex; /* Added for default, can be overridden by responsive */
  justify-content: space-between; /* Added for default */
  align-items: center; /* Added for default */
  margin-bottom: var(--spacing-5); /* Consistent with users page */
}
.page-title {
  font-size: 1.8rem; /* Consistent with users page */
  font-weight: 700;
  color: var(--color-text);
  margin: 0;
}

/* Ensure DashboardCard component uses card styles if it's a wrapper */
/* If DashboardCard is just a div, it might need a .card class or similar */

/* Responsive grid adjustments */
@media (min-width: 768px) { /* md breakpoint */
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1024px) { /* lg breakpoint */
  .lg\\:grid-cols-3 { /* Added for 3 columns on large screens */
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Stat Item Styles */
.stat-item {
  background: var(--color-bg-alt); /* --color-card maps to --color-bg-alt */
  border-radius: var(--radius);
  padding: var(--spacing-4); /* Using spacing-4 for a slightly less padded look than original p-5 */
  display: flex;
  align-items: center;
  gap: var(--spacing-3); /* Using spacing-3 */
  box-shadow: var(--shadow);
  transition: transform var(--transition), box-shadow var(--transition);
  border: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg); /* Assuming --shadow-lg is defined or use a stronger shadow */
}

.stat-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--color-accent); /* Default accent color */
  opacity: 0.85;
}

.stat-item.stat-item--critical::before { background: var(--color-danger); }
.stat-item.stat-item--warning::before { background: var(--color-warning); }
.stat-item.stat-item--suspicious::before { background: var(--color-purple); }

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px; /* Slightly smaller */
  height: 44px; /* Slightly smaller */
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

/* Default stat-icon colors (using accent) */
.stat-icon {
  background-color: var(--color-accent-transparent-10);
  color: var(--color-accent);
}
.stat-item.stat-item--critical .stat-icon {
  background-color: var(--color-danger-transparent-10);
  color: var(--color-danger);
}
.stat-item.stat-item--warning .stat-icon {
  background-color: var(--color-warning-transparent-10);
  color: var(--color-warning);
}
.stat-item.stat-item--suspicious .stat-icon {
  background-color: var(--color-purple-transparent-10);
  color: var(--color-purple);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.65rem; /* Adjusted size */
  font-weight: 700;
  color: var(--color-text);
  line-height: 1.2;
}

.stat-label {
  font-size: 0.875rem; /* text-sm */
  color: var(--color-muted);
  margin-top: var(--spacing-1);
}

/* Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.2rem 0.6rem; /* Adjusted padding */
  border-radius: var(--radius-full, 9999px);
  font-size: 0.7rem; /* Adjusted size */
  font-weight: 600; /* font-semibold */
  text-transform: capitalize;
  line-height: 1;
}

.badge.badge--critical { background-color: var(--color-danger-transparent-10); color: var(--color-danger); }
.badge.badge--high { background-color: var(--color-orange-transparent-10); color: var(--color-orange); }
.badge.badge--medium { background-color: var(--color-warning-transparent-10); color: var(--color-warning); }
.badge.badge--low { background-color: var(--color-success-transparent-10); color: var(--color-success); }

/* Loading Spinner specific utilities */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh; /* Use min-height */
  gap: var(--spacing-3); /* Adjusted gap */
}

.loading-spinner {
  width: 36px; /* Adjusted size */
  height: 36px; /* Adjusted size */
  border-width: 3px; /* Use border-width property */
  border-style: solid;
  border-color: var(--color-primary-transparent-10); /* Light border */
  border-top-color: var(--color-primary); /* Accent color for spinner part */
  border-radius: 50%;
  animation: spin 0.8s linear infinite; /* Slightly faster spin */
}

/* Ensure responsive page header utilities are available if not using full Tailwind */
@media (max-width: 768px) { /* md breakpoint or similar */
  .md\\:page-header-col { /* Custom class for this specific responsive behavior */
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }
}
