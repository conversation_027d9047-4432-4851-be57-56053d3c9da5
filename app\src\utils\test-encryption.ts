/**
 * Simple encryption test for React Native
 * Tests if our encryption works without native crypto issues
 */

import CryptoJS from 'crypto-js';

// Simple test function
function testEncryption() {
  console.log('🧪 Testing React Native compatible encryption...');
  
  try {
    const message = "Test message 123";
    const key = "test-key-123";
    const iv = "1234567890123456"; // 16 bytes for AES
    
    // Encrypt
    const encrypted = CryptoJS.AES.encrypt(message, key, {
      iv: CryptoJS.enc.Utf8.parse(iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    console.log('✅ Encryption successful:', encrypted.toString().substring(0, 20) + '...');
    
    // Decrypt
    const decrypted = CryptoJS.AES.decrypt(encrypted.toString(), key, {
      iv: CryptoJS.enc.Utf8.parse(iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    console.log('✅ Decryption successful:', decryptedText);
    
    if (decryptedText === message) {
      console.log('🎉 Encryption test PASSED!');
      return true;
    } else {
      console.log('❌ Encryption test FAILED - mismatch');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Encryption test ERROR:', error);
    return false;
  }
}

// Export for use in React Native
export { testEncryption };
