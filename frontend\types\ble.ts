/**
 * Standardized BLE Type Definitions
 * Consolidates all Bluetooth-related interfaces and types used across the application
 */

// Web Bluetooth API Core Types
export interface BluetoothDevice {
  readonly id: string;
  readonly name?: string;
  readonly gatt?: BluetoothRemoteGATTServer;
  watchAdvertisements?(options?: WatchAdvertisementsOptions): Promise<void>;
  addEventListener(type: string, listener: EventListener): void;
  removeEventListener(type: string, listener: EventListener): void;
  dispatchEvent(event: Event): boolean;
}

export interface BluetoothRemoteGATTServer {
  readonly device: BluetoothDevice;
  readonly connected: boolean;
  connect(): Promise<BluetoothRemoteGATTServer>;
  disconnect(): void;
  getPrimaryService(service: BluetoothServiceUUID): Promise<BluetoothRemoteGATTService>;
  getPrimaryServices(service?: BluetoothServiceUUID): Promise<BluetoothRemoteGATTService[]>;
}

export interface BluetoothRemoteGATTService {
  readonly device: BluetoothDevice;
  readonly uuid: string;
  readonly isPrimary: boolean;
  getCharacteristic(characteristic: BluetoothCharacteristicUUID): Promise<BluetoothRemoteGATTCharacteristic>;
  getCharacteristics(characteristic?: BluetoothCharacteristicUUID): Promise<BluetoothRemoteGATTCharacteristic[]>;
  addEventListener(type: string, listener: EventListener): void;
  removeEventListener(type: string, listener: EventListener): void;
  dispatchEvent(event: Event): boolean;
}

export interface BluetoothRemoteGATTCharacteristic {
  readonly service: BluetoothRemoteGATTService;
  readonly uuid: string;
  readonly properties: BluetoothCharacteristicProperties;
  readonly value?: DataView;
  readValue(): Promise<DataView>;
  writeValue(value: BufferSource): Promise<void>;
  writeValueWithResponse(value: BufferSource): Promise<void>;
  writeValueWithoutResponse(value: BufferSource): Promise<void>;
  startNotifications(): Promise<BluetoothRemoteGATTCharacteristic>;
  stopNotifications(): Promise<BluetoothRemoteGATTCharacteristic>;
  addEventListener(type: string, listener: EventListener): void;
  removeEventListener(type: string, listener: EventListener): void;
  dispatchEvent(event: Event): boolean;
}

export interface BluetoothCharacteristicProperties {
  readonly broadcast: boolean;
  readonly read: boolean;
  readonly writeWithoutResponse: boolean;
  readonly write: boolean;
  readonly notify: boolean;
  readonly indicate: boolean;
  readonly authenticatedSignedWrites: boolean;
  readonly reliableWrite: boolean;
  readonly writableAuxiliaries: boolean;
}

export interface BluetoothLEScanFilter {
  services?: BluetoothServiceUUID[];
  name?: string;
  namePrefix?: string;
  manufacturerData?: { [companyIdentifier: number]: DataViewInit };
  serviceData?: { [serviceUUID: string]: DataViewInit };
}

export interface RequestDeviceOptions {
  filters?: BluetoothLEScanFilter[];
  optionalServices?: BluetoothServiceUUID[];
  acceptAllDevices?: boolean;
}

export interface Bluetooth {
  requestDevice(options?: RequestDeviceOptions): Promise<BluetoothDevice>;
  getAvailability(): Promise<boolean>;
  getDevices?(): Promise<BluetoothDevice[]>;
}

export interface WatchAdvertisementsOptions {
  signal?: AbortSignal;
}

// Type aliases
export type BluetoothServiceUUID = number | string;
export type BluetoothCharacteristicUUID = number | string;
export type DataViewInit = ArrayBuffer | ArrayBufferView;

// Application-specific BLE Types
export interface BleDevice {
  id: string;
  name?: string;
  connected: boolean;
  lastConnected?: Date;
  services?: BleServiceInfo[];
  gatt?: BluetoothRemoteGATTServer;
}

export interface BleServiceInfo {
  uuid: string;
  characteristics: BleCharacteristicInfo[];
}

export interface BleCharacteristicInfo {
  uuid: string;
  properties: {
    read: boolean;
    write: boolean;
    writeWithoutResponse: boolean;
    notify: boolean;
    indicate: boolean;
    broadcast: boolean;
    authenticatedSignedWrites: boolean;
    reliableWrite: boolean;
    writableAuxiliaries: boolean;
  };
}

// Enhanced BLE device interface with advertising data
export interface DiscoveredBleDevice {
  id: string;
  name?: string;
  rssi?: number;
  advertisingData?: {
    localName?: string;
    manufacturerData?: Map<number, DataView>;
    serviceData?: Map<string, DataView>;
    services?: string[];
  };
  lastSeen: Date;
  connectable: boolean;
  deviceType?: 'calculator' | 'generic' | 'unknown';
}

// Connection and Operation Types
export interface BleConnectionOptions {
  serviceFilter?: string[];
  autoReconnect?: boolean;
  connectionTimeout?: number;
}

export interface BleOperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

export type BleConnectionStatus = 'idle' | 'scanning' | 'connecting' | 'connected' | 'disconnected' | 'error';

// Hook Results
export interface UseBleManagerResult {
  // Device management
  permittedDevices: BleDevice[];
  connectedDevices: BleDevice[];
  selectedDevice: BleDevice | null;
  
  // Connection state
  isScanning: boolean;
  isConnecting: boolean;
  connectionStatus: BleConnectionStatus;
  
  // Actions
  scanForDevices: (options?: BleConnectionOptions) => Promise<BluetoothDevice | null>;
  connectToDevice: (device: BleDevice | BluetoothDevice) => Promise<BleOperationResult>;
  disconnectDevice: (deviceId: string) => Promise<BleOperationResult>;
  selectDevice: (device: BleDevice) => void;
  refreshPermittedDevices: () => Promise<void>;
  
  // Device operations
  readCharacteristic: (serviceUuid: string, characteristicUuid: string) => Promise<BleOperationResult>;
  writeCharacteristic: (serviceUuid: string, characteristicUuid: string, value: ArrayBuffer) => Promise<BleOperationResult>;
  subscribeToNotifications: (serviceUuid: string, characteristicUuid: string, callback: (value: DataView) => void) => Promise<BleOperationResult>;
  unsubscribeFromNotifications: (serviceUuid: string, characteristicUuid: string) => Promise<BleOperationResult>;
  
  // Utility functions
  isWebBluetoothSupported: () => boolean;
  getDeviceInfo: (deviceId: string) => BleDevice | null;
  getDeviceServices: (deviceId: string) => BleServiceInfo[];
  
  // Error handling
  lastError: string | null;
  clearError: () => void;
}

// Component Props Types
export interface BleDeviceManagerProps {
  onDeviceSelected: (device: BleDevice) => void;
  onDeviceDisconnected: (deviceId: string) => void;
  onError: (error: string) => void;
  selectedServiceUUIDs?: string[];
  className?: string;
}

export interface CustomBleDeviceScannerProps {
  onDeviceSelected: (device: DiscoveredBleDevice) => void;
  onScanStateChange?: (isScanning: boolean) => void;
  onError?: (error: string) => void;
  serviceFilters?: string[];
  scanDuration?: number;
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface WebBluetoothStatusProps {
  className?: string;
  showInstructions?: boolean;
}

// Note: Web Bluetooth types are provided by @types/web-bluetooth package
