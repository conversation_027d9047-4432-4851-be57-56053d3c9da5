// CSRF Protection Utility
import crypto from 'crypto';

// CSRF Token Cache (in production, use Redis or another distributed cache)
interface TokenData {
  token: string;
  expiresAt: Date;
}

interface TokenCache {
  [key: string]: TokenData;
}

// In-memory token cache
const tokenCache: TokenCache = {};

// Clean expired tokens (run periodically)
const cleanExpiredTokens = (): void => {
  const now = new Date();
  Object.keys(tokenCache).forEach(key => {
    if (tokenCache[key].expiresAt < now) {
      delete tokenCache[key];
    }
  });
};

// Run cleanup every 15 minutes
setInterval(cleanExpiredTokens, 15 * 60 * 1000);

/**
 * Generate a CSRF token for a user
 * @param userId User or admin ID to associate with the token
 * @param expiresInMinutes Token validity period in minutes (default: 60)
 * @returns The generated CSRF token
 */
export const generateCsrfToken = (userId: string, expiresInMinutes: number = 60): string => {
  // Generate a random token
  const token = crypto.randomBytes(32).toString('hex');
  
  // Store the token with expiration
  tokenCache[userId] = {
    token,
    expiresAt: new Date(Date.now() + expiresInMinutes * 60 * 1000)
  };
  
  return token;
};

/**
 * Validate a CSRF token for a user
 * @param userId User or admin ID associated with the token
 * @param token CSRF token to validate
 * @returns Whether the token is valid
 */
export const validateCsrfToken = (userId: string, token: string): boolean => {
  // Check if token exists for user and hasn't expired
  if (!tokenCache[userId]) {
    return false;
  }
  
  const tokenData = tokenCache[userId];
  
  // Check expiration
  if (tokenData.expiresAt < new Date()) {
    delete tokenCache[userId];
    return false;
  }
  
  // Check if token matches
  const isValid = tokenData.token === token;
  
  // One-time use: delete after validation (uncomment if you want tokens to be one-time use)
  // if (isValid) {
  //   delete tokenCache[userId];
  // }
  
  return isValid;
};

/**
 * Invalidate a CSRF token for a user
 * @param userId User or admin ID associated with the token
 */
export const invalidateCsrfToken = (userId: string): void => {
  delete tokenCache[userId];
};

export default {
  generateCsrfToken,
  validateCsrfToken,
  invalidateCsrfToken
};
