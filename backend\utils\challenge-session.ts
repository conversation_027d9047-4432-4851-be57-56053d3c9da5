/**
 * Challenge Session Management Utility
 * 
 * This utility provides functions for managing authentication challenges
 * with secure storage and expiration handling.
 */

import crypto from 'crypto';

// In-memory store for challenges (in production, use Redis or a distributed cache)
interface ChallengeData {
  challenge: string;
  expiresAt: Date;
  username: string;
  attempts: number;
}

interface ChallengeStore {
  [key: string]: ChallengeData;
}

// Challenge store with TTL (Time To Live)
const challengeStore: ChallengeStore = {};

/**
 * Clean expired challenges from the store
 * This should be run periodically to prevent memory leaks
 */
const cleanExpiredChallenges = (): void => {
  const now = new Date();
  
  Object.keys(challengeStore).forEach(key => {
    if (challengeStore[key].expiresAt < now) {
      delete challengeStore[key];
    }
  });
};

// Clean expired challenges every 15 minutes
setInterval(cleanExpiredChallenges, 15 * 60 * 1000);

/**
 * Generate a new challenge for authentication
 * @param username The username this challenge is for
 * @param expiryMinutes How long the challenge is valid for (default: 5 minutes)
 * @returns The challenge string and a challenge ID
 */
export const generateAuthChallenge = (
  username: string, 
  expiryMinutes: number = 5
): { challenge: string; challengeId: string } => {
  // Generate a random challenge
  const challenge = crypto.randomBytes(32).toString('hex');
  
  // Generate a unique ID for this challenge
  const challengeId = crypto.randomBytes(16).toString('hex');
  
  // Store the challenge with expiration
  challengeStore[challengeId] = {
    challenge,
    username,
    attempts: 0,
    expiresAt: new Date(Date.now() + expiryMinutes * 60 * 1000)
  };
  
  return { challenge, challengeId };
};

/**
 * Validate a challenge for the given user
 * @param challengeId The ID of the challenge to validate
 * @param challenge The challenge string to validate
 * @param username The username to validate the challenge for
 * @returns Whether the challenge is valid
 */
export const validateAuthChallenge = (
  challengeId: string,
  challenge: string,
  username: string
): boolean => {
  // Check if the challenge exists
  if (!challengeStore[challengeId]) {
    return false;
  }
  
  const challengeData = challengeStore[challengeId];
  
  // Check if the challenge has expired
  if (challengeData.expiresAt < new Date()) {
    delete challengeStore[challengeId];
    return false;
  }
  
  // Check if the challenge is for the correct user
  if (challengeData.username !== username) {
    // Increment attempts but don't delete yet (to prevent brute force)
    challengeData.attempts += 1;
    return false;
  }
  
  // Check if the challenge matches
  const isValid = challengeData.challenge === challenge;
  
  // Increment attempts
  challengeData.attempts += 1;
  
  // If too many attempts or challenge is valid, remove it (one-time use)
  if (isValid || challengeData.attempts >= 3) {
    delete challengeStore[challengeId];
  }
  
  return isValid;
};

export default {
  generateAuthChallenge,
  validateAuthChallenge
};
