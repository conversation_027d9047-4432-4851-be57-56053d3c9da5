import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { apiClient } from '../../utils/axiosClient';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { withAdminAuth } from '../../contexts/AdminAuthContext';

interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    maxAge: number;
  };
  sessionSettings: {
    sessionTimeout: number;
    maxConcurrentSessions: number;
    rememberMeEnabled: boolean;
  };
  authSettings: {
    twoFactorRequired: boolean;
    ppkEnabled: boolean;
    rateLimiting: {
      enabled: boolean;
      maxAttempts: number;
      windowMs: number;
    };
  };
  auditSettings: {
    logRetentionDays: number;
    enableDetailedLogging: boolean;
    alertOnSuspiciousActivity: boolean;
  };
}

interface SecurityEvent {
  _id: string;
  type: 'failed_login' | 'suspicious_activity' | 'policy_violation' | 'breach_attempt';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  description: string;
  ipAddress: string;
  userAgent?: string;
  userId?: string;
  resolved: boolean;
}

const AdminSecurityPage: NextPage = () => {
  const [settings, setSettings] = useState<SecuritySettings>({
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      maxAge: 90,
    },
    sessionSettings: {
      sessionTimeout: 30,
      maxConcurrentSessions: 3,
      rememberMeEnabled: true,
    },
    authSettings: {
      twoFactorRequired: false,
      ppkEnabled: true,
      rateLimiting: {
        enabled: true,
        maxAttempts: 5,
        windowMs: 15,
      },
    },
    auditSettings: {
      logRetentionDays: 90,
      enableDetailedLogging: true,
      alertOnSuspiciousActivity: true,
    },
  });
  
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const { admin, checkPermission } = useAdminAuth();

  useEffect(() => {
    fetchSecuritySettings();
    fetchSecurityEvents();
  }, []);

  const fetchSecuritySettings = async () => {
    try {
      const response = await apiClient.frontend.get('/api/system/config');
      if (response.data.security) {
        setSettings(response.data.security);
      }
    } catch (err: any) {
      console.error('Failed to fetch security settings:', err);
    }
  };

  const fetchSecurityEvents = async () => {
    try {
      setLoading(true);
      const response = await apiClient.frontend.get('/api/system/audit?category=security&limit=20');
      
      // Transform audit logs to security events
      const events = response.data.auditLogs?.map((log: any) => ({
        _id: log._id,
        type: log.action === 'LOGIN_FAILED' ? 'failed_login' : 'suspicious_activity',
        severity: log.severity || 'medium',
        timestamp: log.timestamp,
        description: log.details?.message || log.action,
        ipAddress: log.ipAddress || 'Unknown',
        userAgent: log.userAgent,
        userId: log.adminId || log.userId,
        resolved: false,
      })) || [];
      
      setSecurityEvents(events);
    } catch (err: any) {
      setError('Failed to fetch security events');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!checkPermission('superadmin')) {
      setError('Insufficient permissions');
      return;
    }

    try {
      setSaving(true);
      setError('');
      
      await apiClient.frontend.put('/api/system/config', {
        security: settings
      });
      
      setSuccess('Security settings saved successfully');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const updatePasswordPolicy = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      passwordPolicy: {
        ...prev.passwordPolicy,
        [field]: value
      }
    }));
  };

  const updateSessionSettings = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      sessionSettings: {
        ...prev.sessionSettings,
        [field]: value
      }
    }));
  };

  const updateAuthSettings = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setSettings(prev => {
        const parentSettings = prev.authSettings[parent as keyof typeof prev.authSettings];
        const currentParentSettings = (typeof parentSettings === 'object' && parentSettings !== null) 
          ? parentSettings as Record<string, any>
          : {};
        
        return {
          ...prev,
          authSettings: {
            ...prev.authSettings,
            [parent]: {
              ...currentParentSettings,
              [child]: value
            }
          }
        };
      });
    } else {
      setSettings(prev => ({
        ...prev,
        authSettings: {
          ...prev.authSettings,
          [field]: value
        }
      }));
    }
  };

  const updateAuditSettings = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      auditSettings: {
        ...prev.auditSettings,
        [field]: value
      }
    }));
  };

  const getSeverityBadge = (severity: string) => {
    const badges = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };
    return badges[severity as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const getEventTypeBadge = (type: string) => {
    const badges = {
      failed_login: 'bg-red-100 text-red-800',
      suspicious_activity: 'bg-orange-100 text-orange-800',
      policy_violation: 'bg-yellow-100 text-yellow-800',
      breach_attempt: 'bg-red-100 text-red-800'
    };
    return badges[type as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  return (
    <>
      <Head>
        <title>Security Settings | CCALC Admin</title>
        <meta name="description" content="Manage security settings and monitor security events for CCALC." />
      </Head>
      <AdminLayout>
        <div className="min-h-screen bg-gray-50 py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900">Security Settings</h1>
              <p className="mt-2 text-gray-600">Configure security policies and monitor security events</p>
            </div>

            {/* Alerts */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
            {success && (
              <div className="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {success}
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Settings Panel */}
              <div className="lg:col-span-2 space-y-6">
                {/* Password Policy */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Password Policy</h2>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Minimum Length
                        </label>
                        <input
                          type="number"
                          min="4"
                          max="50"
                          value={settings.passwordPolicy.minLength}
                          onChange={(e) => updatePasswordPolicy('minLength', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Age (days)
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="365"
                          value={settings.passwordPolicy.maxAge}
                          onChange={(e) => updatePasswordPolicy('maxAge', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.passwordPolicy.requireUppercase}
                          onChange={(e) => updatePasswordPolicy('requireUppercase', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Require uppercase</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.passwordPolicy.requireLowercase}
                          onChange={(e) => updatePasswordPolicy('requireLowercase', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Require lowercase</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.passwordPolicy.requireNumbers}
                          onChange={(e) => updatePasswordPolicy('requireNumbers', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Require numbers</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.passwordPolicy.requireSpecialChars}
                          onChange={(e) => updatePasswordPolicy('requireSpecialChars', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Require special characters</span>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Session Settings */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Session Settings</h2>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Session Timeout (minutes)
                        </label>
                        <input
                          type="number"
                          min="5"
                          max="480"
                          value={settings.sessionSettings.sessionTimeout}
                          onChange={(e) => updateSessionSettings('sessionTimeout', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Concurrent Sessions
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={settings.sessionSettings.maxConcurrentSessions}
                          onChange={(e) => updateSessionSettings('maxConcurrentSessions', parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.sessionSettings.rememberMeEnabled}
                        onChange={(e) => updateSessionSettings('rememberMeEnabled', e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Enable "Remember Me" option</span>
                    </label>
                  </div>
                </div>

                {/* Authentication Settings */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Authentication Settings</h2>
                  </div>
                  <div className="p-6 space-y-4">
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.authSettings.twoFactorRequired}
                          onChange={(e) => updateAuthSettings('twoFactorRequired', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Require Two-Factor Authentication</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.authSettings.ppkEnabled}
                          onChange={(e) => updateAuthSettings('ppkEnabled', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Enable Public-Private Key Authentication</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.authSettings.rateLimiting.enabled}
                          onChange={(e) => updateAuthSettings('rateLimiting.enabled', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Enable Rate Limiting</span>
                      </label>
                    </div>
                    {settings.authSettings.rateLimiting.enabled && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6 border-l-2 border-gray-200">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Max Attempts
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="20"
                            value={settings.authSettings.rateLimiting.maxAttempts}
                            onChange={(e) => updateAuthSettings('rateLimiting.maxAttempts', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Window (minutes)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="60"
                            value={settings.authSettings.rateLimiting.windowMs}
                            onChange={(e) => updateAuthSettings('rateLimiting.windowMs', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Audit Settings */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Audit Settings</h2>
                  </div>
                  <div className="p-6 space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Log Retention (days)
                      </label>
                      <input
                        type="number"
                        min="7"
                        max="365"
                        value={settings.auditSettings.logRetentionDays}
                        onChange={(e) => updateAuditSettings('logRetentionDays', parseInt(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.auditSettings.enableDetailedLogging}
                          onChange={(e) => updateAuditSettings('enableDetailedLogging', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Enable detailed logging</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={settings.auditSettings.alertOnSuspiciousActivity}
                          onChange={(e) => updateAuditSettings('alertOnSuspiciousActivity', e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-700">Alert on suspicious activity</span>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Save Button */}
                <div className="flex justify-end">
                  <button
                    onClick={saveSettings}
                    disabled={saving || !checkPermission('superadmin')}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {saving ? 'Saving...' : 'Save Settings'}
                  </button>
                </div>
              </div>

              {/* Security Events Sidebar */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900">Recent Security Events</h2>
                    <button
                      onClick={fetchSecurityEvents}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                    >
                      Refresh
                    </button>
                  </div>
                  <div className="p-6">
                    {loading ? (
                      <div className="text-center py-4">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-500">Loading events...</p>
                      </div>
                    ) : securityEvents.length === 0 ? (
                      <p className="text-gray-500 text-sm text-center py-4">No security events found</p>
                    ) : (
                      <div className="space-y-3">
                        {securityEvents.slice(0, 10).map((event) => (
                          <div key={event._id} className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between mb-2">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getEventTypeBadge(event.type)}`}>
                                {event.type.replace('_', ' ')}
                              </span>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityBadge(event.severity)}`}>
                                {event.severity}
                              </span>
                            </div>
                            <p className="text-sm text-gray-900 mb-2">{event.description}</p>
                            <div className="text-xs text-gray-500 space-y-1">
                              <p>IP: {event.ipAddress}</p>
                              <p>{new Date(event.timestamp).toLocaleString()}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default withAdminAuth(AdminSecurityPage);
