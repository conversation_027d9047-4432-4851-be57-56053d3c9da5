#!/usr/bin/env node

/**
 * Start Metro bundler on port 8082
 * This bypasses the Expo CLI TypeScript compatibility issues with Node.js 22
 */

const { runCLI } = require('@react-native-community/cli');
const path = require('path');

console.log('🚀 Starting CCALC Metro bundler on port 8082...');
console.log('📱 This will serve the React Native app for Expo Go');
console.log('🔧 Bypassing TypeScript module loading issues');

// Configure metro to start on port 8082
const config = {
  root: __dirname,
  reactNativePath: path.resolve(__dirname, 'node_modules', 'react-native'),
  command: 'start',
  options: {
    port: 8082,
    resetCache: true,
    verbose: true,
  },
};

try {
  // Start Metro directly
  require('metro/src/cli').run(['start', '--port', '8082', '--config', 'metro.config.js']);
} catch (error) {
  console.error('❌ Failed to start Metro:', error.message);
  console.log('💡 Falling back to npx metro...');
  
  // Fallback: use npx
  const { spawn } = require('child_process');
  const metro = spawn('npx', ['metro', 'start', '--port', '8082', '--config', 'metro.config.js'], {
    stdio: 'inherit',
    cwd: __dirname,
  });
  
  metro.on('error', (err) => {
    console.error('❌ Metro failed to start:', err);
    process.exit(1);
  });
  
  metro.on('exit', (code) => {
    console.log(`🛑 Metro exited with code ${code}`);
    process.exit(code);
  });
}
