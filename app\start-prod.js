#!/usr/bin/env node

/**
 * Simple script to start Expo bypassing TypeScript loading issues
 */
const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting CCALC app with production mode to bypass TypeScript issues...');

try {
  // Run expo with the no-dev flag to bypass TypeScript issues
  execSync('npx expo start --port 8082 --no-dev', {
    stdio: 'inherit',
    cwd: __dirname
  });
} catch (error) {
  console.error('❌ Error starting Expo:', error.message);
  process.exit(1);
}
