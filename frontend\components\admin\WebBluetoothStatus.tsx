import React, { useState, useEffect } from 'react';
import { 
  isWebBluetoothSupported,
  validateBleEnvironment 
} from '../../utils/ble';
import { CONNECTION_CONFIG } from '../../constants/ble';

interface WebBluetoothStatusProps {
  className?: string;
  showInstructions?: boolean;
}

const WebBluetoothStatus: React.FC<WebBluetoothStatusProps> = ({ 
  className = '',
  showInstructions = true 
}) => {
  const [bluetoothSupported, setBluetoothSupported] = useState<boolean | null>(null);
  const [bluetoothAvailable, setBluetoothAvailable] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    checkBluetoothSupport();
      // Set up periodic availability checking using standardized interval
    const intervalId = setInterval(checkBluetoothSupport, CONNECTION_CONFIG.AUTO_REFRESH_INTERVAL);
    
    return () => clearInterval(intervalId);
  }, []);
  const checkBluetoothSupport = async () => {
    try {
      // Use standardized validation
      await validateBleEnvironment();
      setBluetoothSupported(true);
      setBluetoothAvailable(true);
    } catch (error) {
      // Fall back to individual checks
      const supported = isWebBluetoothSupported();
      setBluetoothSupported(supported);
      
      if (supported) {
        try {
          const available = await navigator.bluetooth.getAvailability();
          setBluetoothAvailable(available);
        } catch (availabilityError) {
          console.warn('Could not check Bluetooth availability:', availabilityError);
          setBluetoothAvailable(false);
        }
      } else {
        setBluetoothAvailable(false);
      }
    } finally {
      setIsChecking(false);
    }
  };

  const getStatusColor = () => {
    if (isChecking) return 'bg-gray-100 text-gray-600 border-gray-200';
    if (!bluetoothSupported) return 'bg-red-100 text-red-800 border-red-200';
    if (!bluetoothAvailable) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-green-100 text-green-800 border-green-200';
  };

  const getStatusText = () => {
    if (isChecking) return 'Checking...';
    if (!bluetoothSupported) return 'Not Supported';
    if (!bluetoothAvailable) return 'Disabled';
    return 'Available';
  };

  const getStatusIcon = () => {
    if (isChecking) {
      return (
        <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      );
    }
    
    if (!bluetoothSupported) {
      return (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
      );
    }
    if (!bluetoothAvailable) {
      return (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      );
    }
    return (
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    );
  };

  const isReady = bluetoothSupported && bluetoothAvailable;

  return (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-medium text-gray-900">Web Bluetooth Status</h4>
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor()}`}>
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>
      </div>

      {showInstructions && (
        <>
          {!bluetoothSupported && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h5 className="font-medium text-red-900 mb-2">Web Bluetooth Not Supported</h5>
              <p className="text-sm text-red-700 mb-3">
                Your browser doesn't support Web Bluetooth API. BLE features will not work.
              </p>
              <div className="text-sm text-red-600">
                <strong>Supported Browsers:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Google Chrome (version 56+)</li>
                  <li>Microsoft Edge (version 79+)</li>
                  <li>Opera (version 43+)</li>
                  <li>Samsung Internet (version 6.2+)</li>
                </ul>
              </div>
            </div>
          )}

          {bluetoothSupported && !bluetoothAvailable && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h5 className="font-medium text-yellow-900 mb-2">Web Bluetooth Disabled</h5>
              <p className="text-sm text-yellow-700 mb-3">
                Web Bluetooth is supported but currently disabled. Follow these steps to enable it:
              </p>
              <div className="text-sm text-yellow-600">
                <strong>Chrome/Edge Instructions:</strong>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Navigate to <code className="bg-yellow-100 px-1 rounded">chrome://flags</code> (or <code className="bg-yellow-100 px-1 rounded">edge://flags</code>)</li>
                  <li>Search for "Experimental Web Platform features"</li>
                  <li>Enable this flag</li>
                  <li>Restart your browser</li>
                </ol>
                <div className="mt-3 p-3 bg-yellow-100 rounded border border-yellow-300">
                  <p className="font-medium text-yellow-900">Alternative Methods:</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>Ensure HTTPS is used for production environments</li>
                    <li>Check that no security policies block Web Bluetooth</li>
                    <li>For development: Use <code className="bg-yellow-100 px-1">localhost</code> instead of IP addresses</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {isReady && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h5 className="font-medium text-green-900 mb-2">Web Bluetooth Ready</h5>
              <p className="text-sm text-green-700">
                Web Bluetooth is supported and enabled. You can now scan for and connect to BLE devices.
              </p>
            </div>
          )}
        </>
      )}

      {!showInstructions && !isReady && (
        <div className="text-sm text-gray-600">
          {!bluetoothSupported 
            ? 'Web Bluetooth is not supported in this browser.' 
            : 'Web Bluetooth is disabled. Enable it in browser settings.'}
        </div>
      )}
    </div>
  );
};

export default WebBluetoothStatus;
