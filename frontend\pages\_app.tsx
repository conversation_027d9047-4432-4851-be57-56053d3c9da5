import type { AppProps } from 'next/app';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { AuthProvider } from '../utils/authProvider';
import { AdminAuthProvider } from '../contexts/AdminAuthContext';
import { ToastProvider } from '../contexts/ToastContext';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import ActivityTracker from '../components/ActivityTracker';
import SessionWarning from '../components/SessionWarning';
import RouteGuard from '../components/auth/RouteGuard';
import '../styles/globals.css';
import '../styles/auth.css';
import '../styles/admin.css';

// Create a theme instance
const theme = createTheme({
  palette: {
    primary: {
      main: '#0070f3',
    },
    secondary: {
      main: '#19857b',
    },
    background: {
      default: '#f7f9fc',
    },
  },
});

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const isAdminRoute = router.pathname.startsWith('/admin');

  const AuthProviderToUse = isAdminRoute ? AdminAuthProvider : AuthProvider;

  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="CCALC Admin Portal - Secure end-to-end encrypted messaging platform" />
        <meta name="theme-color" content="#0070f3" />
      </Head>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <ToastProvider>
          <RouteGuard>
            <ActivityTracker>
              <AuthProviderToUse>
                <Component {...pageProps} />
                <SessionWarning />
              </AuthProviderToUse>
            </ActivityTracker>
          </RouteGuard>
        </ToastProvider>
      </ThemeProvider>
    </>
  );
}

export default MyApp;
