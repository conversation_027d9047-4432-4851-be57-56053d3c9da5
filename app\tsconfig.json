{"compilerOptions": {"jsx": "react-jsx", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "noEmit": true, "target": "esnext", "lib": ["esnext", "dom"], "allowJs": true, "isolatedModules": true, "baseUrl": ".", "paths": {"*": ["*", "*.ios", "*.android"]}, "module": "esnext"}, "extends": "expo/tsconfig.base", "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}