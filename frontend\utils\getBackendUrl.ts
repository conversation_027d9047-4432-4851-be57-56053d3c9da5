/**
 * Backend connection utility for server-side API routes
 * This utility helps manage backend URLs in both development and Docker environments
 */

export const getBackendUrl = (): string => {
  // Log hostname to help with debugging Docker issues
  if (typeof process !== 'undefined') {
    try {
      const os = require('os');
      console.log('Current hostname:', os.hostname());
    } catch (e) {
      // Ignore errors if os module is not available
    }
  }

  // First try server-side only environment variable
  if (process.env.BACKEND_URL) {
    console.log('Using BACKEND_URL:', process.env.BACKEND_URL);
    return process.env.BACKEND_URL;
  }
  
  // Then try public environment variable
  if (process.env.NEXT_PUBLIC_BACKEND_URL) {
    console.log('Using NEXT_PUBLIC_BACKEND_URL:', process.env.NEXT_PUBLIC_BACKEND_URL);
    return process.env.NEXT_PUBLIC_BACKEND_URL;
  }
  // Check if we're in a Docker environment by checking the hostname
  // Docker containers typically have hostnames that are their container IDs
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'production') {
    // In Docker production environment, use the service name from docker-compose
    console.log('Using Docker service name for backend: http://backend:3000');
    return 'http://backend:3000';
  }
  
  // Fallback for local development
  console.log('Using fallback localhost backend URL: http://localhost:3000');
  return 'http://localhost:3000';
};

export default getBackendUrl;
