import sharp from 'sharp';
import ffmpeg from 'fluent-ffmpeg';
import path from 'path';

export async function generateThumbnail(filePath: string, mimeType: string): Promise<Buffer | null> {
  try {
    if (mimeType.startsWith('image/')) {
      return await sharp(filePath)
        .resize(200, 200, { 
          fit: 'inside',
          withoutEnlargement: true 
        })
        .jpeg({ quality: 80 })
        .toBuffer();
    }
    
    if (mimeType.startsWith('video/')) {
      return new Promise((resolve, reject) => {
        const outputPath = path.join(process.cwd(), 'temp', `thumb_${Date.now()}.jpg`);
        
        ffmpeg(filePath)
          .screenshot({
            timestamps: ['50%'],
            filename: path.basename(outputPath),
            folder: path.dirname(outputPath),
            size: '200x200'
          })
          .on('end', async () => {
            try {
              const thumbnail = await sharp(outputPath)
                .jpeg({ quality: 80 })
                .toBuffer();
              resolve(thumbnail);
            } catch (error) {
              reject(error);
            }
          })
          .on('error', reject);
      });
    }
    
    return null;
  } catch (error) {
    console.error('Thumbnail generation error:', error);
    return null;
  }
}
