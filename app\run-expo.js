// Simple script to run Expo without TypeScript issues
const { exec } = require('child_process');
const path = require('path');

console.log('🚀 Starting CCALC with JavaScript wrapper...');

// Set the NODE_OPTIONS environment variable
process.env.NODE_OPTIONS = '--experimental-vm-modules';

// Set the EXPO_LEGACY_IMPORTS environment variable
process.env.EXPO_LEGACY_IMPORTS = '1';

// Set the NODE_ENV environment variable to production
process.env.NODE_ENV = 'production';

// Start Expo with the no-dev flag
const expoProcess = exec('npx expo-cli start --port 8082 --no-dev', {
  cwd: __dirname
});

// Handle output
expoProcess.stdout.on('data', (data) => {
  console.log(data);
});

expoProcess.stderr.on('data', (data) => {
  console.error(data);
});

// Handle process exit
expoProcess.on('exit', (code) => {
  console.log(`Expo process exited with code ${code}`);
  process.exit(code);
});

// Handle process errors
expoProcess.on('error', (err) => {
  console.error('Failed to start Expo:', err);
  process.exit(1);
});
