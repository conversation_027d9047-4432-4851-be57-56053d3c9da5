import React from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';

const SuperuserPage: React.FC = () => (
  <>
    <Head>
      <title>Superuser Management | CCALC Admin</title>
    </Head>
    <AdminLayout>
      <div className="container">
        <div className="card">
          <h2>Superuser Management</h2>
          <p className="text-muted mb-3">Manage superuser profile, inbox, and settings. (UI placeholder for future integration.)</p>
          <div className="alert alert-info">Superuser management features coming soon.</div>
        </div>
      </div>
    </AdminLayout>
  </>
);

export default withAdminAuth(SuperuserPage);
