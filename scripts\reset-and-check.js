const mongoose = require('mongoose');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema
const UserSchema = new mongoose.Schema({
  username: String,
  deviceFingerprintHash: String,
  bleUUIDHash: String,
  status: String,
  unlockExpression: String,
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function resetAndCheck() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // Reset device registration
    const result = await User.updateOne(
      { username: 'mobileuser' },
      { 
        $unset: { 
          deviceFingerprintHash: "",
          bleUUIDHash: "",
          deviceMetadata: ""
        },
        $set: { 
          status: 'pending_device_registration'
        }
      }
    );

    console.log('✅ Reset device registration');

    // Check current user state
    const user = await User.findOne({ username: 'mobileuser' });
    if (user) {
      console.log('\n📱 Mobile App Test Credentials:');
      console.log(`Username: ${user.username}`);
      console.log(`Expression: ${user.unlockExpression}`);
      console.log(`Status: ${user.status}`);
      console.log('\n🎯 IMPORTANT: Enter this EXACT expression in the calculator:');
      console.log(`"${user.unlockExpression}"`);
      console.log('Do NOT enter "5+3" or any other expression!');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

resetAndCheck();
