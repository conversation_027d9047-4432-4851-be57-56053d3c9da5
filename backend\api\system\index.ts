import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import AdminModel from '../../models/Admin';
import ChatModel from '../../models/Chat';
import CallModel from '../../models/Call';
import DeviceModel from '../../models/Device';
import BuildModel from '../../models/Build';
import MediaModel from '../../models/Media';
import mongoose from 'mongoose';
import { exec } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

const router = Router();
const execAsync = promisify(exec);

// System shutdown flag
let isShuttingDown = false;
let shutdownTimeout: NodeJS.Timeout | null = null;

/**
 * Get system health and statistics
 * GET /api/system/health
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;

    // Database connection status
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // System metrics
    const systemMetrics = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      systemUptime: os.uptime(),
      loadAverage: os.loadavg(),
      freeMemory: os.freemem(),
      totalMemory: os.totalmem()
    };

    // Database statistics
    const dbStats = {
      users: await UserModel.countDocuments(),
      admins: await AdminModel.countDocuments(),
      chats: await ChatModel.countDocuments(),
      calls: await CallModel.countDocuments(),
      devices: await DeviceModel.countDocuments(),
      builds: await BuildModel.countDocuments(),
      media: await MediaModel.countDocuments(),
      auditLogs: await AuditLogModel.countDocuments()
    };

    // Active connections and sessions
    const activeStats = {
      activeUsers: await UserModel.countDocuments({ 
        lastActivity: { $gte: new Date(Date.now() - 15 * 60 * 1000) } // Last 15 minutes
      }),
      activeCalls: await CallModel.countDocuments({ status: 'active' }),
      pendingBuilds: await BuildModel.countDocuments({ status: { $in: ['pending', 'building'] } }),
      recentErrors: await AuditLogModel.countDocuments({
        severity: { $in: ['high', 'critical'] },
        timestamp: { $gte: new Date(Date.now() - 60 * 60 * 1000) } // Last hour
      })
    };

    // Security metrics
    const securityMetrics = {
      suspendedDevices: await DeviceModel.countDocuments({ status: 'suspended' }),
      failedLogins: await AuditLogModel.countDocuments({
        action: { $regex: /login.*failed/ },
        timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      }),
      securityViolations: await AuditLogModel.countDocuments({
        category: 'security',
        severity: { $in: ['high', 'critical'] },
        timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
      })
    };    await AuditLogModel.create({
      logId: `health_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId: adminId,
      event: {
        type: 'system_action',
        action: 'system_health_checked',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'system-health-check',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system_admin',
        retention: 'short',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { systemStatus: 'healthy', dbStatus }
      }
    });

    res.json({
      success: true,
      health: {
        status: 'healthy',
        timestamp: new Date(),
        database: { status: dbStatus },
        system: systemMetrics,
        statistics: dbStats,
        active: activeStats,
        security: securityMetrics
      }
    });

  } catch (error) {
    console.error('System health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check system health',
      health: {
        status: 'unhealthy',
        timestamp: new Date(),
        error: (error as Error).message
      }
    });
  }
});

/**
 * Get detailed system statistics
 * GET /api/system/stats
 */
router.get('/stats', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { period = '24h' } = req.query;
    
    let dateFilter: Date;
    switch (period) {
      case '1h':
        dateFilter = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        dateFilter = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    // Activity trends
    const activityTrends = await AuditLogModel.aggregate([
      {
        $match: {
          timestamp: { $gte: dateFilter }
        }
      },
      {
        $group: {
          _id: {
            hour: { $hour: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' },
            month: { $month: '$timestamp' }
          },
          totalActions: { $sum: 1 },
          securityEvents: {
            $sum: { $cond: [{ $eq: ['$category', 'security'] }, 1, 0] }
          },
          errors: {
            $sum: { $cond: [{ $in: ['$severity', ['high', 'critical']] }, 1, 0] }
          }
        }
      },
      {
        $sort: { '_id.month': 1, '_id.day': 1, '_id.hour': 1 }
      }
    ]);

    // User activity
    const userActivity = await UserModel.aggregate([
      {
        $match: {
          lastActivity: { $gte: dateFilter }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$lastActivity'
            }
          },
          activeUsers: { $sum: 1 }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Communication stats
    const communicationStats = {
      messages: await ChatModel.aggregate([
        {
          $match: {
            'messages.timestamp': { $gte: dateFilter }
          }
        },
        {
          $unwind: '$messages'
        },
        {
          $match: {
            'messages.timestamp': { $gte: dateFilter }
          }
        },
        {
          $group: {
            _id: null,
            totalMessages: { $sum: 1 },
            ephemeralMessages: {
              $sum: { $cond: [{ $eq: ['$messages.isEphemeral', true] }, 1, 0] }
            }
          }
        }
      ]),
      calls: await CallModel.aggregate([
        {
          $match: {
            startTime: { $gte: dateFilter }
          }
        },
        {
          $group: {
            _id: null,
            totalCalls: { $sum: 1 },
            completedCalls: {
              $sum: { $cond: [{ $eq: ['$status', 'ended'] }, 1, 0] }
            },
            recordedCalls: {
              $sum: { $cond: [{ $eq: ['$isRecorded', true] }, 1, 0] }
            },
            averageDuration: {
              $avg: {
                $subtract: ['$endTime', '$startTime']
              }
            }
          }
        }
      ])
    };

    // Storage usage
    const storageStats = await MediaModel.aggregate([
      {
        $group: {
          _id: null,
          totalFiles: { $sum: 1 },
          totalSize: { $sum: '$size' },
          byType: {
            $push: {
              type: '$mimeType',
              size: '$size'
            }
          }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        period,
        activityTrends,
        userActivity,
        communication: {
          messages: communicationStats.messages[0] || { totalMessages: 0, ephemeralMessages: 0 },
          calls: communicationStats.calls[0] || { totalCalls: 0, completedCalls: 0, recordedCalls: 0, averageDuration: 0 }
        },
        storage: storageStats[0] || { totalFiles: 0, totalSize: 0, byType: [] }
      }
    });

  } catch (error) {
    console.error('System stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system statistics'
    });
  }
});

/**
 * Backup database
 * POST /api/system/backup
 */
router.post('/backup', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { includeMedia = false, compression = true } = req.body;
    const adminId = req.admin?.id;

    const backupId = crypto.randomUUID();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(process.cwd(), 'backups', `backup-${timestamp}-${backupId}`);

    // Create backup directory
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Backup database collections
    const collections = [
      'users', 'admins', 'chats', 'calls', 
      'devices', 'builds', 'media', 'auditlogs'
    ];

    const backupPromises = collections.map(async (collection) => {
      try {
        const { stdout } = await execAsync(
          `mongodump --collection ${collection} --db ${process.env.MONGODB_DB || 'ccalc'} --out ${backupDir}`
        );
        return { collection, success: true, output: stdout };
      } catch (error) {
        return { collection, success: false, error: (error as Error).message };
      }
    });

    const backupResults = await Promise.all(backupPromises);

    // Backup media files if requested
    let mediaBackupResult = null;
    if (includeMedia) {
      try {
        const mediaDir = path.join(process.cwd(), 'uploads');
        const mediaBackupDir = path.join(backupDir, 'media');
        
        if (fs.existsSync(mediaDir)) {
          await execAsync(`cp -r ${mediaDir} ${mediaBackupDir}`);
          mediaBackupResult = { success: true };
        }
      } catch (error) {
        mediaBackupResult = { success: false, error: (error as Error).message };
      }
    }

    // Create backup manifest
    const manifest = {
      backupId,
      timestamp: new Date(),
      adminId,
      collections: backupResults,
      includeMedia,
      mediaBackup: mediaBackupResult,
      compression,
      version: process.env.APP_VERSION || '1.0.0'
    };

    fs.writeFileSync(
      path.join(backupDir, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );

    // Compress backup if requested
    if (compression) {
      try {
        const archivePath = `${backupDir}.tar.gz`;
        await execAsync(`tar -czf ${archivePath} -C ${path.dirname(backupDir)} ${path.basename(backupDir)}`);
        
        // Remove uncompressed directory
        await execAsync(`rm -rf ${backupDir}`);
        
        (manifest as any).archivePath = archivePath;
      } catch (error) {
        console.error('Compression error:', error);
      }
    }

    await AuditLogModel.create({
      logId: `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId: adminId,
      event: {
        type: 'system_action',
        action: 'database_backup_created',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'system-backup',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { 
          backupId, 
          includeMedia, 
          compression,
          collections: backupResults.map(r => ({ name: r.collection, success: r.success }))
        }
      }
    });

    res.json({
      success: true,
      backup: {
        backupId,
        timestamp: manifest.timestamp,
        collections: backupResults,
        mediaBackup: mediaBackupResult,
        path: compression ? `${backupDir}.tar.gz` : backupDir
      }
    });

  } catch (error) {
    console.error('Database backup error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create database backup'
    });
  }
});

/**
 * Get security audit logs
 * GET /api/system/audit
 */
router.get('/audit', authenticateToken, async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 50,
      severity,
      category,
      action,
      userId,
      startDate,
      endDate
    } = req.query;

    const query: any = {};
    if (severity) query.severity = severity;
    if (category) query.category = category;
    if (action) query.action = { $regex: action, $options: 'i' };
    if (userId) query.userId = userId;

    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate as string);
      if (endDate) query.timestamp.$lte = new Date(endDate as string);
    }

    const auditLogs = await AuditLogModel.find(query)
      .sort({ timestamp: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit))
      .populate('userId', 'username email');

    const total = await AuditLogModel.countDocuments(query);

    // Security summary
    const securitySummary = await AuditLogModel.aggregate([
      {
        $match: {
          category: 'security',
          timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: '$severity',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      auditLogs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      },
      securitySummary
    });

  } catch (error) {
    console.error('Audit logs fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit logs'
    });
  }
});

/**
 * Clear old audit logs and temporary files
 * POST /api/system/cleanup
 */
router.post('/cleanup', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { olderThanDays = 90, includeMedia = false } = req.body;
    const adminId = req.admin?.id;

    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // Clean up audit logs
    const auditCleanup = await AuditLogModel.deleteMany({
      timestamp: { $lt: cutoffDate },
      severity: { $nin: ['critical'] } // Keep critical logs
    });

    // Clean up expired builds
    const buildCleanup = await BuildModel.deleteMany({
      'expiry.expiresAt': { $lt: new Date() },
      status: { $nin: ['active'] }
    });

    // Clean up orphaned media files
    let mediaCleanup = { deletedCount: 0 };
    if (includeMedia) {
      const orphanedMedia = await MediaModel.find({
        uploadedAt: { $lt: cutoffDate },
        accessCount: 0
      });

      for (const media of orphanedMedia) {
        try {
          const filePath = path.join(process.cwd(), 'uploads', (media as any).filename);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        } catch (error) {
          console.error('Error deleting media file:', error);
        }
      }

      mediaCleanup = await MediaModel.deleteMany({
        uploadedAt: { $lt: cutoffDate },
        accessCount: 0
      });
    }

    // Clean up temporary files
    const tempDir = path.join(process.cwd(), 'temp');
    let tempCleanup = { deletedCount: 0 };
    if (fs.existsSync(tempDir)) {
      try {
        const tempFiles = fs.readdirSync(tempDir);
        for (const file of tempFiles) {
          const filePath = path.join(tempDir, file);
          const stat = fs.statSync(filePath);
          if (stat.mtime < cutoffDate) {
            fs.unlinkSync(filePath);
            tempCleanup.deletedCount++;
          }
        }
      } catch (error) {
        console.error('Error cleaning temp files:', error);
      }
    }

    await AuditLogModel.create({
      logId: `cleanup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId: adminId,
      event: {
        type: 'system_action',
        action: 'system_cleanup_performed',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'system-cleanup',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system',
        retention: 'medium',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: {
          cutoffDate,
          auditLogsDeleted: auditCleanup.deletedCount,
          buildsDeleted: buildCleanup.deletedCount,
          mediaDeleted: mediaCleanup.deletedCount,
          tempFilesDeleted: tempCleanup.deletedCount
        }
      }
    });

    res.json({
      success: true,
      cleanup: {
        auditLogs: auditCleanup.deletedCount,
        builds: buildCleanup.deletedCount,
        media: mediaCleanup.deletedCount,
        tempFiles: tempCleanup.deletedCount,
        cutoffDate
      }
    });

  } catch (error) {
    console.error('System cleanup error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform system cleanup'
    });
  }
});

/**
 * Initiate graceful server shutdown
 * POST /api/system/shutdown
 */
router.post('/shutdown', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { delay = 30, reason = 'Maintenance' } = req.body;
    const adminId = req.admin?.id;

    if (isShuttingDown) {
      res.status(400).json({
        success: false,
        error: 'Shutdown already in progress'
      });

      return;
    }

    isShuttingDown = true;

    await AuditLogModel.create({
      logId: `shutdown_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId: adminId,
      event: {
        type: 'system_action',
        action: 'server_shutdown_initiated',
        result: 'success',
        severity: 'high'
      },
      context: {
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'system-shutdown',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { delay, reason }
      }
    });

    // Set shutdown timeout
    shutdownTimeout = setTimeout(async () => {
      try {
        // Close database connections
        await mongoose.connection.close();
        
        // Log final shutdown
        console.log('Server shutting down gracefully...');
        process.exit(0);
      } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
      }
    }, delay * 1000);

    res.json({
      success: true,
      message: `Server will shutdown in ${delay} seconds`,
      shutdownTime: new Date(Date.now() + delay * 1000),
      reason
    });

  } catch (error) {
    console.error('Server shutdown error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate server shutdown'
    });
  }
});

/**
 * Cancel server shutdown
 * POST /api/system/cancel-shutdown
 */
router.post('/cancel-shutdown', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;

    if (!isShuttingDown) {
      res.status(400).json({
        success: false,
        error: 'No shutdown in progress'
      });

      return;
    }

    if (shutdownTimeout) {
      clearTimeout(shutdownTimeout);
      shutdownTimeout = null;
    }

    isShuttingDown = false;

    await AuditLogModel.create({
      logId: `shutdown_cancel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId: adminId,
      event: {
        type: 'system_action',
        action: 'server_shutdown_cancelled',
        result: 'success',
        severity: 'medium'
      },
      context: {
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'system-shutdown-cancel',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'system',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: { cancelledBy: adminId }
      }
    });

    res.json({
      success: true,
      message: 'Server shutdown cancelled'
    });

  } catch (error) {
    console.error('Cancel shutdown error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel server shutdown'
    });
  }
});

/**
 * Get system configuration
 * GET /api/system/config
 */
router.get('/config', authenticateToken, async (req: Request, res: Response) => {
  try {
    const config = {
      environment: process.env.NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      database: {
        uri: process.env.MONGODB_URI?.replace(/\/\/.*:.*@/, '//***:***@') || 'Not configured',
        name: process.env.MONGODB_DB || 'ccalc'
      },
      server: {
        host: process.env.HOST || 'localhost',
        port: process.env.PORT || 3000,
        ssl: process.env.SSL_ENABLED === 'true'
      },
      security: {
        jwtExpiry: process.env.JWT_EXPIRES_IN || '24h',
        passwordSaltRounds: process.env.BCRYPT_SALT_ROUNDS || 12,
        maxLoginAttempts: process.env.MAX_LOGIN_ATTEMPTS || 5
      },
      features: {
        mediaUpload: process.env.ENABLE_MEDIA_UPLOAD !== 'false',
        voiceCalls: process.env.ENABLE_VOICE_CALLS !== 'false',
        buildGeneration: process.env.ENABLE_BUILD_GENERATION !== 'false',
        backups: process.env.ENABLE_BACKUPS !== 'false'
      },
      limits: {
        maxFileSize: process.env.MAX_FILE_SIZE || '100MB',
        maxUsers: process.env.MAX_USERS || 'unlimited',
        rateLimit: process.env.RATE_LIMIT || '100/hour'
      }
    };

    res.json({
      success: true,
      config
    });

  } catch (error) {
    console.error('System config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system configuration'
    });
  }
});

export default router;
