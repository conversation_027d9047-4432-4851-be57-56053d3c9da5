import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { apiClient } from '../../utils/axiosClient';
import AdminLayout from '../../components/admin/AdminLayout';
import DashboardCard from '../../components/admin/DashboardCard';
import Button from '../../components/admin/Button';
import StatCard from '../../components/admin/StatCard';

interface SystemStats {
  activeUsers: number;
  totalCalls: number;
  totalChats: number;
  storageUsed: string;
  lastBackup: string;
  serverStatus: {
    cpu: string;
    memory: string;
    uptime: string;
  };
}

export default function Controls() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null);
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [encryptionStatus, setEncryptionStatus] = useState(true);
  const [backupInProgress, setBackupInProgress] = useState(false);
  const router = useRouter();
  
  useEffect(() => {
    // Check if admin is logged in and fetch system data
    const fetchSystemData = async () => {
      try {
        // First check session
        await apiClient.frontend.get('/api/auth/admin/session');

        // Then fetch system stats
        const response = await apiClient.frontend.get('/api/controls/system-stats');
        const data = response.data;
        setSystemStats(data);
        setMaintenanceMode(data.maintenanceMode || false);
        setEncryptionStatus(data.encryptionEnabled !== false);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'An error occurred');
        // Redirect to login page if not authenticated
        if (err.response?.status === 401) {
          router.push('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSystemData();
  }, [router]);
  
  const handleMaintenanceToggle = async () => {
    try {
      setLoading(true);
      await apiClient.frontend.post('/api/controls/maintenance', { 
        enabled: !maintenanceMode 
      });
      setMaintenanceMode(!maintenanceMode);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to toggle maintenance mode');
    } finally {
      setLoading(false);
    }
  };
  
  const handleEncryptionToggle = async () => {
    try {
      if (!confirm(`Are you sure you want to ${encryptionStatus ? 'disable' : 'enable'} encryption? This is a critical security setting.`)) {
        return;
      }

      setLoading(true);
      await apiClient.frontend.post('/api/controls/encryption', { 
        enabled: !encryptionStatus 
      });
      setEncryptionStatus(!encryptionStatus);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to toggle encryption');
    } finally {
      setLoading(false);
    }
  };
  
  const handleBackup = async () => {
    try {
      if (backupInProgress) {
        return;
      }

      setBackupInProgress(true);
      setError('');
      
      await apiClient.frontend.post('/api/controls/backup');

      // Update system stats to reflect new backup time
      const response = await apiClient.frontend.get('/api/controls/system-stats');
      setSystemStats(response.data);

      alert('System backup completed successfully');
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Backup failed');
    } finally {
      setBackupInProgress(false);
    }
  };
  
  const handleResetServer = async () => {
    if (!confirm('Are you sure you want to restart the server? All active connections will be terminated.')) {
      return;
    }
    
    try {
      setLoading(true);
      await apiClient.frontend.post('/api/controls/restart-server');

      alert('Server restart initiated. Please wait a few moments for the server to come back online.');
      // Wait a bit then refresh the page
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Failed to restart server');
      setLoading(false);
    }
  };

  if (loading && !systemStats) {
    return (
      <AdminLayout>
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading system data...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>CCALC Admin - System Controls</title>
      </Head>
      
      <div className="page-header">
        <h2 className="page-title">System Controls</h2>
      </div>
      
      {error && (
        <div className="alert alert-error">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
          <span>{error}</span>
          <button onClick={() => setError('')} className="alert-close">×</button>
        </div>
      )}
      
      {systemStats && (
        <div className="stat-grid">
          <StatCard 
            title="Active Users" 
            value={systemStats.activeUsers.toString()}
            color="var(--color-accent)"
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            }
          />
          
          <StatCard 
            title="Total Calls" 
            value={systemStats.totalCalls.toString()}
            color="#0ea5e9" // sky blue
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
            }
          />
          
          <StatCard 
            title="Total Chats" 
            value={systemStats.totalChats.toString()}
            color="#8b5cf6" // purple
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            }
          />
          
          <StatCard 
            title="Storage Used" 
            value={systemStats.storageUsed}
            color="#f59e0b" // amber
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 3H3v18h18V3z"></path>
                <path d="M21 12H3"></path>
                <path d="M12 3v18"></path>
              </svg>
            }
          />
        </div>
      )}
      
      <div className="card-grid">
        <DashboardCard title="Server Status">
          {systemStats && (
            <div className="status-grid">
              <div className="status-item">
                <span className="status-label">CPU Usage</span>
                <span className="status-value">{systemStats.serverStatus.cpu}</span>
              </div>
              <div className="status-item">
                <span className="status-label">Memory Usage</span>
                <span className="status-value">{systemStats.serverStatus.memory}</span>
              </div>
              <div className="status-item">
                <span className="status-label">Uptime</span>
                <span className="status-value">{systemStats.serverStatus.uptime}</span>
              </div>
              <div className="status-item">
                <span className="status-label">Last Backup</span>
                <span className="status-value">{systemStats.lastBackup}</span>
              </div>
            </div>
          )}
        </DashboardCard>
        
        <DashboardCard title="System Controls">
          <div className="controls-list">
            <div className="control-item">
              <div className="control-info">
                <h4 className="control-name">Maintenance Mode</h4>
                <p className="control-description">
                  When enabled, users will be notified that the system is under maintenance.
                </p>
              </div>
              <div className="control-action">
                <div className={`toggle-switch ${maintenanceMode ? 'toggle-switch--active' : ''}`} onClick={handleMaintenanceToggle}>
                  <div className="toggle-switch__slider"></div>
                </div>
              </div>
            </div>
            
            <div className="control-item">
              <div className="control-info">
                <h4 className="control-name">End-to-End Encryption</h4>
                <p className="control-description">
                  Encrypts all communications between users and the server.
                </p>
              </div>
              <div className="control-action">
                <div className={`toggle-switch ${encryptionStatus ? 'toggle-switch--active' : ''}`} onClick={handleEncryptionToggle}>
                  <div className="toggle-switch__slider"></div>
                </div>
              </div>
            </div>
            
            <div className="control-item">
              <div className="control-info">
                <h4 className="control-name">System Backup</h4>
                <p className="control-description">
                  Create a full backup of the system database and configuration.
                </p>
              </div>
              <div className="control-action">
                <Button
                  variant="primary"
                  onClick={handleBackup}
                  disabled={backupInProgress}
                  isLoading={backupInProgress}
                  leftIcon={
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M19 9l-7 7-7-7"></path>
                    </svg>
                  }
                >
                  {backupInProgress ? 'Backing Up...' : 'Start Backup'}
                </Button>
              </div>
            </div>
            
            <div className="control-item">
              <div className="control-info">
                <h4 className="control-name">Restart Server</h4>
                <p className="control-description">
                  Restart the server and terminate all active connections.
                </p>
              </div>
              <div className="control-action">
                <Button
                  variant="danger"
                  onClick={handleResetServer}
                  disabled={loading}
                  leftIcon={
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M23 4v6h-6"></path>
                      <path d="M1 20v-6h6"></path>
                      <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                    </svg>
                  }
                >
                  Restart Server
                </Button>
              </div>
            </div>
          </div>
        </DashboardCard>
        
        <DashboardCard title="Security Audit" className="security-card">
          <p className="security-text">Run a comprehensive security audit to identify potential vulnerabilities in the system.</p>
          
          <div className="security-actions">
            <Button
              variant="primary"
              leftIcon={
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              }
            >
              Run Security Audit
            </Button>
            
            <Button
              variant="secondary"
              leftIcon={
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              }
            >
              View Audit Logs
            </Button>
          </div>
        </DashboardCard>
      </div>
      
      <style jsx>{`
        .page-header {
          margin-bottom: var(--spacing-6);
        }
        
        .page-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--color-text);
          margin: 0;
        }
        
        .alert {
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
          padding: var(--spacing-4) var(--spacing-5);
          border-radius: var(--radius);
          margin-bottom: var(--spacing-6);
        }
        
        .alert-error {
          background: rgba(220, 38, 38, 0.1);
          border: 1px solid rgba(220, 38, 38, 0.2);
          color: var(--color-error);
        }
        
        .alert-close {
          margin-left: auto;
          background: none;
          border: none;
          color: inherit;
          font-size: 1.25rem;
          cursor: pointer;
          padding: 0;
        }
        
        .stat-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
          gap: var(--spacing-5);
          margin-bottom: var(--spacing-6);
        }
        
        .card-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: var(--spacing-6);
        }
        
        .status-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--spacing-4);
        }
        
        .status-item {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-2);
        }
        
        .status-label {
          font-size: 0.875rem;
          color: var(--color-muted);
        }
        
        .status-value {
          font-size: 1.125rem;
          font-weight: 500;
        }
        
        .controls-list {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-5);
        }
        
        .control-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: var(--spacing-4);
        }
        
        .control-info {
          flex: 1;
        }
        
        .control-name {
          margin: 0 0 var(--spacing-1);
          font-size: 1rem;
          font-weight: 500;
        }
        
        .control-description {
          margin: 0;
          font-size: 0.875rem;
          color: var(--color-muted);
        }
        
        .toggle-switch {
          position: relative;
          width: 48px;
          height: 24px;
          border-radius: 12px;
          background-color: var(--color-muted);
          cursor: pointer;
          transition: background-color var(--transition);
        }
        
        .toggle-switch--active {
          background-color: var(--color-accent);
        }
        
        .toggle-switch__slider {
          position: absolute;
          top: 2px;
          left: 2px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background-color: white;
          transition: transform var(--transition);
        }
        
        .toggle-switch--active .toggle-switch__slider {
          transform: translateX(24px);
        }
        
        .security-text {
          margin-bottom: var(--spacing-5);
        }
        
        .security-actions {
          display: flex;
          gap: var(--spacing-4);
        }
        
        .loading-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 60vh;
          gap: var(--spacing-4);
        }
        
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid rgba(37, 99, 235, 0.1);
          border-top-color: var(--color-accent);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
          .card-grid {
            grid-template-columns: 1fr;
          }
          
          .status-grid {
            grid-template-columns: 1fr;
          }
          
          .control-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-3);
          }
          
          .security-actions {
            flex-direction: column;
            width: 100%;
          }
          
          .security-actions :global(button) {
            width: 100%;
          }
        }
      `}</style>
    </AdminLayout>
  );
}
