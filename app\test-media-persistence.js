/**
 * Media Persistence Test Script
 * Run this to test persistent media storage in Expo Go vs Standalone build
 */

import React from 'react';
import { AppRegistry } from 'react-native';
import MediaPersistenceTestScreen from './src/screens/MediaPersistenceTestScreen';

// Register the app
AppRegistry.registerComponent('MediaPersistenceTest', () => MediaPersistenceTestScreen);

// Start the test screen
console.log('Starting Media Persistence Test...');
