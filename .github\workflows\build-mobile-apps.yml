name: Build Mobile Apps (iOS & Android)

on:
  workflow_dispatch:
    inputs:
      backend_url:
        description: 'Backend URL (e.g., https://your-ngrok-url.ngrok.io)'
        required: true
        default: 'https://example.ngrok.io'
      frontend_url:
        description: 'Frontend URL (e.g., https://your-frontend-url.ngrok.io)'
        required: false
        default: 'https://example.ngrok.io'
      build_profile:
        description: 'EAS Build Profile'
        required: true
        default: 'github-ci'
        type: choice
        options:
          - github-ci
          - preview
          - production
      platforms:
        description: 'Platforms to build'
        required: true
        default: 'both'
        type: choice
        options:
          - both
          - ios
          - android

jobs:
  build-ios:
    if: ${{ github.event.inputs.platforms == 'both' || github.event.inputs.platforms == 'ios' }}
    runs-on: macos-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Setup Expo CLI
        run: npm install -g @expo/cli@latest eas-cli@latest

      - name: Install dependencies
        working-directory: ./app
        run: npm ci

      - name: Create .env file with build URLs
        working-directory: ./app
        run: |
          echo "EXPO_PUBLIC_BACKEND_URL=${{ github.event.inputs.backend_url }}" > .env
          echo "EXPO_PUBLIC_FRONTEND_URL=${{ github.event.inputs.frontend_url || github.event.inputs.backend_url }}" >> .env
          echo "EXPO_PUBLIC_BUILD_ENV=github-ci" >> .env
          echo "EXPO_PUBLIC_APP_VERSION=1.0.${{ github.run_number }}" >> .env
          echo "EXPO_PUBLIC_DEBUG_MODE=false" >> .env
          echo "EXPO_PUBLIC_LOG_NETWORK=false" >> .env

      - name: Authenticate with Expo
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        run: expo whoami

      - name: Build iOS app
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        run: eas build --platform ios --non-interactive --profile ${{ github.event.inputs.build_profile }} --wait

  build-android:
    if: ${{ github.event.inputs.platforms == 'both' || github.event.inputs.platforms == 'android' }}
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Setup Expo CLI
        run: npm install -g @expo/cli@latest eas-cli@latest

      - name: Install dependencies
        working-directory: ./app
        run: npm ci

      - name: Create .env file with build URLs
        working-directory: ./app
        run: |
          echo "EXPO_PUBLIC_BACKEND_URL=${{ github.event.inputs.backend_url }}" > .env
          echo "EXPO_PUBLIC_FRONTEND_URL=${{ github.event.inputs.frontend_url || github.event.inputs.backend_url }}" >> .env
          echo "EXPO_PUBLIC_BUILD_ENV=github-ci" >> .env
          echo "EXPO_PUBLIC_APP_VERSION=1.0.${{ github.run_number }}" >> .env
          echo "EXPO_PUBLIC_DEBUG_MODE=false" >> .env
          echo "EXPO_PUBLIC_LOG_NETWORK=false" >> .env

      - name: Authenticate with Expo
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        run: expo whoami

      - name: Build Android app
        working-directory: ./app
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        run: eas build --platform android --non-interactive --profile ${{ github.event.inputs.build_profile }} --wait
