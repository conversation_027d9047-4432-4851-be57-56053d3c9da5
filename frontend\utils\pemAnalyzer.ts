/**
 * Utilities for analyzing and working with PEM key formats
 */

/**
 * Analyze a PEM key content to determine its type and encryption status
 * @param pemContent The PEM key content as a string
 * @returns Analysis results including key type and encryption status
 */
export function analyzePEMKey(pemContent: string): {
  isValid: boolean;
  isEncrypted: boolean;
  keyType: string;
  message: string;
} {
  // Default result
  const result = {
    isValid: false,
    isEncrypted: false,
    keyType: 'unknown',
    message: 'Unknown key format'
  };

  // Check if this is a valid PEM format (has BEGIN and END markers)
  if (!pemContent.includes('-----BEGIN') || !pemContent.includes('-----END')) {
    result.message = 'Not a valid PEM format key file';
    return result;
  }

  // Determine key type based on header
  if (pemContent.includes('-----BEGIN ENCRYPTED PRIVATE KEY-----')) {
    result.isValid = true;
    result.isEncrypted = true;
    result.keyType = 'encrypted-pkcs8';
    result.message = 'Encrypted PKCS#8 format private key';
  } else if (pemContent.includes('-----<PERSON><PERSON><PERSON> PRIVATE KEY-----')) {
    result.isValid = true;
    result.isEncrypted = false;
    result.keyType = 'pkcs8';
    result.message = 'Unencrypted PKCS#8 format private key';
  } else if (pemContent.includes('-----BEGIN RSA PRIVATE KEY-----')) {
    result.isValid = true;
    result.isEncrypted = false;
    result.keyType = 'pkcs1';
    result.message = 'Unencrypted PKCS#1 format RSA private key';
  } else if (pemContent.includes('-----BEGIN PUBLIC KEY-----')) {
    result.isValid = true;
    result.isEncrypted = false;
    result.keyType = 'public';
    result.message = 'Public key in SPKI format';
  } else if (pemContent.includes('-----BEGIN RSA PUBLIC KEY-----')) {
    result.isValid = true;
    result.isEncrypted = false;
    result.keyType = 'rsa-public';
    result.message = 'RSA public key';
  } else if (pemContent.includes('-----BEGIN OPENSSH PRIVATE KEY-----')) {
    result.isValid = true;
    result.isEncrypted = pemContent.includes('Proc-Type: 4,ENCRYPTED');
    result.keyType = 'openssh';
    result.message = result.isEncrypted 
      ? 'OpenSSH encrypted private key (not supported)' 
      : 'OpenSSH private key (may not be compatible)';
  } else {
    result.message = 'Unsupported PEM key format';
  }

  return result;
}

/**
 * Generates instructions for converting an encrypted PEM key to unencrypted format
 * @returns Instructions as a string
 */
export function getKeyConversionInstructions(): string {
  return `
To convert an encrypted PEM key to an unencrypted format:

Using OpenSSL (recommended):
1. Open a terminal or command prompt
2. Run: openssl rsa -in encrypted_key.pem -out unencrypted_key.pem
3. Enter the passphrase when prompted
4. Upload the resulting 'unencrypted_key.pem' file

Using PuTTYgen (for Windows users):
1. Open PuTTYgen
2. Click Load and select your encrypted key
3. Enter the passphrase
4. Go to Conversions > Export OpenSSH key (NOT 'SSH-2 Private Key')
5. Save the unencrypted key file
6. Upload the exported key file

Note: Keep unencrypted key files secure and consider removing them when no longer needed.
  `;
}
