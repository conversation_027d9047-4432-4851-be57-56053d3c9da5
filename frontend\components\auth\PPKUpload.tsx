import React, { useState } from 'react';
import PPKFileUploader from './PPKFileUploader';
import { signChallenge } from '../../utils/ppkAuth';

interface PPKUploadProps {
  challenge: string;
  onPPKReady: (challenge: string, signature: string, privateKey: CryptoKey) => void;
  onPPKError: (error: string) => void;
  disabled?: boolean;
  className?: string;
}

const PPKUpload: React.FC<PPKUploadProps> = ({ 
  challenge, 
  onPPKReady, 
  onPPKError, 
  disabled = false,
  className = ''
}) => {
  const [privateKey, setPrivateKey] = useState<CryptoKey | null>(null);

  const handleKeyLoaded = async (key: CryptoKey) => {
    setPrivateKey(key);
    
    if (challenge) {
      try {
        const signature = await signChallenge(challenge, key);
        onPPKReady(challenge, signature, key);
      } catch (error) {
        onPPKError(error instanceof Error ? error.message : 'Failed to sign challenge');
      }
    }
  };

  const handleError = (error: string) => {
    onPPKError(error);
  };

  return (
    <div className={className}>
      <PPKFileUploader
        onKeyLoaded={handleKeyLoaded}
        onError={handleError}
        challenge={challenge}
      />
    </div>
  );
};

export default PPKUpload;
