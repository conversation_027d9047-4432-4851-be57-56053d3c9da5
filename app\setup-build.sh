#!/bin/bash

# CCALC App Build Setup Script
# This script helps you configure the environment for building the app

set -e

echo "🚀 CCALC App Build Setup"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local result
    
    echo -ne "${BLUE}$prompt${NC} [${YELLOW}$default${NC}]: "
    read result
    echo "${result:-$default}"
}

# Check if we're in the app directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: Please run this script from the app directory${NC}"
    echo "cd app && ./setup-build.sh"
    exit 1
fi

echo "📋 This script will help you configure the build environment."
echo ""

# Get URLs from user
BACKEND_URL=$(prompt_with_default "Enter your backend ngrok URL" "https://your-backend.ngrok.io")
FRONTEND_URL=$(prompt_with_default "Enter your frontend ngrok URL" "$BACKEND_URL")
BUILD_ENV=$(prompt_with_default "Enter build environment" "development")
APP_VERSION=$(prompt_with_default "Enter app version" "1.0.0")

# Validate URLs
if [[ ! $BACKEND_URL =~ ^https?:// ]]; then
    echo -e "${YELLOW}⚠️  Warning: Backend URL should start with http:// or https://${NC}"
fi

if [[ ! $FRONTEND_URL =~ ^https?:// ]]; then
    echo -e "${YELLOW}⚠️  Warning: Frontend URL should start with http:// or https://${NC}"
fi

# Create .env file
echo ""
echo "📝 Creating .env file..."

cat > .env << EOF
# Environment Configuration for CCALC App
# Generated on $(date)

# Backend URL - Your ngrok backend URL
EXPO_PUBLIC_BACKEND_URL=$BACKEND_URL

# Frontend URL - Your ngrok frontend URL  
EXPO_PUBLIC_FRONTEND_URL=$FRONTEND_URL

# Build Environment
EXPO_PUBLIC_BUILD_ENV=$BUILD_ENV

# App Version
EXPO_PUBLIC_APP_VERSION=$APP_VERSION

# Debug flags
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_NETWORK=true
EOF

echo -e "${GREEN}✅ Created .env file with your configuration${NC}"
echo ""

# Show the created configuration
echo "📄 Configuration created:"
echo "------------------------"
cat .env
echo ""

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# Check if EAS is configured
if [ ! -f "eas.json" ]; then
    echo -e "${YELLOW}⚠️  EAS configuration not found${NC}"
    echo "Run 'eas build:configure' to set up EAS builds"
else
    echo -e "${GREEN}✅ EAS configuration found${NC}"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Make sure your backend is running at: $BACKEND_URL"
echo "2. Test the app locally: npm run start"
echo "3. Build for iOS: npm run build:ios"
echo "4. Build for Android: npm run build:android"
echo ""
echo "For GitHub Actions builds:"
echo "1. Add EXPO_TOKEN to your repository secrets"
echo "2. Use the 'Build Mobile Apps' workflow"
echo "3. Enter your ngrok URLs when prompted"
echo ""
echo -e "${GREEN}Happy building! 🚀${NC}"
