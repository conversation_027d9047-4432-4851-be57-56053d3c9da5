import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';
import axios from 'axios';
import apiClient, { apiEndpoints } from '../../../utils/apiClient';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get admin token from cookies (this is an admin-only operation)
    const cookies = parse(req.headers.cookie || '');
    const token = cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }    if (req.method === 'GET') {
      // List users
      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.users.list}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        return res.status(200).json(response.data);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          return res.status(error.response.status).json(error.response.data);
        }
        throw error;
      }
    }    else if (req.method === 'POST') {
      // Create new user
      const { username, expression, displayName, avatarUrl, deviceFingerprint, bleUUID, isSuperuser } = req.body;

      if (!username || !expression || !displayName || !deviceFingerprint || !bleUUID) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      try {
        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.users.list}`,
          {
            username,
            expression,
            displayName,
            avatarUrl,
            deviceFingerprint,
            bleUUID,
            isSuperuser
          },
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        return res.status(200).json(response.data);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          return res.status(error.response.status).json(error.response.data);
        }
        throw error;
      }
    } 
    else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Users API error:', error);
    return res.status(500).json({ error: 'Failed to process request' });
  }
}
