import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { chatColors, chatTypography, chatSpacing, chatRadius } from '../styles/ChatStyles';

interface ErrorBannerProps {
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
}

export const ErrorBanner: React.FC<ErrorBannerProps> = ({ message, onRetry, onDismiss }) => (
  <View style={styles.container}>
    <Text style={styles.text}>{message}</Text>
    {onRetry && (
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryText}>Retry</Text>
      </TouchableOpacity>
    )}
    <TouchableOpacity style={styles.closeButton} onPress={onDismiss}>
      <Text style={styles.closeText}>✕</Text>
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: chatColors.danger,
    padding: chatSpacing.md,
    borderRadius: chatRadius.medium,
    margin: chatSpacing.md,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  text: {
    flex: 1,
    color: '#fff',
    fontSize: chatTypography.body,
    fontWeight: chatTypography.medium,
  },
  retryButton: {
    marginLeft: chatSpacing.md,
    paddingHorizontal: chatSpacing.sm,
    paddingVertical: chatSpacing.xs,
    backgroundColor: '#fff',
    borderRadius: chatRadius.small,
  },
  retryText: {
    color: chatColors.danger,
    fontWeight: chatTypography.semibold,
    fontSize: chatTypography.caption,
  },
  closeButton: {
    marginLeft: chatSpacing.sm,
    padding: chatSpacing.xs,
  },
  closeText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
