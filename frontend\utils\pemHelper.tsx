/**
 /**
 * Helper functions for PEM key management in the frontend
 */
import React from 'react';

/**
 * Get readable instructions for PEM key file preparation
 * @returns HTML-formatted instructions
 */
export function getPemKeyInstructions(): JSX.Element {
  return (
    <>
      <h3>PEM Key File Instructions:</h3>
      <p>
        For authentication, you need a valid PEM-formatted RSA private key file with one of these formats:
      </p>
      <ul>
        <li>Unencrypted PKCS#8 format (begins with <code>-----BEGIN PRIVATE KEY-----</code>)</li>
        <li>Unencrypted PKCS#1 format (begins with <code>-----BEGIN RSA PRIVATE KEY-----</code>)</li>
      </ul>
      
      <h4>Converting from Other Formats:</h4>
      <p>If you have a key in another format, you can convert it using these tools:</p>
      <ul>
        <li>
          <strong>PuTTY PPK format:</strong> Open PuTTYgen, load your key, 
          then go to Conversions &gt; Export OpenSSH key
        </li>
        <li>
          <strong>OpenSSH format:</strong> Use command: 
          <code>ssh-keygen -p -m PEM -f your_key_file</code>
        </li>
      </ul>
    </>
  );
}

/**
 * Analyze a key file and return information about it
 * @param filename The name of the key file
 * @returns Information about the key file
 */
export function analyzeKeyFilename(filename: string): { 
  valid: boolean;
  format: string;
  message: string;
} {
  if (!filename) {
    return { 
      valid: false,
      format: 'unknown',
      message: 'No file selected'
    };
  }
  
  const extension = filename.split('.').pop()?.toLowerCase();
  
  switch(extension) {
    case 'pem':
      return {
        valid: true,
        format: 'pem',
        message: 'PEM format (supported)'
      };
    case 'key':
      return {
        valid: true,
        format: 'key',
        message: 'KEY format (likely PEM-compatible)'
      };
    case 'ppk':
      return {
        valid: false,
        format: 'ppk',
        message: 'PuTTY PPK format (needs conversion to PEM)'
      };
    default:
      return {
        valid: false,
        format: 'unknown',
        message: `Unknown format (.${extension}), please use .pem or .key files`
      };
  }
}
