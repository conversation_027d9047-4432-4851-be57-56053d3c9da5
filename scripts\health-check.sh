# Check the status of all CCALC services
# This script provides a quick health check of all services in the stack

# ANSI color codes for better readability
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}===== CCALC System Health Check =====${NC}"
echo ""

# Check if docker and docker-compose are available
if ! command -v docker &> /dev/null || ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Error: Docker and/or docker-compose are not installed${NC}"
    exit 1
fi

# Check container status
echo -e "${YELLOW}Container Status:${NC}"
docker-compose ps | sed '1!G;h;$!d' | sed '1!G;h;$!d'

# Check backend logs
echo ""
echo -e "${YELLOW}Backend Logs (last 5 lines):${NC}"
docker-compose logs --tail=5 backend

# Check MongoDB connection
echo ""
echo -e "${YELLOW}MongoDB Connection Test:${NC}"
if docker-compose exec -T mongo mongosh --quiet --username root --password example --eval "db.adminCommand('ping')" | grep -q '"ok" : 1'; then
    echo -e "${GREEN}MongoDB connection successful${NC}"
else
    echo -e "${RED}MongoDB connection failed${NC}"
fi

# Check backend API health
echo ""
echo -e "${YELLOW}Backend API Health Check:${NC}"
if curl -s http://localhost:3000/health | grep -q "healthy"; then
    echo -e "${GREEN}Backend API is healthy${NC}"
else
    echo -e "${RED}Backend API is not responding or unhealthy${NC}"
fi

# Check frontend
echo ""
echo -e "${YELLOW}Frontend Health Check:${NC}"
if curl -s -I http://localhost | grep -q "200 OK"; then
    echo -e "${GREEN}Frontend is accessible${NC}"
else
    echo -e "${RED}Frontend is not accessible${NC}"
fi

echo ""
echo -e "${YELLOW}===== Health Check Complete =====${NC}"
