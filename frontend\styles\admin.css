/* ========================================
   MODERN ADMIN DASHBOARD DESIGN SYSTEM
   Professional, Clean, Responsive UI/UX
======================================== */

/* CSS Custom Properties (8pt Design System) */
:root {
  /* Colors - Professional & Accessible */
  --color-primary: #2563eb;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #2563eb;
  --color-primary-600: #1d4ed8;
  --color-primary-700: #1e40af;
  --color-primary-900: #1e3a8a;
  
  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;
  
  --color-success: #16a34a;
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-warning: #f59e0b;
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-error: #dc2626;
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-info: #0ea5e9;
  --color-info-50: #f0f9ff;
  --color-info-100: #e0f2fe;
  
  /* 8pt Spacing System */
  --space-1: 0.125rem;  /* 2px */
  --space-2: 0.25rem;   /* 4px */
  --space-3: 0.375rem;  /* 6px */
  --space-4: 0.5rem;    /* 8px */
  --space-6: 0.75rem;   /* 12px */
  --space-8: 1rem;      /* 16px */
  --space-10: 1.25rem;  /* 20px */
  --space-12: 1.5rem;   /* 24px */
  --space-16: 2rem;     /* 32px */
  --space-20: 2.5rem;   /* 40px */
  --space-24: 3rem;     /* 48px */
  --space-32: 4rem;     /* 64px */
  --space-40: 5rem;     /* 80px */
  --space-48: 6rem;     /* 96px */
  --space-64: 8rem;     /* 128px */
  
  /* Typography - Inter Font System */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  
  /* Shadows - Depth System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
  
  /* Layout Dimensions */
  --sidebar-width: 16rem;           /* 256px */
  --sidebar-width-collapsed: 4rem;  /* 64px */
  --topbar-height: 4rem;            /* 64px */
  --container-max-width: 80rem;     /* 1280px */
  
  /* Z-index Scale */
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
}

/* ========================================
   GLOBAL BASE STYLES
======================================== */

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  font-size: 16px;
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
}

/* ========================================
   LAYOUT COMPONENTS
======================================== */

.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--color-gray-50);
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  background-color: white;
  border-right: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-fixed);
  transition: all var(--duration-normal) var(--ease-in-out);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: var(--space-16) var(--space-16) var(--space-12);
  border-bottom: 1px solid var(--color-gray-100);
  flex-shrink: 0;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  color: var(--color-gray-900);
  text-decoration: none;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
  transition: color var(--duration-fast) var(--ease-out);
}

.sidebar-logo:hover {
  color: var(--color-primary-600);
}

.sidebar-logo-icon {
  width: var(--space-32);
  height: var(--space-32);
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
}

.sidebar-nav {
  flex: 1;
  padding: var(--space-8) 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--space-24);
}

.nav-section-title {
  padding: 0 var(--space-16) var(--space-8);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-8) var(--space-16);
  margin: 0 var(--space-8);
  color: var(--color-gray-700);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-out);
  position: relative;
}

.nav-link:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

.nav-link.active {
  background-color: var(--color-primary-50);
  color: var(--color-primary-700);
  font-weight: var(--font-weight-semibold);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: var(--space-16);
  background-color: var(--color-primary-600);
  border-radius: 0 var(--radius-base) var(--radius-base) 0;
}

.nav-icon {
  width: var(--space-16);
  height: var(--space-16);
  flex-shrink: 0;
}

.sidebar-footer {
  padding: var(--space-16);
  border-top: 1px solid var(--color-gray-100);
  flex-shrink: 0;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-8);
  border-radius: var(--radius-lg);
  transition: background-color var(--duration-fast) var(--ease-out);
}

.user-profile:hover {
  background-color: var(--color-gray-50);
}

.user-avatar {
  width: var(--space-32);
  height: var(--space-32);
  border-radius: var(--radius-full);
  background-color: var(--color-primary-500);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  margin: 0;
}

/* Main Content Area */
.main-wrapper {
  flex: 1;
  margin-left: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left var(--duration-normal) var(--ease-in-out);
}

/* Top Navigation */
.topbar {
  height: var(--topbar-height);
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-24);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-xs);
}

.topbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.menu-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: var(--space-32);
  height: var(--space-32);
  border: none;
  background: none;
  color: var(--color-gray-600);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
}

.menu-toggle:hover {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.page-subtitle {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  margin: var(--space-2) 0 0;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--space-24);
  max-width: var(--container-max-width);
  margin: 0 auto;
  width: 100%;
}

/* ========================================
   COMPONENT SYSTEM
======================================== */

/* Cards */
.card {
  background-color: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-fast) var(--ease-out);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--space-16) var(--space-20) var(--space-12);
  border-bottom: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-25);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
}

.card-description {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  margin: var(--space-2) 0 0;
}

.card-body {
  padding: var(--space-20);
}

.card-footer {
  padding: var(--space-12) var(--space-20) var(--space-16);
  border-top: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-25);
}

/* Stat Cards */
.stat-card {
  background-color: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--space-20);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-fast) var(--ease-out);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-12);
}

.stat-card-title {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  margin: 0;
}

.stat-card-icon {
  width: var(--space-32);
  height: var(--space-32);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-card-icon--primary { background-color: var(--color-primary-500); }
.stat-card-icon--success { background-color: var(--color-success); }
.stat-card-icon--warning { background-color: var(--color-warning); }
.stat-card-icon--error { background-color: var(--color-error); }
.stat-card-icon--info { background-color: var(--color-info); }

.stat-card-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-4);
}

.stat-card-change {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.stat-card-change--positive { color: var(--color-success); }
.stat-card-change--negative { color: var(--color-error); }
.stat-card-change--neutral { color: var(--color-gray-500); }

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-6);
  padding: var(--space-8) var(--space-16);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn--primary {
  background-color: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn--primary:hover:not(:disabled) {
  background-color: var(--color-primary-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn--secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
  border-color: var(--color-gray-300);
}

.btn--secondary:hover:not(:disabled) {
  background-color: var(--color-gray-200);
  border-color: var(--color-gray-400);
}

.btn--outline {
  background-color: transparent;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn--outline:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  color: var(--color-gray-900);
}

.btn--ghost {
  background-color: transparent;
  color: var(--color-gray-600);
}

.btn--ghost:hover:not(:disabled) {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

.btn--danger {
  background-color: var(--color-error);
  color: white;
}

.btn--danger:hover:not(:disabled) {
  background-color: #b91c1c;
  transform: translateY(-1px);
}

.btn--sm {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-xs);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--text-base);
}

.btn--icon {
  padding: var(--space-8);
  width: var(--space-32);
  height: var(--space-32);
}

/* Alerts */
.alert {
  display: flex;
  align-items: flex-start;
  gap: var(--space-8);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-lg);
  border: 1px solid;
  font-size: var(--text-sm);
  line-height: var(--line-height-snug);
}

.alert-icon {
  flex-shrink: 0;
  width: var(--space-16);
  height: var(--space-16);
  margin-top: var(--space-1);
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--space-2);
}

.alert-description {
  margin: 0;
  opacity: 0.9;
}

.alert-close {
  background: none;
  border: none;
  padding: var(--space-2);
  margin: -var(--space-2) -var(--space-2) 0 0;
  border-radius: var(--radius-base);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.alert-close:hover {
  opacity: 1;
}

.alert--success {
  background-color: var(--color-success-50);
  border-color: var(--color-success-200);
  color: var(--color-success-800);
}

.alert--warning {
  background-color: var(--color-warning-50);
  border-color: var(--color-warning-200);
  color: var(--color-warning-800);
}

.alert--error {
  background-color: var(--color-error-50);
  border-color: var(--color-error-200);
  color: var(--color-error-800);
}

.alert--info {
  background-color: var(--color-info-50);
  border-color: var(--color-info-200);
  color: var(--color-info-800);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-16);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  margin-bottom: var(--space-4);
}

.form-input {
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--text-sm);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
  background-color: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--ease-out);
}

.form-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px var(--color-primary-100);
}

.form-input:disabled {
  background-color: var(--color-gray-50);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.form-input--error {
  border-color: var(--color-error);
}

.form-input--error:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px var(--color-error-100);
}

/* ========================================
   LOADING COMPONENTS & SKELETONS
======================================== */

/* Loading Container */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-24);
}

.loading-container--full-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--color-white);
  z-index: 9999;
}

.loading-container--overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  z-index: 100;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-12);
}

/* Loading Spinner */
.loading-spinner {
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner--xs {
  width: 16px;
  height: 16px;
  border-width: 1.5px;
}

.loading-spinner--sm {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner--md {
  width: 32px;
  height: 32px;
  border-width: 2px;
}

.loading-spinner--lg {
  width: 48px;
  height: 48px;
  border-width: 3px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  font-weight: 500;
}

/* Inline Loading */
.inline-loading {
  display: inline-flex;
  align-items: center;
  gap: var(--space-6);
}

.inline-loading-text {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

/* Skeleton Components */
.skeleton-bar {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  border-radius: var(--radius-sm);
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-bar--xs {
  height: 12px;
  width: 60%;
}

.skeleton-bar--sm {
  height: 16px;
  width: 80%;
}

.skeleton-bar--md {
  height: 20px;
  width: 100%;
}

.skeleton-bar--lg {
  height: 24px;
  width: 120%;
}

.skeleton-circle {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  border-radius: 50%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-circle--sm {
  width: 32px;
  height: 32px;
}

.skeleton-circle--md {
  width: 40px;
  height: 40px;
}

.skeleton-circle--lg {
  width: 48px;
  height: 48px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Table Skeleton */
.table-skeleton {
  width: 100%;
}

.table-skeleton-header,
.table-skeleton-row {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr 3fr;
  gap: var(--space-12);
  padding: var(--space-12) 0;
  border-bottom: 1px solid var(--color-gray-100);
}

.table-skeleton-header {
  padding-top: 0;
}

.table-skeleton-row:last-child {
  border-bottom: none;
}

/* Card Skeleton */
.card-skeleton,
.stat-card-skeleton {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.card-skeleton .skeleton-bar:first-child {
  width: 80%;
}

.card-skeleton .skeleton-bar:nth-child(2) {
  width: 60%;
}

.card-skeleton .skeleton-bar:last-child {
  width: 40%;
}

/* Utility Classes */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
/* ========================================
   RESPONSIVE DESIGN
======================================== */

@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-wrapper {
    margin-left: 0;
  }
  
  .menu-toggle {
    display: flex;
  }
}

@media (max-width: 768px) {
  .topbar {
    padding: 0 var(--space-16);
  }
  
  .main-content {
    padding: var(--space-16);
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: var(--text-xl);
  }
}

@media (max-width: 640px) {
  .topbar-actions {
    gap: var(--space-4);
  }
  
  .stat-card {
    padding: var(--space-16);
  }
  
  .card-body {
    padding: var(--space-16);
  }
}

/* ========================================
   ADMIN LAYOUT COMPONENTS
======================================== */

/* Main Layout Container */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* Sidebar */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  background: var(--glass-bg-dark);
  backdrop-filter: blur(16px) saturate(1.2);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--glass-shadow);
  transform: translateX(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: var(--z-sidebar);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: var(--font-size-lg);
  transition: opacity 0.2s ease;
}

.logo:hover {
  opacity: 0.8;
}

.logo-text {
  font-weight: 700;
  letter-spacing: -0.025em;
}

.menu-close-btn {
  display: none;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: var(--spacing-lg) 0;
  overflow-y: auto;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  position: relative;
  margin: 0 var(--spacing-md);
  border-radius: var(--radius-lg);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(4px);
}

.nav-link.active {
  background: rgba(99, 102, 241, 0.2);
  color: #e0e7ff;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--color-primary-100);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile-condensed {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-radius: var(--radius-full);
  background: var(--color-primary-500);
  color: white;
  box-shadow: var(--shadow-md);
}

.avatar--sm {
  width: 32px;
  height: 32px;
  font-size: var(--font-size-sm);
}

.username {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* Main Content Area */
.main-wrapper {
  flex: 1;
  margin-left: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Top Bar */
.topbar {
  height: var(--topbar-height);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px) saturate(1.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  position: sticky;
  top: 0;
  z-index: var(--z-topbar);
}

.topbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.menu-toggle-btn {
  display: none;
}

.page-title-main {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.user-profile-topbar {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-gray-50);
  border-radius: var(--radius-full);
  border: 1px solid var(--color-gray-200);
}

.user-profile-topbar .username {
  color: var(--color-gray-700);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-xl);
  animation: fadeIn 0.3s ease;
}

.main-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

/* ========================================
   DROPDOWN MENUS & NOTIFICATIONS
======================================== */

/* Dropdown Base */
.dropdown-menu {
  position: absolute;
  top: calc(100% + var(--space-4));
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  min-width: 200px;
  opacity: 0;
  animation: dropdownSlideIn 0.15s ease forwards;
}

.dropdown-menu--right {
  right: 0;
}

.dropdown-menu--wide {
  min-width: 320px;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

.dropdown-header {
  padding: var(--space-12) var(--space-16) var(--space-8);
  border-bottom: 1px solid var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdown-header h6 {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-900);
}

.dropdown-footer {
  padding: var(--space-8) var(--space-16) var(--space-12);
  border-top: 1px solid var(--color-gray-100);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  width: 100%;
  padding: var(--space-8) var(--space-16);
  border: none;
  background: none;
  text-align: left;
  font-size: var(--text-sm);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  background-color: var(--color-gray-50);
  color: var(--color-gray-900);
}

.dropdown-item:active {
  background-color: var(--color-gray-100);
}

.dropdown-divider {
  height: 1px;
  background: var(--color-gray-100);
  margin: var(--space-4) 0;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--color-error);
  color: var(--color-white);
  font-size: 10px;
  font-weight: 600;
  padding: 2px 5px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* Notification List */
.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-8);
  padding: var(--space-12) var(--space-16);
  border-bottom: 1px solid var(--color-gray-50);
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item::before {
  content: '';
  position: absolute;
  left: var(--space-8);
  top: var(--space-16);
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.notification-item--info::before {
  background: var(--color-info);
}

.notification-item--success::before {
  background: var(--color-success);
}

.notification-item--warning::before {
  background: var(--color-warning);
}

.notification-item--error::before {
  background: var(--color-error);
}

.notification-content {
  flex: 1;
  padding-left: var(--space-16);
}

.notification-message {
  margin: 0 0 var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-gray-900);
  line-height: 1.4;
}

.notification-time {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
}

.notification-close {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.notification-close:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}

/* Badge Components */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-2) var(--space-6);
  font-size: var(--text-xs);
  font-weight: 600;
  border-radius: var(--radius-full);
  line-height: 1;
}

.badge--primary {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
}

.badge--success {
  background: var(--color-success-100);
  color: var(--color-success-700);
}

.badge--warning {
  background: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge--error {
  background: var(--color-error-100);
  color: var(--color-error-700);
}

/* Enhanced Button Variants */
.btn--sm {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-sm);
  min-height: 32px;
}

.btn--xs {
  padding: var(--space-2) var(--space-6);
  font-size: var(--text-xs);
  min-height: 24px;
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
  .dropdown-menu {
    left: var(--space-8);
    right: var(--space-8);
    min-width: auto;
  }
  
  .dropdown-menu--wide {
    min-width: auto;
  }
  
  .notification-item {
    padding: var(--space-8) var(--space-12);
  }
  
  .notification-content {
    padding-left: var(--space-12);
  }
}

/* ========================================
   MODAL COMPONENTS
======================================== */

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  opacity: 0;
  animation: backdropFadeIn 0.2s ease forwards;
}

@keyframes backdropFadeIn {
  to {
    opacity: 1;
  }
}

.modal {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-height: calc(100vh - var(--space-32));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.95) translateY(-20px);
  animation: modalSlideIn 0.2s ease forwards;
  border: 1px solid var(--color-gray-200);
}

@keyframes modalSlideIn {
  to {
    transform: scale(1) translateY(0);
  }
}

.modal--sm {
  width: 100%;
  max-width: 400px;
}

.modal--md {
  width: 100%;
  max-width: 500px;
}

.modal--lg {
  width: 100%;
  max-width: 700px;
}

.modal--xl {
  width: 100%;
  max-width: 900px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-20) var(--space-24) var(--space-16);
  border-bottom: 1px solid var(--color-gray-200);
  flex-shrink: 0;
}

.modal-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-gray-900);
}

.modal-close {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}

.modal-body {
  padding: var(--space-20) var(--space-24);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: var(--space-16) var(--space-24) var(--space-20);
  border-top: 1px solid var(--color-gray-200);
  flex-shrink: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: var(--space-8);
    align-items: flex-end;
  }
  
  .modal {
    max-height: 80vh;
    width: 100%;
    margin-bottom: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  
  .modal--sm,
  .modal--md,
  .modal--lg,
  .modal--xl {
    max-width: none;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: var(--space-16);
    padding-right: var(--space-16);
  }
}

/* ========================================
   TOAST NOTIFICATIONS
======================================== */

.toast-container {
  position: fixed;
  top: var(--space-16);
  right: var(--space-16);
  z-index: 3000;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  max-width: 400px;
  width: 100%;
}

.toast {
  background: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-gray-200);
  padding: var(--space-16);
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
  position: relative;
  transform: translateX(100%);
  animation: toastSlideIn 0.3s ease forwards;
}

@keyframes toastSlideIn {
  to {
    transform: translateX(0);
  }
}

.toast--success {
  border-left: 4px solid var(--color-success);
}

.toast--error {
  border-left: 4px solid var(--color-error);
}

.toast--warning {
  border-left: 4px solid var(--color-warning);
}

.toast--info {
  border-left: 4px solid var(--color-info);
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.toast--success .toast-icon {
  background: var(--color-success-100);
  color: var(--color-success);
}

.toast--error .toast-icon {
  background: var(--color-error-100);
  color: var(--color-error);
}

.toast--warning .toast-icon {
  background: var(--color-warning-100);
  color: var(--color-warning);
}

.toast--info .toast-icon {
  background: var(--color-info-100);
  color: var(--color-info);
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--space-2);
}

.toast-message {
  font-size: var(--text-sm);
  color: var(--color-gray-700);
  line-height: 1.4;
}

.toast-action {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  font-size: var(--text-sm);
  font-weight: 600;
  margin-top: var(--space-4);
  padding: 0;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.toast-action:hover {
  text-decoration: none;
}

.toast-close {
  background: none;
  border: none;
  color: var(--color-gray-400);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  flex-shrink: 0;
  position: absolute;
  top: var(--space-8);
  right: var(--space-8);
}

.toast-close:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .toast-container {
    left: var(--space-8);
    right: var(--space-8);
    top: var(--space-8);
    max-width: none;
  }
  
  .toast {
    padding: var(--space-12);
    gap: var(--space-8);
  }
}

/* ========================================
   ERROR BOUNDARY & FALLBACK COMPONENTS
======================================== */

.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--space-24);
}

.error-boundary-content {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-boundary-icon {
  color: var(--color-error);
  margin-bottom: var(--space-16);
}

.error-boundary-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-8);
}

.error-boundary-message {
  font-size: var(--text-base);
  color: var(--color-gray-600);
  line-height: 1.5;
  margin: 0 0 var(--space-20);
}

.error-boundary-details {
  text-align: left;
  margin: var(--space-16) 0;
  padding: var(--space-12);
  background: var(--color-gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-200);
}

.error-boundary-details summary {
  cursor: pointer;
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--space-8);
}

.error-boundary-stack {
  font-size: var(--text-xs);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: var(--color-gray-800);
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
}

.error-boundary-actions {
  display: flex;
  gap: var(--space-8);
  justify-content: center;
  flex-wrap: wrap;
}

/* Error Fallback */
.error-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  min-height: 200px;
}

.error-fallback-content {
  text-align: center;
  max-width: 400px;
}

.error-fallback-icon {
  color: var(--color-error);
  margin-bottom: var(--space-12);
}

.error-fallback-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 var(--space-6);
}

.error-fallback-message {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  margin: 0 0 var(--space-16);
  line-height: 1.4;
}

.error-fallback-button {
  margin-bottom: var(--space-12);
}

.error-fallback-details {
  text-align: left;
  margin-top: var(--space-12);
  padding: var(--space-8);
  background: var(--color-gray-50);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-gray-200);
}

.error-fallback-details summary {
  cursor: pointer;
  font-size: var(--text-xs);
  color: var(--color-gray-600);
  margin-bottom: var(--space-4);
}

.error-fallback-stack {
  font-size: var(--text-xs);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: var(--color-gray-700);
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
  max-height: 150px;
  overflow-y: auto;
}
