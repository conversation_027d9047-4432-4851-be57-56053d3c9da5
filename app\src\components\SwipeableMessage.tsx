/**
 * SwipeableMessage - iOS-native swipe-to-reply functionality
 * Optimized for iPhone with native iOS haptics and gestures
 */

import React, { ReactNode } from 'react';
import { View, Text, Animated } from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import { chatStyles } from '../styles/ChatStyles';

interface SwipeableMessageProps {
  children: ReactNode;
  onSwipeToReply: () => void;
  isUser: boolean;
}

export const SwipeableMessage: React.FC<SwipeableMessageProps> = ({
  children,
  onSwipeToReply,
  isUser,
}) => {
  const translateX = new Animated.Value(0);
  const opacity = new Animated.Value(0);

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    const { state, translationX } = event.nativeEvent;

    if (state === State.ACTIVE) {
      // Show reply icon when swiping
      const swipeDistance = Math.abs(translationX);
      const targetOpacity = Math.min(swipeDistance / 50, 1);
      
      Animated.timing(opacity, {
        toValue: targetOpacity,
        duration: 0,
        useNativeDriver: true,
      }).start();

      // iOS-native haptic feedback at threshold
      if (swipeDistance > 50 && swipeDistance < 60) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      
      // Strong haptic feedback when ready to trigger
      if (swipeDistance > 80 && swipeDistance < 90) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    }

    if (state === State.END) {
      const swipeDistance = Math.abs(translationX);
      
      // Trigger reply if swiped enough (iOS-native threshold)
      if (swipeDistance > 70) {
        onSwipeToReply();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      // Reset animation
      Animated.parallel([
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  return (
    <View style={chatStyles.swipeableContainer}>
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        activeOffsetX={isUser ? [-10, 0] : [0, 10]}
        failOffsetY={[-20, 20]}
      >
        <Animated.View style={{ transform: [{ translateX }] }}>
          <View>
            {children as any}
          </View>
        </Animated.View>
      </PanGestureHandler>
      
      {/* Reply icon */}
      <Animated.View 
        style={[
          chatStyles.replyIcon,
          isUser ? chatStyles.replyIconRight : chatStyles.replyIconLeft,
          { opacity }
        ]}
      >
        <Text style={chatStyles.replyIconText}>↩️</Text>
      </Animated.View>
    </View>
  );
};
