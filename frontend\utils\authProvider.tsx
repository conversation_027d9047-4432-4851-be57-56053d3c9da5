// AuthProvider Component - Enhanced with Cookie-based Security
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { importPrivate<PERSON>ey, signChallenge } from './ppkAuth';
import axios from 'axios';
import { setCsrfToken, clearCsrfToken } from './csrfProtection';
import { sessionManager } from './sessionManager';
import { tokenManager } from './tokenManager';

// Enhanced Context for authentication state with improved security
const AuthContext = createContext<{
  isAuthenticated: boolean;
  isAdmin: boolean;
  token: string | null;
  adminInfo: any | null;
  userInfo: any | null;
  csrfToken: string | null;
  // Unified login function for admin with enhanced security
  handleLogin: (username: string, password: string, ppkChallenge?: string, ppkSignature?: string, deviceFingerprint?: string) => Promise<any>;
  userLogin: (username: string, expression: string, deviceFingerprint: string, bleUUID: string) => Promise<any>;
  logout: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
  loading: boolean;
  error: string | null;
}>({
  isAuthenticated: false,
  isAdmin: false,
  token: null,
  adminInfo: null,
  userInfo: null,
  csrfToken: null,
  handleLogin: async () => ({}),
  userLogin: async () => ({}),
  logout: async () => {},
  checkAuthStatus: async () => {},
  loading: true,
  error: null,
});

interface AdminLoginParams {
  username: string;
  password: string;
  ppkSignature?: string; // Signature is now passed directly
  challenge?: string;    // Challenge used for the signature
  deviceFingerprint?: string;
}

interface UserLoginParams {
  username: string;
  expression: string;
  deviceFingerprint: string;
  bleUUID: string;
}

/**
 * Initialize admin login to get PPK challenge
 */
export async function initAdminLogin(username: string): Promise<{
  requiresPPK: boolean;
  challenge?: string;
  authMethod: string;
  message: string;
  csrfToken?: string;
}> {
  try {
    const response = await axios.post('/api/auth/admin/login/init', { username });
    return response.data;
  } catch (error) {
    console.error('Admin login init error:', error);
    throw error;
  }
}

/**
 * Complete admin login with password and optional PPK signature
 */
export async function completeAdminLogin({
  username,
  password,
  ppkSignature, // Expecting signature directly
  challenge,    // Expecting challenge used for signature
  deviceFingerprint = 'web',
}: AdminLoginParams): Promise<{
  token: string;
  csrfToken: string;
  admin: {
    id: string;
    username: string;
    email: string;
    role: string;
  };
}> {
  try {
    const response = await axios.post('/api/auth/admin/login/complete', {
      username,
      password,
      ppkSignature, // Pass the signature
      challenge,    // Pass the challenge
      deviceFingerprint,
    });

    if (response.data.csrfToken) {
      setCsrfToken(response.data.csrfToken);
    }
    return response.data;
  } catch (error) {
    console.error('Admin login complete error:', error);
    throw error;
  }
}

/**
 * Handle user login
 */
export async function userLogin({
  username,
  expression,
  deviceFingerprint,
  bleUUID,
}: UserLoginParams): Promise<{
  token: string;
  csrfToken: string;
  user: {
    id: string;
    username: string;
    displayName?: string;
  };
}> {
  try {
    const response = await axios.post('/api/auth/user/login', {
      username,
      expression,
      deviceFingerprint,
      bleUUID,
    });

    // Store CSRF token for subsequent requests
    if (response.data.csrfToken) {
      setCsrfToken(response.data.csrfToken);
    }

    return response.data;
  } catch (error) {
    console.error('User login error:', error);
    throw error;
  }
}

/**
 * Logout user or admin
 */
export async function logout(token: string): Promise<void> {
  try {
    await axios.post('/api/auth/logout', {}, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    // Clear CSRF token on logout
    clearCsrfToken();
    
  } catch (error) {
    console.error('Logout error:', error);
    // Still clear CSRF token even if logout failed
    clearCsrfToken();
  }
}

/**
 * Auth Provider component to wrap app with authentication context
 */
export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [adminInfo, setAdminInfo] = useState<any | null>(null);
  const [userInfo, setUserInfo] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [csrfToken, setCsrfTokenState] = useState<string | null>(null);

  // Enhanced authentication status check that supports both localStorage and cookies
  const checkAuthStatus = async () => {
    setLoading(true);
    try {
      // First try to check cookie-based session
      const sessionResponse = await fetch('/api/auth/session', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (sessionResponse.ok) {
        const sessionData = await sessionResponse.json();
        if (sessionData.authenticated) {
          setIsAuthenticated(true);
          setIsAdmin(sessionData.userType === 'admin');
          
          if (sessionData.userType === 'admin') {
            setAdminInfo(sessionData.user);
          } else {
            setUserInfo(sessionData.user);
          }
          
          if (sessionData.csrfToken) {
            setCsrfTokenState(sessionData.csrfToken);
            setCsrfToken(sessionData.csrfToken);
          }
          
          setLoading(false);
          return; // Cookie-based session is valid
        }
      }

      // Fallback to localStorage-based session (for backward compatibility)
      const storedToken = localStorage.getItem('authToken');
      const storedUserType = localStorage.getItem('userType');
      
      if (storedToken) {
        setToken(storedToken);
        setIsAuthenticated(true);
        
        if (storedUserType === 'admin') {
          setIsAdmin(true);
          try {
              const adminData = localStorage.getItem('adminInfo');
              if (adminData) setAdminInfo(JSON.parse(adminData));
          } catch (e) { 
            console.error("Failed to parse adminInfo from localStorage", e); 
            localStorage.removeItem('adminInfo');
          }
        } else {
          try {
              const userData = localStorage.getItem('userInfo');
              if (userData) setUserInfo(JSON.parse(userData));
          } catch (e) { 
            console.error("Failed to parse userInfo from localStorage", e); 
            localStorage.removeItem('userInfo');
          }
        }
        
        const storedCsrfToken = localStorage.getItem('csrfToken');
        if (storedCsrfToken) {
          setCsrfTokenState(storedCsrfToken);
          setCsrfToken(storedCsrfToken);
        }
      } else {
        // No valid session found
        setIsAuthenticated(false);
        setIsAdmin(false);
        setAdminInfo(null);
        setUserInfo(null);
        setCsrfTokenState(null);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setError('Failed to verify authentication status');
      setIsAuthenticated(false);
      setIsAdmin(false);
      setAdminInfo(null);
      setUserInfo(null);
      setCsrfTokenState(null);
    }
    setLoading(false);
  };

  // Initialize auth state from localStorage on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);
  // Enhanced Unified Admin Login Handler with cookie support
  const handleLogin = async (username: string, password: string, ppkChallenge?: string, ppkSignature?: string, deviceFingerprint: string = 'web') => {
    setLoading(true);
    setError(null);
    
    try {
      // Try enhanced cookie-based login first
      const response = await fetch('/api/auth/admin/login', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken && { 'X-CSRF-Token': csrfToken }),
        },
        body: JSON.stringify({
          username,
          password,
          ppkChallenge,
          ppkSignature,
          deviceFingerprint,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        
        // Cookie-based authentication successful
        setIsAuthenticated(true);
        setIsAdmin(true);
        setAdminInfo(data.admin || data.data?.admin);
        
        if (data.csrfToken || data.data?.csrfToken) {
          const newCsrfToken = data.csrfToken || data.data.csrfToken;
          setCsrfTokenState(newCsrfToken);
          setCsrfToken(newCsrfToken);
        }
        
        setLoading(false);
        return data;
      }

      // If cookie-based login fails, fallback to localStorage-based login
      console.log('Cookie-based login failed, trying localStorage-based login');
      
      const initData = await initAdminLogin(username);
      const { requiresPPK, challenge: serverChallenge, csrfToken: initCsrfToken } = initData;

      if (initCsrfToken) {
        setCsrfTokenState(initCsrfToken);
        setCsrfToken(initCsrfToken);
        localStorage.setItem('csrfToken', initCsrfToken);
      }

      let fallbackResponse;
      if (requiresPPK) {
        if (!ppkSignature || !ppkChallenge) {
          throw new Error('PPK signature and challenge are required for this admin account.');
        }
        fallbackResponse = await completeAdminLogin({
          username,
          password,
          ppkSignature,
          challenge: ppkChallenge,
          deviceFingerprint,
        });
      } else {
        fallbackResponse = await completeAdminLogin({ username, password, deviceFingerprint });
      }
      
      const { token: authToken, csrfToken: responseCsrfToken, admin } = fallbackResponse;
      
      // Store in localStorage for backward compatibility
      localStorage.setItem('authToken', authToken);
      localStorage.setItem('userType', 'admin');
      localStorage.setItem('adminInfo', JSON.stringify(admin));
      if (responseCsrfToken) {
        localStorage.setItem('csrfToken', responseCsrfToken);
        setCsrfTokenState(responseCsrfToken);
        setCsrfToken(responseCsrfToken);
      }
      
      setToken(authToken);
      setAdminInfo(admin);
      setIsAuthenticated(true);
      setIsAdmin(true);
      
      return fallbackResponse;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Authentication failed';
      setError(errorMessage);
      console.error('Enhanced login error:', err);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // User login
  const handleUserLogin = async (username: string, expression: string, deviceFingerprint: string, bleUUID: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userLogin({
        username,
        expression,
        deviceFingerprint,
        bleUUID
      });
      
      const { token: authToken, csrfToken, user } = response;
      
      localStorage.setItem('authToken', authToken);
      localStorage.setItem('userType', 'user');
      localStorage.setItem('userInfo', JSON.stringify(user));
      if (csrfToken) {
        localStorage.setItem('csrfToken', csrfToken);
        setCsrfToken(csrfToken);
      }
      
      setToken(authToken);
      setUserInfo(user);
      setIsAuthenticated(true);
      setIsAdmin(false); // Explicitly set isAdmin to false for user login
      
      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'User authentication failed';
      setError(errorMessage);
      console.error('User login error:', err);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  // Enhanced Logout with cookie support
  const handleLogout = async () => {
    setLoading(true);
    
    try {
      // Try cookie-based logout first
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken && { 'X-CSRF-Token': csrfToken }),
        },
      });
    } catch (error) {
      console.error('Cookie-based logout error:', error);
      
      // Fallback to token-based logout if available
      const currentToken = token;
      if (currentToken) {
        try {
          await logout(currentToken);
        } catch (tokenLogoutError) {
          console.error('Token-based logout error:', tokenLogoutError);
        }
      }
    } finally {
      // Clear all client-side state regardless of API call success
      localStorage.removeItem('authToken');
      localStorage.removeItem('userType');
      localStorage.removeItem('adminInfo');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('csrfToken');
      
      clearCsrfToken();
      
      setToken(null);
      setAdminInfo(null);
      setUserInfo(null);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setCsrfTokenState(null);
      setError(null);
      setLoading(false);
    }
  };
  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isAdmin,
        token,
        adminInfo,
        userInfo,
        csrfToken,
        handleLogin,
        userLogin: handleUserLogin,
        logout: handleLogout,
        checkAuthStatus,
        loading,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use the auth context
export function useAuth() {
  return useContext(AuthContext);
}

export default AuthContext;
