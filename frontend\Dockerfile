# Secure, production-ready Dockerfile for Next.js admin panel (frontend)
FROM node:20-alpine as builder

WORKDIR /usr/src/app

# Install dependencies
COPY package.json ./
RUN npm i

# Copy source code
COPY . .

# Build Next.js app
RUN npm run build

# Production image
FROM node:20-alpine as prod
WORKDIR /usr/src/app
COPY --from=builder /usr/src/app .
EXPOSE 3001
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
USER appuser
CMD ["npx", "next", "start", "-p", "3001"]
