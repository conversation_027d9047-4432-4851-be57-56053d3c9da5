#!/bin/bash

# iPhone Build Script for CCALC
# This script prepares and builds the app for iPhone testing

echo "🍎 Starting iPhone Build Process for CCALC..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Step 1: Installing CocoaPods dependencies...${NC}"
cd "$(dirname "$0")"
pod install --repo-update

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ CocoaPods installation completed successfully${NC}"
else
    echo -e "${RED}❌ CocoaPods installation failed${NC}"
    exit 1
fi

echo -e "${BLUE}Step 2: Opening Xcode workspace...${NC}"
open CCALC.xcworkspace

echo -e "${GREEN}📱 Ready for iPhone build!${NC}"
echo -e "${BLUE}Next steps in Xcode:${NC}"
echo "1. Select your iPhone from the device list"
echo "2. Set your development team in project settings"
echo "3. Update bundle identifier (com.yourname.ccalc)"
echo "4. Click the play button to build and run on your iPhone"
echo ""
echo -e "${BLUE}Backend is running at: http://localhost:3000${NC}"
echo -e "${BLUE}Make sure your iPhone is on the same network to connect to the backend${NC}"
