import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAdminAuth } from '../../contexts/AdminAuthContext';

/**
 * Higher Order Component for protected admin routes
 * Redirects to login page if user is not authenticated as admin
 */
const withAdminAuth = <P extends object>(Component: React.ComponentType<P>) => {
  const WithAdminAuth: React.FC<P> = (props) => {
    const { isAuthenticated, admin, isLoading } = useAdminAuth();
    const router = useRouter();

    useEffect(() => {
      // Only redirect if we're done loading and not authenticated
      if (!isLoading && !isAuthenticated && router.isReady) {
        console.log('Redirecting to login - not authenticated');
        router.replace('/admin/login');
      }
    }, [isAuthenticated, isLoading, router.isReady]);

    // Show loading indicator while checking authentication
    if (isLoading) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading admin panel...</p>
          </div>
        </div>
      );
    }

    // If not authenticated, return null (will redirect)
    if (!isAuthenticated) {
      return null;
    }

    // If authenticated as admin, render the component
    return <Component {...props} />;
  };

  // Set display name for debugging
  WithAdminAuth.displayName = `WithAdminAuth(${Component.displayName || Component.name || 'Component'})`;

  return WithAdminAuth;
};

export default withAdminAuth;
