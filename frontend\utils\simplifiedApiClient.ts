/**
 * Simplified Client-Side API Client
 * Uses the unified proxy for all backend communication
 */

interface ApiClientOptions {
  timeout?: number;
  baseUrl?: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any;
}

class ApiClient {
  private timeout: number;
  private baseUrl: string;

  constructor(options: ApiClientOptions = {}) {
    this.timeout = options.timeout || 10000;
    this.baseUrl = options.baseUrl || '';
  }

  private async makeRequest<T = any>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // Use the unified proxy
      const proxyUrl = `/api/proxy?path=${encodeURIComponent(endpoint)}`;
      
      const response = await fetch(proxyUrl, {
        ...options,
        signal: controller.signal,
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      let responseData;
      try {
        responseData = await response.json();
      } catch {
        responseData = { success: false, error: 'Invalid response format' };
      }

      if (!response.ok) {
        return {
          success: false,
          error: responseData.error || 'Request failed',
          message: responseData.message || `HTTP ${response.status}`,
          details: responseData.details || { statusCode: response.status }
        };
      }

      return {
        success: true,
        data: responseData,
        message: responseData.message
      };

    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        return {
          success: false,
          error: 'Request timeout',
          message: 'The request took too long to complete'
        };
      }

      return {
        success: false,
        error: 'Network error',
        message: error.message || 'Failed to connect to server'
      };
    }
  }

  async get<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T = any>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' });
  }

  async patch<T = any>(endpoint: string, data?: any, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// Export a default instance
export const apiClient = new ApiClient();

// Export the class for custom instances
export default ApiClient;
