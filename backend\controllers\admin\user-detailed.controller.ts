import { Request, Response } from 'express';
import User from '../../models/User';
import AuditLogModel from '../../models/AuditLog';

/**
 * Get detailed user information for admin panel
 * GET /api/admin/users/detailed
 */
export const getUsersDetailed = async (req: Request, res: Response): Promise<void> => {
  try {
    const users = await User.find({})
      .select('-expressionHash -deviceFingerprintHash -bleUUIDHash') // Exclude sensitive hashes
      .sort({ createdAt: -1 });

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-users-fetch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'users_fetch',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          userCount: users.length,
          requestedBy: req.admin?.username || 'Unknown',
          adminId: req.admin?.id
        }
      }
    });

    res.json({
      success: true,
      users: users.map(user => ({
        _id: user._id,
        username: user.username,
        email: user.email,
        isActive: user.status === 'active',
        status: user.status,
        createdAt: user.createdAt,
        lastLogin: user.lastLoginAt,
        devices: user.devices || [],
        loginHistory: user.loginHistory || [],
        chatHistory: user.chatHistory || [],
        voiceRecordings: user.voiceRecordings || [],
        securityEvents: user.securityEvents || [],
        mathExpression: {
          expression: user.unlockExpression || 'Hidden',
          type: user.expressionType,
          updatedAt: user.expressionUpdatedAt
        },
        profile: user.profile,
        isSuperuser: user.isSuperuser || false
      }))
    });

  } catch (error) {
    console.error('Error fetching detailed users:', error);

    // Create error audit log
    try {
      await AuditLogModel.create({
        logId: `admin-users-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        event: {
          type: 'admin_action',
          action: 'users_fetch',
          result: 'failure',
          severity: 'medium'
        },
        context: {
          userAgent: req.get('User-Agent') || 'unknown',
          ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
          endpoint: req.path,
          method: req.method
        },
        compliance: {
          category: 'system_admin',
          retention: 'long',
          piiIncluded: false,
          sensitiveData: false,
          exportable: true
        },
        data: {
          metadata: {
            error: error instanceof Error ? error.message : 'Unknown error',
            requestedBy: req.admin?.username || 'Unknown',
            adminId: req.admin?.id
          }
        }
      });
    } catch (auditError) {
      console.error('Failed to create audit log:', auditError);
    }

    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
};

/**
 * Get specific user details by ID
 * GET /api/admin/users/:id/detailed
 */
export const getUserDetailed = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.params.id;
    const user = await User.findById(userId)
      .select('-expressionHash -deviceFingerprintHash -bleUUIDHash');

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    // Create audit log
    await AuditLogModel.create({
      logId: `admin-user-view-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      event: {
        type: 'admin_action',
        action: 'user_view',
        result: 'success',
        severity: 'low'
      },
      context: {
        userAgent: req.get('User-Agent') || 'unknown',
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        endpoint: req.path,
        method: req.method
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: true,
        sensitiveData: true,
        exportable: false
      },
      data: {
        metadata: {
          userId: user._id,
          username: user.username,
          requestedBy: req.admin?.username || 'Unknown',
          adminId: req.admin?.id
        }
      }
    });

    res.json({
      success: true,
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        isActive: user.status === 'active',
        status: user.status,
        createdAt: user.createdAt,
        lastLogin: user.lastLoginAt,
        devices: user.devices || [],
        loginHistory: user.loginHistory || [],
        chatHistory: user.chatHistory || [],
        voiceRecordings: user.voiceRecordings || [],
        securityEvents: user.securityEvents || [],
        mathExpression: {
          expression: user.unlockExpression || 'Hidden',
          type: user.expressionType,
          updatedAt: user.expressionUpdatedAt
        },
        profile: user.profile,
        isSuperuser: user.isSuperuser || false
      }
    });

  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user details'
    });
  }
};
