/**
 * Session Warning Component
 * Shows session expiration warnings and handles session refresh
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  Alert
} from '@mui/material';
import { sessionManager, SessionStatus } from '../utils/sessionManager';

interface SessionWarningProps {
  warningThresholdMinutes?: number; // Show warning when X minutes left
}

const SessionWarning: React.FC<SessionWarningProps> = ({
  warningThresholdMinutes = 5
}) => {
  const [sessionStatus, setSessionStatus] = useState<SessionStatus | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [timeLeft, setTimeLeft] = useState<string>('');

  useEffect(() => {
    // Listen to session status changes
    const unsubscribe = sessionManager.addListener((status: SessionStatus) => {
      setSessionStatus(status);
      
      // Show warning if session is expiring soon
      const minutesLeft = status.timeRemaining / (1000 * 60);
      const shouldShowWarning = status.isActive && 
                               status.isWarning && 
                               minutesLeft <= warningThresholdMinutes;
      
      setShowWarning(shouldShowWarning);
    });

    // Cleanup listener on unmount
    return unsubscribe;
  }, [warningThresholdMinutes]);

  useEffect(() => {
    if (!showWarning || !sessionStatus) {
      return;
    }

    // Update time left display every second
    const updateTimer = setInterval(() => {
      const minutes = Math.floor(sessionStatus.timeRemaining / (1000 * 60));
      const seconds = Math.floor((sessionStatus.timeRemaining % (1000 * 60)) / 1000);
      setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`);
    }, 1000);

    return () => clearInterval(updateTimer);
  }, [showWarning, sessionStatus]);

  const handleRefreshSession = async () => {
    setIsRefreshing(true);
    
    try {
      const success = await sessionManager.refreshSession();
      
      if (success) {
        setShowWarning(false);
        // Show success message briefly
        setTimeout(() => {
          // Could show a toast notification here
          console.log('Session refreshed successfully');
        }, 100);
      } else {
        // If refresh fails, the session manager will handle logout
        console.error('Failed to refresh session');
      }
    } catch (error) {
      console.error('Session refresh error:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleLogout = async () => {
    await sessionManager.logout();
  };

  const handleContinueSession = () => {
    // Just close the warning and update activity
    sessionManager.updateActivity();
    setShowWarning(false);
  };

  if (!showWarning || !sessionStatus) {
    return null;
  }

  const minutesLeft = Math.floor(sessionStatus.timeRemaining / (1000 * 60));

  return (
    <Dialog
      open={showWarning}
      onClose={(event, reason) => {
        // Prevent closing by clicking backdrop or pressing escape
        if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          return;
        }
      }}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Typography variant="h6" color="warning.main">
          Session Expiring Soon
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box mb={2}>
          <Alert severity="warning">
            Your session will expire in <strong>{timeLeft}</strong> due to inactivity.
          </Alert>
        </Box>
        
        <Typography variant="body1" paragraph>
          To continue working, please refresh your session or you will be automatically logged out.
        </Typography>
        
        <Box mt={2}>
          <Typography variant="body2" color="textSecondary">
            Time remaining: {timeLeft}
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={(sessionStatus.timeRemaining / (warningThresholdMinutes * 60 * 1000)) * 100}
            color="warning"
            sx={{ mt: 1 }}
          />
        </Box>
        
        {isRefreshing && (
          <Box mt={2}>
            <LinearProgress />
            <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
              Refreshing session...
            </Typography>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button 
          onClick={handleLogout}
          disabled={isRefreshing}
          color="error"
        >
          Logout
        </Button>
        <Button 
          onClick={handleContinueSession}
          disabled={isRefreshing}
          color="primary"
        >
          Continue Session
        </Button>
        <Button 
          onClick={handleRefreshSession}
          disabled={isRefreshing}
          variant="contained"
          color="primary"
        >
          {isRefreshing ? 'Refreshing...' : 'Refresh Session'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionWarning;
