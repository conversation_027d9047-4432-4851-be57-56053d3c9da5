#!/bin/bash

# CCALC Security Implementation Test Script
# Tests all implemented security features

echo "🔒 CCALC Security Implementation Test Suite"
echo "==========================================="

# Backend URL
BACKEND_URL="${BACKEND_URL:-http://localhost:3000}"
FRONTEND_URL="${FRONTEND_URL:-http://localhost:3001}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test result counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC} - $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAIL${NC} - $test_name: $message"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Function to make HTTP requests with error handling
make_request() {
    local method="$1"
    local url="$2"
    local data="$3"
    local headers="$4"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" "$url" \
             -H "Content-Type: application/json" \
             ${headers:+-H "$headers"} \
             -d "$data" \
             -w "\n%{http_code}"
    else
        curl -s -X "$method" "$url" \
             ${headers:+-H "$headers"} \
             -w "\n%{http_code}"
    fi
}

echo -e "\n${BLUE}1. Testing Backend Health${NC}"
echo "------------------------"

# Test backend health endpoint
response=$(make_request "GET" "$BACKEND_URL/health")
status_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

if [ "$status_code" = "200" ]; then
    print_result "Backend Health Check" "PASS"
else
    print_result "Backend Health Check" "FAIL" "Status: $status_code"
fi

echo -e "\n${BLUE}2. Testing CSRF Token Endpoints${NC}"
echo "--------------------------------"

# Test CSRF token generation
csrf_response=$(make_request "GET" "$BACKEND_URL/api/csrf-token")
csrf_status=$(echo "$csrf_response" | tail -n1)
csrf_body=$(echo "$csrf_response" | head -n -1)

if [ "$csrf_status" = "200" ]; then
    csrf_token=$(echo "$csrf_body" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    if [ -n "$csrf_token" ]; then
        print_result "CSRF Token Generation" "PASS"
        
        # Test CSRF token validation
        validation_response=$(make_request "POST" "$BACKEND_URL/api/csrf-validate" \
                             '{"csrfToken":"'$csrf_token'"}' \
                             "X-CSRF-Token: $csrf_token")
        validation_status=$(echo "$validation_response" | tail -n1)
        
        if [ "$validation_status" = "200" ]; then
            print_result "CSRF Token Validation" "PASS"
        else
            print_result "CSRF Token Validation" "FAIL" "Status: $validation_status"
        fi
    else
        print_result "CSRF Token Generation" "FAIL" "No token in response"
    fi
else
    print_result "CSRF Token Generation" "FAIL" "Status: $csrf_status"
fi

echo -e "\n${BLUE}3. Testing Authentication Flow${NC}"
echo "-------------------------------"

# Test admin login initiation
login_init_response=$(make_request "POST" "$BACKEND_URL/api/auth/admin/login/init" \
                     '{"username":"admin"}')
init_status=$(echo "$login_init_response" | tail -n1)

if [ "$init_status" = "200" ]; then
    print_result "Admin Login Initiation" "PASS"
    
    challenge=$(echo "$login_init_response" | head -n -1 | grep -o '"challenge":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$challenge" ]; then
        # Note: We can't complete the login without proper credentials
        # This is just testing the endpoint availability
        print_result "Challenge Generation" "PASS"
    else
        print_result "Challenge Generation" "FAIL" "No challenge in response"
    fi
else
    print_result "Admin Login Initiation" "FAIL" "Status: $init_status"
fi

echo -e "\n${BLUE}4. Testing Session Endpoints${NC}"
echo "-----------------------------"

# Test session endpoint (should fail without auth)
session_response=$(make_request "GET" "$BACKEND_URL/api/auth/session")
session_status=$(echo "$session_response" | tail -n1)

if [ "$session_status" = "401" ]; then
    print_result "Session Endpoint Security" "PASS"
else
    print_result "Session Endpoint Security" "FAIL" "Expected 401, got $session_status"
fi

# Test admin session endpoint (should fail without auth)
admin_session_response=$(make_request "GET" "$BACKEND_URL/api/auth/admin/session")
admin_session_status=$(echo "$admin_session_response" | tail -n1)

if [ "$admin_session_status" = "401" ]; then
    print_result "Admin Session Endpoint Security" "PASS"
else
    print_result "Admin Session Endpoint Security" "FAIL" "Expected 401, got $admin_session_status"
fi

echo -e "\n${BLUE}5. Testing Frontend Availability${NC}"
echo "---------------------------------"

# Test frontend health
frontend_response=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" 2>/dev/null || echo "000")

if [ "$frontend_response" = "200" ] || [ "$frontend_response" = "404" ]; then
    print_result "Frontend Availability" "PASS"
else
    print_result "Frontend Availability" "FAIL" "Status: $frontend_response"
fi

echo -e "\n${BLUE}6. Testing CORS Configuration${NC}"
echo "------------------------------"

# Test CORS preflight
cors_response=$(curl -s -X OPTIONS "$BACKEND_URL/api/auth/csrf-token" \
               -H "Origin: $FRONTEND_URL" \
               -H "Access-Control-Request-Method: GET" \
               -H "Access-Control-Request-Headers: Content-Type" \
               -w "%{http_code}" \
               -o /dev/null)

if [ "$cors_response" = "200" ] || [ "$cors_response" = "204" ]; then
    print_result "CORS Preflight" "PASS"
else
    print_result "CORS Preflight" "FAIL" "Status: $cors_response"
fi

echo -e "\n${BLUE}7. Testing Rate Limiting${NC}"
echo "------------------------"

# Test rate limiting by making multiple requests quickly
rate_limit_pass=true
for i in {1..5}; do
    response=$(make_request "GET" "$BACKEND_URL/api/csrf-token")
    status=$(echo "$response" | tail -n1)
    if [ "$status" != "200" ]; then
        rate_limit_pass=false
        break
    fi
done

if [ "$rate_limit_pass" = true ]; then
    print_result "Basic Rate Limiting" "PASS"
else
    print_result "Basic Rate Limiting" "FAIL" "Rate limit exceeded unexpectedly"
fi

echo -e "\n${BLUE}8. Testing Error Handling${NC}"
echo "--------------------------"

# Test invalid endpoint
invalid_response=$(make_request "GET" "$BACKEND_URL/api/invalid-endpoint")
invalid_status=$(echo "$invalid_response" | tail -n1)

if [ "$invalid_status" = "404" ]; then
    print_result "404 Error Handling" "PASS"
else
    print_result "404 Error Handling" "FAIL" "Expected 404, got $invalid_status"
fi

# Test malformed JSON
malformed_response=$(curl -s -X POST "$BACKEND_URL/api/auth/admin/login/init" \
                    -H "Content-Type: application/json" \
                    -d "invalid json" \
                    -w "%{http_code}")
malformed_status=$(echo "$malformed_response" | tail -n1)

if [ "$malformed_status" = "400" ]; then
    print_result "Malformed JSON Handling" "PASS"
else
    print_result "Malformed JSON Handling" "FAIL" "Expected 400, got $malformed_status"
fi

echo -e "\n${BLUE}9. Testing Security Headers${NC}"
echo "----------------------------"

# Test security headers
headers_response=$(curl -s -I "$BACKEND_URL/health")
if echo "$headers_response" | grep -i "x-frame-options" > /dev/null; then
    print_result "Security Headers (X-Frame-Options)" "PASS"
else
    print_result "Security Headers (X-Frame-Options)" "FAIL" "Header not found"
fi

if echo "$headers_response" | grep -i "x-content-type-options" > /dev/null; then
    print_result "Security Headers (X-Content-Type-Options)" "PASS"
else
    print_result "Security Headers (X-Content-Type-Options)" "FAIL" "Header not found"
fi

echo -e "\n${YELLOW}=========================================${NC}"
echo -e "${YELLOW}Test Summary${NC}"
echo -e "${YELLOW}=========================================${NC}"
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Security implementation is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please review the implementation.${NC}"
    exit 1
fi
