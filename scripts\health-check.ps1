# Check the status of all CCALC services
# This script provides a quick health check of all services in the stack

# PowerShell colors for better readability
$GREEN = "Green"
$RED = "Red"
$YELLOW = "Yellow"
$NC = "White" # Default color

Write-Host "===== CCALC System Health Check =====" -ForegroundColor $YELLOW
Write-Host ""

# Check if docker and docker-compose are available
if (!(Get-Command docker -ErrorAction SilentlyContinue) -or !(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Docker and/or docker-compose are not installed" -ForegroundColor $RED
    exit 1
}

# Check container status
Write-Host "Container Status:" -ForegroundColor $YELLOW
docker-compose ps

# Check backend logs
Write-Host ""
Write-Host "Backend Logs (last 5 lines):" -ForegroundColor $YELLOW
docker-compose logs --tail=5 backend

# Check MongoDB connection
Write-Host ""
Write-Host "MongoDB Connection Test:" -ForegroundColor $YELLOW
$mongoResult = docker-compose exec mongo mongosh --quiet --username root --password example --eval "db.adminCommand('ping')" 2>&1
if ($mongoResult -match '"ok"\s*:\s*1') {
    Write-Host "MongoDB connection successful" -ForegroundColor $GREEN
}
else {
    Write-Host "MongoDB connection failed" -ForegroundColor $RED
    Write-Host $mongoResult
}

# Check backend API health
Write-Host ""
Write-Host "Backend API Health Check:" -ForegroundColor $YELLOW
try {
    $apiResponse = Invoke-WebRequest -Uri http://localhost:3000/health -UseBasicParsing -ErrorAction Stop
    if ($apiResponse.Content -match "healthy") {
        Write-Host "Backend API is healthy" -ForegroundColor $GREEN
    }
    else {
        Write-Host "Backend API is not healthy" -ForegroundColor $RED
    }
}
catch {
    Write-Host "Backend API is not responding or unhealthy" -ForegroundColor $RED
    Write-Host $_.Exception.Message
}

# Check frontend
Write-Host ""
Write-Host "Frontend Health Check:" -ForegroundColor $YELLOW
try {
    $frontendResponse = Invoke-WebRequest -Uri http://localhost -Method Head -UseBasicParsing -ErrorAction Stop
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "Frontend is accessible" -ForegroundColor $GREEN
    }
    else {
        Write-Host "Frontend returned status code: $($frontendResponse.StatusCode)" -ForegroundColor $RED
    }
}
catch {
    Write-Host "Frontend is not accessible" -ForegroundColor $RED
    Write-Host $_.Exception.Message
}

Write-Host ""
Write-Host "===== Health Check Complete =====" -ForegroundColor $YELLOW
