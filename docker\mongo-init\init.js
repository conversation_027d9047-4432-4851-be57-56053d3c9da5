// MongoDB initialization script
db = db.getSiblingDB('ccalc');

// Create collections
db.createCollection('users');
db.createCollection('admins');
db.createCollection('builds');
db.createCollection('chats');
db.createCollection('calls');
db.createCollection('devices');
db.createCollection('tokenBlacklists');

// Generate a default PPK key pair for admin initialization
// WARNING: This is a development key - in production, generate unique keys per admin
// NOTE: The automatic admin creation has been disabled to prevent conflicts with custom admin setup
/*
const defaultPPKPublicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1H5KA5wG/ESBLf5sHJ01
y8lY5aNE6fh+pt24VVJ1QUmKTUBRD9BBk06QXU9+6Z+WU+P7cI5qAZY0RPTnYgAr
qFXO2C4JlXI8DkY5bUBifHKRgVuSEysFQbRYZDZCzY8TBB7OJu1qmJJywYcFGFRR
JDc+GJMUsfNPnwmQtI6HYVV1QP1eRu/Jzwx1BYZq6VkDTfF2Qb+e1SLFe0q9V9Bc
Vm+Z8/qmx9+0GhqsZWEXjLdKSr4hQDYGGKqBpk/2QHUJkAmfBxkUPnkCKxT71s3G
UpTrxkQDQcWjIZqkCNZPNjC6kr+U0M21MPV6W7HVgEYKtRTQrWsAhMZELBTOzNzA
kwIDAQAB
-----END PUBLIC KEY-----`;

// Create admin user with hashed password (using bcrypt in real-world)
// This is a placeholder password 'admin123' - you should change this immediately in production
db.admins.insertOne({
  username: 'admin',
  password: '$2b$10$9Gg8n6R5oVS8H4H9UKM2z.7QgiPBUzu5C3blj/1d/pKiZdTuNcb62', // admin123
  email: '<EMAIL>',
  role: 'superadmin',
  isActive: true,
  lastLogin: null,
  ppkPublicKey: defaultPPKPublicKey, // Adding the PPK public key
  sessionTokens: [],
  createdAt: new Date(),
  updatedAt: new Date()
});
*/

// Note: Admin user will be created using the setup-admin-with-ppk.js script

// Create indexes
db.users.createIndex({ username: 1 }, { unique: true });
db.users.createIndex({ email: 1 }, { unique: true });
db.admins.createIndex({ username: 1 }, { unique: true });
db.admins.createIndex({ email: 1 }, { unique: true });
db.builds.createIndex({ version: 1, platform: 1 }, { unique: true });
db.devices.createIndex({ deviceId: 1 }, { unique: true });
db.tokenBlacklists.createIndex({ token: 1 }, { unique: true });
db.tokenBlacklists.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });

print('MongoDB initialization completed successfully');
