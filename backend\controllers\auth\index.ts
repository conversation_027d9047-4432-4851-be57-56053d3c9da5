import { Router } from 'express';
import { logoutController } from './logout.controller';
import { verifySessionController } from './session.controller';
import { auth } from '../../middleware/auth';
// Import calculator auth routes
import calculatorAuthRouter from '../../api/auth/calculator';

const authSubRouter = Router();

// Calculator authentication (mobile app)
authSubRouter.use('/calculator', calculatorAuthRouter);

// Session verification
authSubRouter.get('/session', auth, verifySessionController);

// Logout route
authSubRouter.post('/logout', auth, logoutController);

export default authSubRouter;
