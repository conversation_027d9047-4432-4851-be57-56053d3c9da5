import type { NextApiRequest, NextApiResponse } from 'next';
import { rateLimit } from '../../../utils/rateLimit';

const limiter = rateLimit({
  interval: 60 * 1000, // 60 seconds
  uniqueTokenPerInterval: 500, // Max 500 users per second
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await limiter.check(res, 2, 'BACKUP_RATE_LIMIT'); // 2 requests per minute (backups are resource-intensive)

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Check if admin is logged in
    const adminSessionResponse = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/auth/session`, {
      method: 'GET',
      headers: {
        'Cookie': req.headers.cookie || '',
        'Content-Type': 'application/json',
      },
    });

    if (!adminSessionResponse.ok) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Forward request to backend API
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/system/backup`, {
      method: 'POST',
      headers: {
        'Cookie': req.headers.cookie || '',
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    return res.status(200).json(data);
  } catch (error) {
    console.error('System backup error:', error);
    return res.status(500).json({ error: 'Failed to create system backup' });
  }
}
