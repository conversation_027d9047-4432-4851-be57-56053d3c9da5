import mongoose, { Schema, Document } from 'mongoose';

export interface ISystemConfig extends Document {
  type: 'system' | 'security';
  settings: any;
  updatedBy: mongoose.Types.ObjectId;
  updatedAt: Date;
  createdAt: Date;
}

const SystemConfigSchema: Schema = new Schema({
  type: {
    type: String,
    required: true,
    enum: ['system', 'security'],
    index: true
  },
  settings: {
    type: Schema.Types.Mixed,
    required: true,
    default: {}
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'systemconfigs'
});

// Ensure only one config per type
SystemConfigSchema.index({ type: 1 }, { unique: true });

// Update the updatedAt field on save
SystemConfigSchema.pre('save', function(this: ISystemConfig, next) {
  this.updatedAt = new Date();
  next();
});

export default mongoose.model<ISystemConfig>('SystemConfig', SystemConfigSchema);
