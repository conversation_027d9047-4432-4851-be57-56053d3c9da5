/**
 * BLE Utility Functions
 * Common utility functions for BLE operations, error handling, and device detection
 */

import { 
  BLE_ERROR_MESSAGES, 
  DEVICE_PATTERNS, 
  isLocalhostEnvironment,
  isSecureContext,
  getBrowserCompatibility
} from '../constants/ble';
import type { DiscoveredBleDevice, BleOperationResult } from '../types/ble';

/**
 * Check if Web Bluetooth is supported in the current browser
 */
export const isWebBluetoothSupported = (): boolean => {
  return typeof navigator !== 'undefined' && 'bluetooth' in navigator;
};

/**
 * Check if Web Bluetooth is available (not just supported)
 */
export const checkWebBluetoothAvailability = async (): Promise<{ available: boolean; error?: string }> => {
  if (!isWebBluetoothSupported()) {
    return { available: false, error: BLE_ERROR_MESSAGES.NOT_SUPPORTED };
  }

  try {
    const available = await navigator.bluetooth.getAvailability();
    if (!available) {
      return { available: false, error: BLE_ERROR_MESSAGES.DISABLED };
    }
    return { available: true };
  } catch (error: any) {
    console.warn('Could not check Bluetooth availability:', error);
    return { available: false, error: `Availability check failed: ${error.message}` };
  }
};

/**
 * Validate secure context for BLE operations
 */
export const validateSecureContext = (): { valid: boolean; error?: string } => {
  if (!isSecureContext()) {
    if (isLocalhostEnvironment()) {
      return { valid: false, error: BLE_ERROR_MESSAGES.SECURITY_ERROR };
    } else {
      return { valid: false, error: BLE_ERROR_MESSAGES.HTTPS_REQUIRED };
    }
  }
  return { valid: true };
};

/**
 * Comprehensive BLE environment check
 */
export const validateBleEnvironment = async (): Promise<BleOperationResult> => {
  // Check browser compatibility
  const browserCompat = getBrowserCompatibility();
  if (!browserCompat.supported) {
    return { 
      success: false, 
      error: `${BLE_ERROR_MESSAGES.NOT_SUPPORTED} Reason: ${browserCompat.reason}` 
    };
  }

  // Check secure context
  const secureContext = validateSecureContext();
  if (!secureContext.valid) {
    return { success: false, error: secureContext.error };
  }

  // Check Web Bluetooth support
  if (!isWebBluetoothSupported()) {
    return { success: false, error: BLE_ERROR_MESSAGES.NOT_SUPPORTED };
  }

  // Check Web Bluetooth availability
  const availability = await checkWebBluetoothAvailability();
  if (!availability.available) {
    return { success: false, error: availability.error };
  }

  return { success: true };
};

/**
 * Parse and categorize BLE errors
 */
export const parseBleError = (error: any): string => {
  if (!error) return 'Unknown error occurred';

  const errorMessage = error.message || error.toString();
  const errorName = error.name;

  // Map specific error types to user-friendly messages
  switch (errorName) {
    case 'NotAllowedError':
      return BLE_ERROR_MESSAGES.PERMISSION_DENIED;
    case 'NotFoundError':
      return BLE_ERROR_MESSAGES.NOT_FOUND;
    case 'NotSupportedError':
      return BLE_ERROR_MESSAGES.NOT_SUPPORTED;
    case 'SecurityError':
      return BLE_ERROR_MESSAGES.SECURITY_ERROR;
    case 'AbortError':
      return 'Operation was cancelled';
    case 'TimeoutError':
      return BLE_ERROR_MESSAGES.CONNECTION_TIMEOUT;
    default:
      break;
  }

  // Check for specific error patterns in message
  if (errorMessage.includes('not allowed') || errorMessage.includes('disabled')) {
    return BLE_ERROR_MESSAGES.NOT_ALLOWED;
  }
  if (errorMessage.includes('https') || errorMessage.includes('secure context')) {
    return BLE_ERROR_MESSAGES.HTTPS_REQUIRED;
  }
  if (errorMessage.includes('User cancelled') || errorMessage.includes('cancelled')) {
    return 'Device selection was cancelled';
  }
  if (errorMessage.includes('timeout')) {
    return BLE_ERROR_MESSAGES.CONNECTION_TIMEOUT;
  }
  if (errorMessage.includes('disconnected')) {
    return BLE_ERROR_MESSAGES.DEVICE_DISCONNECTED;
  }

  return `BLE operation failed: ${errorMessage}`;
};

/**
 * Detect device type based on name and services
 */
export const detectDeviceType = (
  deviceName?: string, 
  services?: string[]
): 'calculator' | 'generic' | 'unknown' => {
  if (!deviceName && (!services || services.length === 0)) {
    return 'unknown';
  }

  const name = (deviceName || '').toLowerCase();
  // Check for calculator device
  if (DEVICE_PATTERNS.CALCULATOR.names.some(pattern => name.includes(pattern)) ||
      services?.some(uuid => DEVICE_PATTERNS.CALCULATOR.services.includes(uuid as any))) {
    return 'calculator';
  }
  // Check for audio device
  if (DEVICE_PATTERNS.AUDIO.names.some(pattern => name.includes(pattern)) ||
      services?.some(uuid => DEVICE_PATTERNS.AUDIO.services.includes(uuid as any))) {
    return 'generic';
  }

  // If has services but not specifically identified, it's generic
  if (services && services.length > 0) {
    return 'generic';
  }

  return 'unknown';
};

/**
 * Generate a simulated RSSI value (since Web Bluetooth doesn't provide real RSSI)
 */
export const generateSimulatedRssi = (): number => {
  // Generate a realistic RSSI value between -30 and -100 dBm
  return Math.floor(Math.random() * 70) - 100;
};

/**
 * Format device name for display
 */
export const formatDeviceName = (name?: string, id?: string): string => {
  if (name && name.trim()) {
    return name.trim();
  }
  if (id) {
    return `Device ${id.substring(0, 8)}...`;
  }
  return 'Unknown Device';
};

/**
 * Format UUID for display (shorter format)
 */
export const formatUuid = (uuid: string): string => {
  // Convert to uppercase and add standard UUID formatting if needed
  const formattedUuid = uuid.toUpperCase();
  
  // If it's a 16-bit UUID, expand it to full format
  if (formattedUuid.length === 4) {
    return `0000${formattedUuid}-0000-1000-8000-00805F9B34FB`;
  }
  
  return formattedUuid;
};

/**
 * Get friendly service name from UUID
 */
export const getServiceName = (uuid: string): string => {
  const formattedUuid = formatUuid(uuid);
  
  const serviceNames: Record<string, string> = {
    '0000180F-0000-1000-8000-00805F9B34FB': 'Battery Service',
    '0000180A-0000-1000-8000-00805F9B34FB': 'Device Information',
    '0000180D-0000-1000-8000-00805F9B34FB': 'Heart Rate',
    '00001826-0000-1000-8000-00805F9B34FB': 'Fitness Machine',
    '0000110B-0000-1000-8000-00805F9B34FB': 'Audio Sink',
    '0000110E-0000-1000-8000-00805F9B34FB': 'A2DP',
    '0000111E-0000-1000-8000-00805F9B34FB': 'Hands-Free',
    '00001234-0000-1000-8000-00805F9B34FB': 'Calculator Service',
    '0000CAC0-0000-1000-8000-00805F9B34FB': 'Calculator Service (Dev)',
  };

  return serviceNames[formattedUuid] || `Service ${uuid.substring(0, 8)}...`;
};

/**
 * Create a timeout promise for operations
 */
export const createTimeoutPromise = <T>(timeoutMs: number, errorMessage?: string): Promise<T> => {
  return new Promise<T>((_, reject) => {
    setTimeout(() => {
      reject(new Error(errorMessage || BLE_ERROR_MESSAGES.CONNECTION_TIMEOUT));
    }, timeoutMs);
  });
};

/**
 * Retry operation with exponential backoff
 */
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        break;
      }

      // Don't retry on user cancellation or permission errors
      if (error.name === 'NotAllowedError' || 
          error.name === 'AbortError' ||
          error.message?.includes('User cancelled')) {
        throw error;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

/**
 * Retry utility with exponential backoff
 */
export const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        break;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      logBleOperation(`Retry attempt ${attempt}/${maxAttempts} failed, retrying in ${Math.round(delay)}ms`, { error: error }, 'warn');
    }
  }
  
  throw lastError!;
};

/**
 * Validate device compatibility
 */
export const validateDeviceCompatibility = (device: DiscoveredBleDevice): { 
  compatible: boolean; 
  warnings: string[];
  recommendations: string[];
} => {
  const warnings: string[] = [];
  const recommendations: string[] = [];

  if (!device.name) {
    warnings.push('Device name not available');
  }

  if (!device.advertisingData?.services || device.advertisingData.services.length === 0) {
    warnings.push('No advertising services detected');
    recommendations.push('Device may have limited functionality');
  }

  if (device.rssi && device.rssi < -80) {
    warnings.push('Weak signal strength detected');
    recommendations.push('Move closer to the device for better connection reliability');
  }

  if (!device.connectable) {
    warnings.push('Device may not be connectable');
    recommendations.push('Ensure device is in pairing mode');
  }

  const compatible = device.connectable !== false;

  return { compatible, warnings, recommendations };
};

/**
 * Log BLE operation for debugging
 */
export const logBleOperation = (
  operation: string,
  details: any,
  level: 'info' | 'warn' | 'error' = 'info'
): void => {
  if (isLocalhostEnvironment()) {
    const timestamp = new Date().toISOString();
    const logMessage = `[BLE ${timestamp}] ${operation}:`;
    
    switch (level) {
      case 'error':
        console.error(logMessage, details);
        break;
      case 'warn':
        console.warn(logMessage, details);
        break;
      default:
        console.log(logMessage, details);
        break;
    }
  }
};
