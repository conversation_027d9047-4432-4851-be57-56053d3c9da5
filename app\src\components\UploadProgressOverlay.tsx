/**
 * UploadProgressOverlay - iOS-native upload progress indicator
 * WhatsApp-like persistent upload progress with iOS-native design
 * Optimized for iPhone experience
 */

import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import * as Haptics from 'expo-haptics';
import { chatStyles } from '../styles/ChatStyles';

interface UploadProgressOverlayProps {
  uploadStatus: 'pending' | 'uploading' | 'uploaded' | 'failed';
  uploadProgress?: number;
  onRetry?: () => void;
  retryDisabled?: boolean;
}

export const UploadProgressOverlay: React.FC<UploadProgressOverlayProps> = ({
  uploadStatus,
  uploadProgress = 0,
  onRetry,
  retryDisabled,
}) => {
  // Don't show overlay if upload is completed or not started
  if (uploadStatus === 'uploaded' || uploadStatus === 'pending') {
    return null;
  }

  const renderContent = () => {
    switch (uploadStatus) {
      case 'uploading':
        return (
          <View style={chatStyles.uploadProgressContainer}>
            <View style={chatStyles.uploadProgressBar}>
              <View 
                style={[
                  chatStyles.uploadProgressFill,
                  { width: `${uploadProgress}%` }
                ]}
              />
            </View>
            <Text style={chatStyles.uploadProgressText}>
              {Math.round(uploadProgress)}%
            </Text>
          </View>
        );
      
      case 'failed':
        return (
          <View style={chatStyles.uploadProgressContainer}>
            <Text style={chatStyles.uploadProgressText}>Upload failed</Text>
            {onRetry && (
              <TouchableOpacity 
                style={[chatStyles.uploadRetryButton, retryDisabled && { opacity: 0.5 }]}
                onPress={() => {
                  if (!retryDisabled) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                    onRetry();
                  }
                }}
                activeOpacity={retryDisabled ? 1 : 0.8}
                disabled={retryDisabled}
              >
                <Text style={chatStyles.uploadRetryButtonText}>Retry</Text>
              </TouchableOpacity>
            )}
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <View style={chatStyles.uploadOverlay}>
      {renderContent()}
    </View>
  );
};
