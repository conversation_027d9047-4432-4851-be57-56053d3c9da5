#!/bin/bash

# CocoaPods Installation and Setup Script for CCALC
# This script handles the complete setup for macOS/iOS development

set -e

echo "🔧 CCALC iOS Setup - CocoaPods Installation & Configuration"
echo "================================================================"

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script is designed for macOS only."
    echo "   For Windows development, use React Native CLI or EAS Build."
    exit 1
fi

# Check if we're in the correct directory
if [ ! -f "Podfile" ]; then
    echo "❌ Error: Podfile not found. Please run this script from the ios directory."
    echo "   Expected location: CCALC/app/ios/"
    exit 1
fi

echo "📍 Current directory: $(pwd)"
echo "✅ Podfile found"

# Function to install CocoaPods
install_cocoapods() {
    echo ""
    echo "🍫 Installing CocoaPods..."
    
    # Check if Homebrew is available (preferred method)
    if command -v brew &> /dev/null; then
        echo "📦 Using Homebrew to install CocoaPods..."
        brew install cocoapods
    else
        echo "💎 Using RubyGems to install CocoaPods..."
        echo "   This may require sudo permissions..."
        
        # Check if we have write permissions to gem directory
        if gem environment | grep -q "INSTALLATION DIRECTORY.*$(id -un)"; then
            gem install cocoapods
        else
            echo "   Installing with sudo (you may be prompted for password)..."
            sudo gem install cocoapods
        fi
    fi
}

# Check for CocoaPods installation
echo ""
echo "🔍 Checking for CocoaPods installation..."

if ! command -v pod &> /dev/null; then
    echo "❌ CocoaPods not found."
    echo ""
    echo "CocoaPods is required for iOS development with React Native."
    echo "Would you like to install it now? (y/n)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        install_cocoapods
    else
        echo ""
        echo "❌ Cannot continue without CocoaPods."
        echo ""
        echo "To install manually:"
        echo "  Method 1 (Homebrew): brew install cocoapods"
        echo "  Method 2 (RubyGems): sudo gem install cocoapods"
        echo ""
        echo "Then run this script again."
        exit 1
    fi
else
    echo "✅ CocoaPods found: $(pod --version)"
fi

# Update CocoaPods repo (this might take a while on first run)
echo ""
echo "🔄 Updating CocoaPods repositories..."
echo "   This may take several minutes on first run..."
pod repo update

# Clean any existing installations
echo ""
echo "🧹 Cleaning previous installations..."
if [ -d "Pods" ]; then
    echo "   Removing existing Pods directory..."
    rm -rf Pods
fi

if [ -f "Podfile.lock" ]; then
    echo "   Removing existing Podfile.lock..."
    rm -f Podfile.lock
fi

# Install dependencies
echo ""
echo "📦 Installing iOS dependencies..."
pod install --verbose

# Verify installation
echo ""
echo "🔍 Verifying installation..."

required_files=(
    "Pods"
    "Podfile.lock"
    "CCALC.xcworkspace"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -e "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ All required files present!"
else
    echo "❌ Missing files:"
    printf '   %s\n' "${missing_files[@]}"
    exit 1
fi

# Update workspace to include Pods
echo ""
echo "🔗 Updating Xcode workspace configuration..."

cat > CCALC.xcworkspace/contents.xcworkspacedata << EOF
<?xml version="1.0" encoding="UTF-8"?>
<Workspace
   version = "1.0">
   <FileRef
      location = "group:CCALC.xcodeproj">
   </FileRef>
   <FileRef
      location = "group:Pods/Pods.xcodeproj">
   </FileRef>
</Workspace>
EOF

echo "✅ Workspace updated"

# Success message
echo ""
echo "🎉 Setup Complete!"
echo "================================================================"
echo ""
echo "📖 Next Steps:"
echo "1. Open Xcode: open CCALC.xcworkspace"
echo "2. Select a device or simulator"
echo "3. Press Cmd+R to build and run"
echo ""
echo "⚠️  Important Notes:"
echo "• Always use CCALC.xcworkspace (not .xcodeproj)"
echo "• Start Metro bundler with 'npm start' in the app root"
echo "• For device testing, configure code signing in Xcode"
echo ""
echo "🐛 If you encounter issues:"
echo "• Clean build: Product → Clean Build Folder in Xcode"
echo "• Reset Metro: npm start -- --reset-cache"
echo "• Reinstall pods: rm -rf Pods Podfile.lock && pod install"
