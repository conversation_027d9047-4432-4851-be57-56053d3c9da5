/* All auth and login styles are now unified in globals.css for consistency and maintainability. */

/* PPK Upload Component Styles */
.ppk-upload {
  border: 2px solid #0070f3;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #f0f7ff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ppk-status {
  margin-bottom: 10px;
}

.ppk-success {
  color: #28a745;
  font-weight: bold;
}

.ppk-instruction {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.file-input:disabled {
  background-color: #eee;
  cursor: not-allowed;
}

.reset-ppk {
  margin-top: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
}

.reset-ppk:hover {
  background-color: #e2e6ea;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.loading-indicator span {
  margin-left: 10px;
}

.error-message {
  color: #dc3545;
  margin-top: 5px;
  font-size: 0.9rem;
}

/* Login Form Styles */
.login-container {
  max-width: 500px;
  margin: 50px auto;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: white;
}

.login-header {
  text-align: center;
  margin-bottom: 25px;
}

.login-header h1 {
  margin-bottom: 5px;
  color: #333;
}

.subtitle {
  color: #666;
  font-size: 1rem;
}

.security-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
}

.badge-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.badge-text {
  font-size: 0.9rem;
  color: #495057;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

button[type="submit"] {
  width: 100%;
  padding: 12px;
  background-color: #0056b3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
}

button[type="submit"]:hover {
  background-color: #004494;
}

button[type="submit"]:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

.security-note {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border-left: 4px solid #0056b3;
  font-size: 0.9rem;
  color: #495057;
}

button.loading {
  position: relative;
  color: transparent;
}

button.loading::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin: -10px 0 0 -10px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
