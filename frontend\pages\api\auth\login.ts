import type { NextApiRequest, NextApiResponse } from 'next';
import { rateLimit } from '../../../utils/rateLimit';

const limiter = rateLimit({
  interval: 60 * 1000, // 60 seconds
  uniqueTokenPerInterval: 500, // Max 500 users per second
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await limiter.check(res, 10, 'LOGIN_RATE_LIMIT'); // 10 requests per minute

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { username, expression, deviceFingerprint, bleUUID } = req.body;

    if (!username || !expression || !deviceFingerprint || !bleUUID) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Forward request to backend API
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/auth/user/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username,
        expression,
        deviceFingerprint,
        bleUUID,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    // Set JWT token as HttpOnly cookie
    if (data.token) {
      res.setHeader('Set-Cookie', `auth-token=${data.token}; HttpOnly; Path=/; Max-Age=43200; SameSite=Strict${process.env.NODE_ENV === 'production' ? '; Secure' : ''}`);
    }

    return res.status(200).json({ user: data.user });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ error: 'Authentication failed' });
  }
}
