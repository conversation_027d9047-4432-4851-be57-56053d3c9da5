import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import DashboardCard from '../../components/admin/DashboardCard';
import Button from '../../components/admin/Button';
import { apiClient } from '../../utils/axiosClient';
import withAdminAuth from '../../components/hoc/withAdminAuth';

interface KeyData {
  publicKey: string;
  privateKey: string;
  keyVersion: string;
}

const AdminKeysPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [generateDialogOpen, setGenerateDialogOpen] = useState(false);
  const [keyGenerated, setKeyGenerated] = useState(false);
  const [keyData, setKeyData] = useState<KeyData | null>(null);
  const [authMethod, setAuthMethod] = useState<string>('password');
  const [adminInfo, setAdminInfo] = useState<any>(null);

  useEffect(() => {
    fetchAdminInfo();
  }, []);

  const fetchAdminInfo = async () => {
    try {
      const response = await apiClient.frontend.get('/api/auth/admin/session');
      setAdminInfo(response.data.admin);
      setAuthMethod(response.data.admin.authMethod || 'password');
    } catch (error: any) {
      console.error('Error fetching admin info:', error);
      setError(`Failed to load admin information: ${error.response?.data?.error || error.message}`);
    }
  };

  const generateNewKeyPair = async () => {
    if (!adminInfo) return;
    
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await apiClient.frontend.post(`/api/admin/admins/${adminInfo._id}/generate-ppk`);
      
      setKeyData(response.data.ppk);
      setKeyGenerated(true);
      setSuccess('Key pair generated successfully!');
    } catch (error: any) {
      console.error('Error generating key pair:', error);
      setError(`Failed to generate key pair: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleAuthMethodChange = async (newMethod: string) => {
    if (!adminInfo) return;
    
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      await apiClient.frontend.put(`/api/admin/admins/${adminInfo._id}`, {
        authMethod: newMethod
      });
      
      setAuthMethod(newMethod);
      setSuccess(`Authentication method updated to ${newMethod === 'ppk' ? 'Public-Private Key' : 'Password'}`);
    } catch (error: any) {
      console.error('Error updating auth method:', error);
      setError(`Failed to update auth method: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const downloadPrivateKey = () => {
    if (!keyData) return;
    
    const element = document.createElement("a");
    const file = new Blob([keyData.privateKey], {type: 'text/plain'});
    element.href = URL.createObjectURL(file);
    element.download = `admin_private_key_${keyData.keyVersion}.pem`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const downloadPublicKey = () => {
    if (!keyData) return;
    
    const element = document.createElement("a");
    const file = new Blob([keyData.publicKey], {type: 'text/plain'});
    element.href = URL.createObjectURL(file);
    element.download = `admin_public_key_${keyData.keyVersion}.pem`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const closeGenerateDialog = () => {
    if (keyGenerated) {
      // Prompt user they might lose their key
      if (confirm('You will lose access to the private key after closing. Make sure you have downloaded it. Continue?')) {
        setGenerateDialogOpen(false);
        setKeyGenerated(false);
        setKeyData(null);
      }
    } else {
      setGenerateDialogOpen(false);
    }
  };

  return (
    <AdminLayout>
      <Head>
        <title>Security Keys - CCALC Admin</title>
      </Head>
      
      <div className="container">
        <div className="page-header">
          <h2 className="page-title">Security Keys Management</h2>
        </div>

        {error && (
          <div className="alert alert-danger mb-4">
            {/* Consider using an Icon component if available */}
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
            <span>{error}</span>
            <button onClick={() => setError(null)} className="alert-close" aria-label="Close">×</button>
          </div>
        )}

        {success && (
          <div className="alert alert-success mb-4">
            {/* Consider using an Icon component */}
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
              <polyline points="22 4 12 14.01 9 11.01"/>
            </svg>
            <span>{success}</span>
            <button onClick={() => setSuccess(null)} className="alert-close" aria-label="Close">×</button>
          </div>
        )}

        <div className="grid md:grid-cols-2 gap-5 mb-5">
          <div className="card">
            <h3 className="text-lg font-semibold mb-3">Authentication Method</h3>
            <p className="text-sm text-muted mb-4">
              Choose between password-based or public-private key (PPK) authentication for enhanced security.
            </p>
            
            <div className="space-y-3">
              <div className={`p-4 border rounded-md transition-all ${authMethod === 'password' ? 'border-primary bg-primary-light' : 'border-border'}`}>
                <div className="flex items-center mb-2">
                  {/* Icon for password */}
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-3 text-primary">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  <h4 className="font-medium">Password</h4>
                </div>
                <p className="text-xs text-muted mb-3">Traditional username and password login.</p>
                {authMethod !== 'password' && (
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleAuthMethodChange('password')}
                    disabled={loading}
                  >
                    Switch to Password
                  </Button>
                )}
                {authMethod === 'password' && <span className="text-xs font-medium text-success">Currently Active</span>}
              </div>
              
              <div className={`p-4 border rounded-md transition-all ${authMethod === 'ppk' ? 'border-primary bg-primary-light' : 'border-border'}`}>
                <div className="flex items-center mb-2">
                  {/* Icon for PPK */}
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-3 text-primary">
                     <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline>
                  </svg>
                  <h4 className="font-medium">Public-Private Key (PPK)</h4>
                </div>
                <p className="text-xs text-muted mb-3">Enhanced security with cryptographic key pairs.</p>
                {authMethod !== 'ppk' && (
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => handleAuthMethodChange('ppk')}
                    disabled={loading}
                  >
                    Switch to PPK
                  </Button>
                )}
                {authMethod === 'ppk' && <span className="text-xs font-medium text-success">Currently Active</span>}
              </div>
            </div>
          </div>
          
          <div className="card">
            <h3 className="text-lg font-semibold mb-3">Key Management</h3>
            <p className="text-sm text-muted mb-4">
              Generate and manage your public-private key pairs. Keep your private key safe.
            </p>
            
            <Button
              variant="primary"
              onClick={() => setGenerateDialogOpen(true)}
              disabled={loading}
              leftIcon={
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 12L2 12L9 4L9 20L2 12Z"></path> {/* Consider a more common 'key-plus' or 'generate' icon */}
                  <path d="M14 12L22 12L15 4L15 20L22 12Z"></path>
                </svg>
              }
              className="w-full md:w-auto"
            >
              Generate New Key Pair
            </Button>
            
            <div className="mt-5 pt-4 border-t border-border">
              <h4 className="text-sm font-medium mb-2">Current Setup</h4>
              <div className="text-xs text-muted space-y-1">
                <div className="flex justify-between">
                  <span>Authentication Method:</span>
                  <span className="font-semibold text-text">
                    {authMethod === 'ppk' ? 'Public-Private Key' : 'Password'}
                  </span>
                </div>
                {adminInfo && (
                  <>
                    <div className="flex justify-between">
                      <span>Admin Account:</span>
                      <span className="font-semibold text-text">{adminInfo.username}</span>
                    </div>
                    {adminInfo.lastKeyGenerated && (
                      <div className="flex justify-between">
                        <span>Last Key Generated:</span>
                        <span className="font-semibold text-text">
                          {new Date(adminInfo.lastKeyGenerated).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      
        {generateDialogOpen && (
          <div className="dialog-backdrop">
            <div className="dialog max-w-lg">
              <div className="dialog-header">
                <h3 className="dialog-title">
                  {keyGenerated ? 'Key Pair Generated' : 'Generate New Key Pair'}
                </h3>
                <button className="dialog-close" onClick={closeGenerateDialog} aria-label="Close">×</button>
              </div>
              <div className="dialog-content">
                {keyGenerated && keyData ? (
                  <div className="text-center">
                    <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-success-light mb-3">
                      {/* Icon for success */}
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-success">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                        <polyline points="22 4 12 14.01 9 11.01"/>
                      </svg>
                    </div>
                    <h4 className="text-lg font-medium mb-2">Key Pair Generated Successfully!</h4>
                    
                    <div className="alert alert-warning my-4">
                      <strong>IMPORTANT:</strong> This is the only time you will see your private key.
                      Download it now and keep it in a secure location.
                    </div>
                    
                    <div className="grid sm:grid-cols-2 gap-3 my-5">
                      <Button
                        variant="primary"
                        onClick={downloadPrivateKey}
                        leftIcon={
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                          </svg>
                        }
                        className="w-full"
                      >
                        Download Private Key (.pem)
                      </Button>
                      <Button
                        variant="secondary"
                        onClick={downloadPublicKey}
                        leftIcon={
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                          </svg>
                        }
                        className="w-full"
                      >
                        Download Public Key (.pem)
                      </Button>
                    </div>
                    
                    <div className="text-left text-xs text-muted bg-base-200 p-3 rounded-md">
                      <h5 className="font-semibold text-sm text-text mb-1">Next Steps:</h5>
                      <ol className="list-decimal list-inside space-y-1">
                        <li>Download both private and public keys.</li>
                        <li>Store your private key in a very secure location (e.g., password manager, encrypted drive).</li>
                        <li>If you intend to use PPK authentication, ensure it's selected as your authentication method.</li>
                        <li>The public key is stored on the server. You will use the private key for future logins with compatible client software.</li>
                      </ol>
                    </div>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm text-muted mb-4">
                      You are about to generate a new cryptographic key pair (public and private keys) for enhanced
                      security. This is recommended for administrators.
                    </p>
                    
                    <div className="alert alert-info mb-4">
                      <h5 className="font-semibold mb-1">Important Considerations:</h5>
                      <ul className="list-disc list-inside text-xs space-y-1">
                        <li>Your new private key will only be shown ONCE after generation.</li>
                        <li>You MUST download and securely store your private key. Loss of the private key may result in loss of access if PPK is your sole authentication method.</li>
                        <li>Generating a new key pair will invalidate any previously generated key pair for this admin account.</li>
                      </ul>
                    </div>
                    
                    <p className="text-sm text-muted">
                      After generating your key pair, you can switch your authentication method to use Public-Private Key authentication.
                    </p>
                  </div>
                )}
              </div>
              <div className="dialog-footer">
                {keyGenerated ? (
                  <Button variant="outline" onClick={closeGenerateDialog}>
                    Close & Acknowledge
                  </Button>
                ) : (
                  <>
                    <Button variant="ghost" onClick={() => setGenerateDialogOpen(false)} disabled={loading}>
                      Cancel
                    </Button>
                    <Button 
                      variant="primary"
                      onClick={generateNewKeyPair}
                      disabled={loading}
                      leftIcon={loading ? undefined : <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><polyline points="20 6 9 17 4 12"></polyline></svg>}
                    >
                      {loading ? 'Generating Keys...' : 'Confirm & Generate Key Pair'}
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Removed style jsx block */}
    </AdminLayout>
  );
};

export default withAdminAuth(AdminKeysPage);
