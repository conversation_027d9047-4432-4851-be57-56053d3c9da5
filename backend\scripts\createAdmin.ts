import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';
import { AdminModel } from '../models/Admin';
import { connectDatabase } from '../config/database';
import { generatePPKKeyPair, exportPublicKey, savePrivate<PERSON>ey } from '../utils/ppk';
import path from 'path';
import fs from 'fs';

// Load environment variables
dotenv.config();

/**
 * <PERSON><PERSON><PERSON> to create an admin user for development purposes
 * Usage: npx ts-node scripts/createAdmin.ts
 */
async function createAdmin() {
  try {
    // Connect to database
    await connectDatabase();
    
    console.log('Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await AdminModel.findOne({ username: 'admin' });
    
    if (existingAdmin) {
      console.log('Admin user already exists');
      process.exit(0);
    }
      // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    // Generate PPK key pair
    console.log('Generating PPK key pair for admin...');
    const { publicKey, privateKey } = generatePPKKeyPair();
    
    // Save private key to file
    const keysDir = path.join(__dirname, '../../keys');
    if (!fs.existsSync(keysDir)) {
      fs.mkdirSync(keysDir, { recursive: true });
    }
    
    const privateKeyPath = path.join(keysDir, 'admin.private.pem');
    savePrivateKey(privateKey, privateKeyPath);
    console.log(`Private key saved to: ${privateKeyPath}`);
    console.log('IMPORTANT: Keep this key secure and do not share it!');
    
    // Format public key for storage
    const publicKeyFormatted = exportPublicKey(publicKey);
    
    // Create new admin
    const admin = new AdminModel({
      username: 'admin',
      password: hashedPassword,
      email: '<EMAIL>',
      role: 'superadmin',
      isActive: true,
      ppkPublicKey: publicKeyFormatted, // Add the PPK public key
      sessionTokens: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    await admin.save();
    
    console.log('Admin user created successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

// Run the function
createAdmin();
