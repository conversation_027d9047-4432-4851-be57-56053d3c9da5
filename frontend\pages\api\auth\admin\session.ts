import type { NextApiRequest, NextApiResponse } from 'next';
import ServerApiClient from '../../../../utils/serverApiClient';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }    // Get token from Authorization header only
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('No Authorization Bearer token found');
      return res.status(401).json({ error: 'Authorization Bearer token required' });
    }

    const token = authHeader.substring(7);
    console.log('Session check - Using Authorization Bearer token');

    // Verify session with backend
    const serverApiClient = new ServerApiClient();
    console.log('Admin session: Making request to backend');
      try {
      const response = await serverApiClient.get('/api/auth/session', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });

      console.log('Admin session: Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.log('Admin session: Backend error:', errorData);
        return res.status(response.status).json(errorData);
      }      const data = await response.json();
      console.log('Admin session: Session verified successfully');
      
      return res.status(200).json(data);

    } catch (error) {
      console.error('Admin session error:', error);
      return res.status(500).json({ error: 'Failed to verify session' });
    }
  } catch (error) {
    console.error('Session handler error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
