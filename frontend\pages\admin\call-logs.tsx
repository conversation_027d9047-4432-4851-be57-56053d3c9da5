import React, { useEffect, useState } from 'react';
import <PERSON> from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import apiClient from '../../utils/apiClient';
import Button from '../../components/admin/Button';

interface CallLog {
  _id: string;
  callId: string;
  createdAt: string;
  status: string;
  callType: string;
  initiatorId?: { username: string };
  recipientId?: { username: string };
  duration?: number;
  recording?: { enabled: boolean; encryptedPath?: string };
}

const CallLogsPage: React.FC = () => {
  const [calls, setCalls] = useState<CallLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit] = useState(25);

  const fetchCalls = async (pageNum = 1) => {
    setLoading(true);
    setError(null);
    try {
      const res = await apiClient.get(`/api/calls/history?page=${pageNum}&limit=${limit}`);
      setCalls(res.data.calls || []);
      setTotalPages(res.data.pagination?.pages || 1);
      setPage(res.data.pagination?.currentPage || pageNum);
    } catch (e: any) {
      setError(e.response?.data?.error || 'Failed to fetch call logs. Please check the network or API status.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCalls(page);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, limit]);

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'answered':
        return 'badge-success';
      case 'missed':
      case 'declined':
      case 'failed':
        return 'badge-danger';
      case 'ringing':
      case 'initiated':
        return 'badge-warning';
      default:
        return 'badge-neutral';
    }
  };

  return (
    <AdminLayout>
      <Head>
        <title>Call Logs | CCALC Admin</title>
      </Head>
      <div className="container">
        <div className="page-header">
          <h1 className="page-title">Call Log History</h1>
          <p className="page-subtitle">Browse and review system call records.</p>
        </div>

        {error && (
          <div className="alert alert-danger mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 inline-block" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>
            <span>{error}</span>
            <button onClick={() => setError(null)} className="alert-close" aria-label="Close">×</button>
          </div>
        )}

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">All Calls</h3>
          </div>

          {loading && calls.length === 0 ? (
            <div className="loading-placeholder">
              <svg className="animate-spin h-8 w-8 text-primary mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p>Loading call logs...</p>
            </div>
          ) : !loading && calls.length === 0 && !error ? (
            <div className="empty-state">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.308 1.154a.5.5 0 00-.223.63l1.18 3.54A1 1 0 008.48 15h7.04a1 1 0 00.947-.684l1.18-3.54a.5.5 0 00-.223-.63l-2.308-1.154a1 1 0 01-.502-1.21l1.498-4.493A1 1 0 0117.72 3H21a2 2 0 012 2v14a2 2 0 01-2 2H3a2 2 0 01-2-2V5z" />
              </svg>
              <p>No call logs found.</p>
              {page > 1 && <Button onClick={() => setPage(1)} variant="outline" className="mt-2">Go to First Page</Button>}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table-auto w-full">
                <thead className="table-header">
                  <tr>
                    <th className="px-4 py-2 text-left">Timestamp</th>
                    <th className="px-4 py-2 text-left">Call ID</th>
                    <th className="px-4 py-2 text-left">Type</th>
                    <th className="px-4 py-2 text-left">Status</th>
                    <th className="px-4 py-2 text-left">Initiator</th>
                    <th className="px-4 py-2 text-left">Recipient</th>
                    <th className="px-4 py-2 text-right">Duration (s)</th>
                    <th className="px-4 py-2 text-center">Recording</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {calls.map((call) => (
                    <tr key={call._id} className="table-row">
                      <td className="border-t px-4 py-2 text-sm">{new Date(call.createdAt).toLocaleString()}</td>
                      <td className="border-t px-4 py-2 font-mono text-xs">{call.callId}</td>
                      <td className="border-t px-4 py-2">{call.callType}</td>
                      <td className="border-t px-4 py-2">
                        <span className={`badge ${getStatusBadge(call.status)}`}>{call.status}</span>
                      </td>
                      <td className="border-t px-4 py-2">{call.initiatorId?.username || 'N/A'}</td>
                      <td className="border-t px-4 py-2">{call.recipientId?.username || 'N/A'}</td>
                      <td className="border-t px-4 py-2 text-right">{call.duration ?? '-'}</td>
                      <td className="border-t px-4 py-2 text-center">
                        {call.recording?.enabled ? 
                          <span className="badge badge-info">Yes</span> : 
                          <span className="badge badge-neutral">No</span>
                        }
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {totalPages > 1 && (
            <div className="pagination-controls mt-6 flex justify-between items-center">
              <Button 
                onClick={() => setPage((p) => Math.max(1, p - 1))} 
                disabled={page === 1 || loading}
                variant="outline"
              >
                Previous
              </Button>
              <span className="text-sm text-muted">
                Page {page} of {totalPages}
              </span>
              <Button 
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))} 
                disabled={page === totalPages || loading}
                variant="outline"
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(CallLogsPage);
