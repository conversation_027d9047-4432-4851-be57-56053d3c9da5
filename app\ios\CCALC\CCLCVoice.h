//
//  CCLCVoice.h
//  CCALC
//
//  Created on 2025-06-22.
//  Voice Processing Module for CCALC
//

#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <AVFoundation/AVFoundation.h>

@interface CCLCVoice : RCTEventEmitter <RCTBridgeModule, AVAudioRecorderDelegate, AVAudioPlayerDelegate>

@property (nonatomic, strong) AVAudioRecorder *audioRecorder;
@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic, strong) AVAudioEngine *audioEngine;
@property (nonatomic, strong) AVAudioPlayerNode *playerNode;
@property (nonatomic, strong) AVAudioUnitTimePitch *pitchEffect;
@property (nonatomic, strong) AVAudioUnitDistortion *distortionEffect;
@property (nonatomic, strong) AVAudioUnitReverb *reverbEffect;

- (void)startRecording:(NSString *)outputPath;
- (void)stopRecording;
- (void)startPlayback:(NSString *)filePath withProfile:(NSDictionary *)profile;
- (void)stopPlayback;
- (void)applyVoiceMorphing:(NSDictionary *)profile;

@end
