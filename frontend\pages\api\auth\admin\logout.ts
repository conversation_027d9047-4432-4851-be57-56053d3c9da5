import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';
import getBackendUrl from '../../../../utils/getBackendUrl';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Revoke token on backend
      try {
        const backendUrl = getBackendUrl();
        console.log('Admin logout: Connecting to backend at:', backendUrl);
        
        await fetch(`${backendUrl}/api/auth/logout`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });
      } catch (fetchError) {
        console.error('Error revoking admin token on backend:', fetchError);
        // Continue with successful logout response even if backend call fails
      }
    }

    return res.status(200).json({ message: 'Admin logged out successfully' });
  } catch (error) {
    console.error('Admin logout error:', error);
    return res.status(500).json({ error: 'Admin logout failed' });
  }
}
