/**
 * Generates pagination data for API responses
 * 
 * @param page Current page number (1-based)
 * @param limit Items per page
 * @param total Total number of items
 * @returns Pagination data object
 */
export function generatePaginationData(page: number, limit: number, total: number) {
  const totalPages = Math.ceil(total / limit);
  
  return {
    currentPage: page,
    totalPages,
    totalItems: total,
    itemsPerPage: limit,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
    nextPage: page < totalPages ? page + 1 : null,
    previousPage: page > 1 ? page - 1 : null
  };
}

/**
 * Validates and sanitizes pagination parameters
 * 
 * @param queryPage Page from query params
 * @param queryLimit Limit from query params
 * @param defaultLimit Default limit to use
 * @param maxLimit Maximum allowed limit
 * @returns Sanitized pagination parameters
 */
export function sanitizePaginationParams(
  queryPage?: string | string[],
  queryLimit?: string | string[],
  defaultLimit = 10,
  maxLimit = 100
) {
  // Parse page parameter
  let page = 1;
  if (typeof queryPage === 'string') {
    const parsedPage = parseInt(queryPage);
    if (!isNaN(parsedPage) && parsedPage > 0) {
      page = parsedPage;
    }
  }
  
  // Parse limit parameter
  let limit = defaultLimit;
  if (typeof queryLimit === 'string') {
    const parsedLimit = parseInt(queryLimit);
    if (!isNaN(parsedLimit) && parsedLimit > 0) {
      limit = Math.min(parsedLimit, maxLimit);
    }
  }
  
  return { page, limit };
}
