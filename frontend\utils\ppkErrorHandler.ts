/**
 * Enhanced error handling and user guidance for PPK authentication
 */

export interface PPKError {
  code: string;
  message: string;
  userMessage: string;
  suggestions: string[];
  technical?: string;
}

/**
 * Create a user-friendly error with suggestions for common PPK issues
 */
export function createPPKError(
  code: string,
  technical: string,
  userMessage: string,
  suggestions: string[] = []
): PPKError {
  return {
    code,
    message: technical,
    userMessage,
    suggestions,
    technical
  };
}

/**
 * Analyze and categorize PPK authentication errors
 */
export function analyzePPKError(error: unknown): PPKError {
  const errorMessage = error instanceof Error ? error.message : String(error);
  const lowerMessage = errorMessage.toLowerCase();

  // Key import errors
  if (lowerMessage.includes('import') || lowerMessage.includes('key')) {
    if (lowerMessage.includes('passphrase')) {
      return createPPKError(
        'PASSPHRASE_REQUIRED',
        errorMessage,
        'This private key is encrypted and requires a passphrase.',
        [
          'Enter the passphrase for your private key',
          'If you forgot the passphrase, you may need to generate new keys',
          'Alternatively, use an unencrypted private key'
        ]
      );
    }

    if (lowerMessage.includes('algorithm') || lowerMessage.includes('format')) {
      return createPPKError(
        'UNSUPPORTED_KEY_FORMAT',
        errorMessage,
        'Your private key is in an unsupported format.',
        [
          'Ensure your key is in PEM format (starts with -----BEGIN PRIVATE KEY-----)',
          'Convert your key using: openssl rsa -in yourkey.pem -out converted.pem',
          'Use a standard RSA 2048-bit key for maximum compatibility',
          'Avoid keys in PKCS#1 format, use PKCS#8 instead'
        ]
      );
    }

    if (lowerMessage.includes('corrupted') || lowerMessage.includes('invalid')) {
      return createPPKError(
        'CORRUPTED_KEY',
        errorMessage,
        'Your private key file appears to be corrupted or invalid.',
        [
          'Re-download or re-export your private key',
          'Ensure the entire key file was copied correctly',
          'Check that the file hasn\'t been modified',
          'Generate a new key pair if the issue persists'
        ]
      );
    }

    return createPPKError(
      'KEY_IMPORT_FAILED',
      errorMessage,
      'Failed to import your private key.',
      [
        'Ensure your key is in PEM format',
        'Check that the key file is not corrupted',
        'Try using an unencrypted private key',
        'Contact support if the issue persists'
      ]
    );
  }

  // Signature errors
  if (lowerMessage.includes('sign') || lowerMessage.includes('signature')) {
    if (lowerMessage.includes('verification') || lowerMessage.includes('verify')) {
      return createPPKError(
        'SIGNATURE_VERIFICATION_FAILED',
        errorMessage,
        'The signature could not be verified.',
        [
          'Ensure you\'re using the correct private key',
          'Make sure your private key matches the public key on the server',
          'Try generating a new signature',
          'Contact your administrator if the issue persists'
        ]
      );
    }

    return createPPKError(
      'SIGNATURE_CREATION_FAILED',
      errorMessage,
      'Failed to create a digital signature.',
      [
        'Ensure your private key is valid and imported correctly',
        'Try reloading your private key',
        'Check your browser\'s security settings',
        'Use a different browser if the issue persists'
      ]
    );
  }

  // Network/API errors
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch') || lowerMessage.includes('401') || lowerMessage.includes('403')) {
    return createPPKError(
      'AUTHENTICATION_FAILED',
      errorMessage,
      'Authentication failed.',
      [
        'Check your internet connection',
        'Ensure your account has PPK authentication enabled',
        'Try logging in again',
        'Contact your administrator if you continue to have issues'
      ]
    );
  }

  // Challenge errors
  if (lowerMessage.includes('challenge')) {
    return createPPKError(
      'CHALLENGE_ERROR',
      errorMessage,
      'There was an issue with the authentication challenge.',
      [
        'Try refreshing the page and logging in again',
        'Check your internet connection',
        'Clear your browser cache and cookies',
        'Contact support if the issue continues'
      ]
    );
  }

  // Generic error
  return createPPKError(
    'UNKNOWN_ERROR',
    errorMessage,
    'An unexpected error occurred during PPK authentication.',
    [
      'Try refreshing the page and attempting again',
      'Check your internet connection',
      'Clear your browser cache and try again',
      'Contact technical support with the error details'
    ]
  );
}

/**
 * Format an error for display to the user
 */
export function formatErrorForUser(error: PPKError): string {
  let message = error.userMessage;
  
  if (error.suggestions.length > 0) {
    message += '\n\nSuggestions:\n';
    error.suggestions.forEach((suggestion, index) => {
      message += `${index + 1}. ${suggestion}\n`;
    });
  }

  return message.trim();
}

/**
 * Log error details for debugging while showing user-friendly message
 */
export function handlePPKError(error: unknown, context: string = 'PPK Authentication'): PPKError {
  const ppkError = analyzePPKError(error);
  
  // Log technical details for debugging
  console.error(`[${context}] PPK Error:`, {
    code: ppkError.code,
    technical: ppkError.technical,
    original: error
  });
  
  return ppkError;
}
