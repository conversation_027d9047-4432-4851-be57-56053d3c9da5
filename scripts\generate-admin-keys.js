#!/usr/bin/env node

/**
 * Enhanced Admin Key Generator for CCALC
 * 
 * This script generates RSA key pairs for admin PPK authentication
 * without requiring database connection or complex setup.
 * 
 * Features:
 * - Creates both encrypted and unencrypted private keys
 * - Formats public key for easy database insertion
 * - Creates JSON info file with key metadata
 * 
 * Usage:
 *   node scripts/generate-admin-keys.js [username] [email]
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Command line interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Ask a question and return a promise
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// Generate RSA key pair with optional encryption
function generateKeyPair(encrypted = false, passphrase = '') {
  const options = {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  };

  if (encrypted) {
    options.privateKeyEncoding.cipher = 'aes-256-cbc';
    options.privateKeyEncoding.passphrase = passphrase;
  }

  return crypto.generateKeyPairSync('rsa', options);
}

// Format public key for database storage
function formatPublicKeyForDB(publicKey) {
  return publicKey
    .replace(/-----BEGIN PUBLIC KEY-----/, '')
    .replace(/-----END PUBLIC KEY-----/, '')
    .replace(/\n/g, '')
    .trim();
}

// Main function
async function main() {
  try {
    // Get username and email from arguments or prompt
    let username = process.argv[2];
    let email = process.argv[3];
    
    if (!username) {
      username = await question('Enter admin username [admin]: ') || 'admin';
    }
    
    if (!email) {
      email = await question(`Enter email for ${username} [${username}@ccalc.local]: `) || `${username}@ccalc.local`;
    }

    // Determine if we should encrypt the private key
    const encryptResponse = await question('Generate encrypted private key? (y/N): ');
    const wantEncrypted = encryptResponse.toLowerCase() === 'y';
    
    // Get passphrase if encryption is requested
    let passphrase = '';
    if (wantEncrypted) {
      passphrase = await question('Enter passphrase for key encryption: ');
      const confirm = await question('Confirm passphrase: ');
      
      if (passphrase !== confirm) {
        throw new Error('Passphrases do not match');
      }
    }

    console.log(`\nGenerating key pair for admin: ${username} <${email}>`);
    
    // Ensure keys directory exists
    const keysDir = path.join(__dirname, '../keys');
    if (!fs.existsSync(keysDir)) {
      fs.mkdirSync(keysDir, { recursive: true });
    }

    // Generate timestamp for file names
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const prefix = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
    
    // Generate both encrypted and unencrypted keys
    const encryptedKeys = wantEncrypted ? generateKeyPair(true, passphrase) : null;
    const unencryptedKeys = generateKeyPair(false);
    
    // Define file paths
    const publicKeyPath = path.join(keysDir, `${prefix}_${username}_public_${timestamp}.pem`);
    const privateKeyPath = wantEncrypted 
      ? path.join(keysDir, `${prefix}_${username}_private_encrypted_${timestamp}.pem`)
      : path.join(keysDir, `${prefix}_${username}_private_${timestamp}.pem`);
    const unencryptedPrivateKeyPath = wantEncrypted 
      ? path.join(keysDir, `${prefix}_${username}_private_unencrypted_${timestamp}.pem`)
      : null;
    const formattedPublicKeyPath = path.join(keysDir, `${prefix}_${username}_public_formatted_${timestamp}.txt`);
    const infoPath = path.join(keysDir, `${prefix}_${username}_keys_info_${timestamp}.json`);
    
    // Write keys to files
    fs.writeFileSync(publicKeyPath, unencryptedKeys.publicKey);
    
    if (wantEncrypted) {
      fs.writeFileSync(privateKeyPath, encryptedKeys.privateKey);
      fs.writeFileSync(unencryptedPrivateKeyPath, unencryptedKeys.privateKey);
    } else {
      fs.writeFileSync(privateKeyPath, unencryptedKeys.privateKey);
    }
    
    // Format public key for database and save to file
    const formattedPublicKey = formatPublicKeyForDB(unencryptedKeys.publicKey);
    fs.writeFileSync(formattedPublicKeyPath, formattedPublicKey);
    
    // Create info file with key metadata
    const keyInfo = {
      username,
      email,
      generatedAt: new Date().toISOString(),
      encrypted: wantEncrypted,
      environment: process.env.NODE_ENV || 'development',
      publicKeyPath: path.relative(process.cwd(), publicKeyPath),
      privateKeyPath: path.relative(process.cwd(), privateKeyPath),
      formattedPublicKeyPath: path.relative(process.cwd(), formattedPublicKeyPath),
      unencryptedPrivateKeyPath: wantEncrypted ? path.relative(process.cwd(), unencryptedPrivateKeyPath) : null,
      note: 'The formatted public key should be added to the admin document in MongoDB'
    };
    
    fs.writeFileSync(infoPath, JSON.stringify(keyInfo, null, 2));
    
    // Output results
    console.log('\n✅ Admin keys generated successfully:');
    console.log(`Public key: ${publicKeyPath}`);
    console.log(`Private key: ${privateKeyPath}`);
    if (wantEncrypted) {
      console.log(`Unencrypted private key: ${unencryptedPrivateKeyPath}`);
    }
    console.log(`Formatted public key for DB: ${formattedPublicKeyPath}`);
    console.log(`Key info: ${infoPath}`);
    
    console.log('\n📋 Next steps:');
    console.log('1. Copy the contents of the formatted public key file to update in your database');
    console.log('2. To update the MongoDB admin document, use this formatted key for the ppkPublicKey field');
    console.log('3. Also set ppkEnabled: true and authMethod: "both" for the admin user');
    
    if (wantEncrypted) {
      console.log('\n⚠️ IMPORTANT: Store your passphrase securely!');
      console.log('For development, you can use the unencrypted private key for testing.');
    }
    
    console.log('\nThe admin can now log in using both password and PPK authentication.\n');

  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the script
main();
