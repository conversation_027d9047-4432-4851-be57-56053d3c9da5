/**
 * Mobile Chat API Endpoints
 * Superuser-only messaging system for mobile app
 */

import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import { MobileChat, MobileMessage, IMobileChat } from '../../models/MobileChat';
import mongoose from 'mongoose';

const router = Router();

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1, // One file per message
  },
  fileFilter: (req, file, cb) => {
    // Allow all standard media and document types for development
    const allowedTypes = [
      // Images
      'image/png',
      'image/jpeg', 
      'image/jpg',
      'image/heic',
      'image/webp',
      // Documents
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed',
      // Audio
      'audio/mpeg',
      'audio/wav',
      'audio/mp4',
      'audio/m4a',
      'audio/aac',
      // Video
      'video/mp4',
      'video/quicktime',
      'video/x-msvideo',
      'video/mov',
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} not supported`));
    }
  },
});

// Type for authenticated request user data
interface AuthenticatedUser {
  id: string;
  username: string;
  isSuperuser: boolean;
}

/**
 * Send Message
 * POST /api/chat/send
 * 
 * Send message from mobile app user to superuser (or vice versa)
 * Supports text messages and file attachments
 */
router.post('/send', upload.single('attachment'), authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { message, timestamp, isEncrypted } = req.body;
    const file = req.file;
    const userId = (req as any).user?.id;
    const username = (req as any).user?.username;
    const isSuperuser = (req as any).user?.isSuperuser;

    console.log('📱 Mobile chat send request:', { 
      userId, 
      username, 
      isSuperuser, 
      messageLength: message?.length,
      hasAttachment: !!file,
      fileName: file?.originalname,
      isEncrypted: !!isEncrypted
    });

    // Validate that either message or attachment is provided
    if ((!message || typeof message !== 'string' || message.trim().length === 0) && !file) {
      res.status(400).json({
        success: false,
        error: 'Message content or attachment is required'
      });
      return;
    }

    if (message && message.length > 1000) {
      res.status(400).json({
        success: false,
        error: 'Message too long (max 1000 characters)'
      });
      return;
    }

    // Additional file validation for screenshots
    if (file) {
      const fileName = file.originalname?.toLowerCase() || '';
      const screenshotPatterns = [
        'screenshot', 'screen shot', 'img_', 'image_', 'photo_', 'pic_', 'capture', 'snap_'
      ];
      
      if (screenshotPatterns.some(pattern => fileName.includes(pattern))) {
        res.status(400).json({
          success: false,
          error: 'Screenshots are not allowed'
        });
        return;
      }
    }

    // Get user details
    const user = await UserModel.findById(userId);
    if (!user || user.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    let recipientId: string;
    let chatId: string;

    if (user.isSuperuser) {
      // Superuser sending message - for now, we'll need to specify recipient
      // In a real implementation, this would be determined by context
      res.status(400).json({
        success: false,
        error: 'Superuser message sending requires recipient specification'
      });
      return;
    } else {
      // Regular user - can only send to superuser
      const superuser = await UserModel.findOne({ isSuperuser: true, status: 'active' });
      
      if (!superuser) {
        res.status(500).json({
          success: false,
          error: 'No active superuser found'
        });
        return;
      }
      
      recipientId = (superuser._id as any).toString();
      
      // Generate consistent chat ID for user-superuser conversation
      const participants = [userId, recipientId].sort();
      chatId = `chat_${participants[0]}_${participants[1]}`;
    }

    // Find or create chat
    let chat = await MobileChat.findOne({ chatId });
    
    if (!chat) {
      chat = await MobileChat.create({
        chatId,
        participants: [userId, recipientId],
        type: 'direct',
        metadata: {
          createdAt: new Date(),
          lastActivity: new Date(),
          messageCount: 0
        },
        isActive: true
      });
    }

    // Handle file attachment if present
    let attachmentInfo = null;
    if (file) {
      try {
        console.log('📎 Processing file attachment:', {
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          bufferLength: file.buffer?.length
        });

        // Validate buffer exists
        if (!file.buffer || file.buffer.length === 0) {
          throw new Error('File buffer is empty or missing');
        }

        // Save file to disk (in production, this would be encrypted and stored securely)
        const uploadsDir = path.join(process.cwd(), 'uploads', 'chat-attachments');
        await fs.mkdir(uploadsDir, { recursive: true });
        
        const fileExtension = path.extname(file.originalname);
        const fileName = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}${fileExtension}`;
        const filePath = path.join(uploadsDir, fileName);
        
        await fs.writeFile(filePath, file.buffer);
        
        // Verify file was written correctly
        const stats = await fs.stat(filePath);
        if (stats.size !== file.buffer.length) {
          throw new Error(`File write verification failed: expected ${file.buffer.length}, got ${stats.size}`);
        }
        
        attachmentInfo = {
          originalName: file.originalname,
          fileName: fileName,
          filePath: filePath,
          mimeType: file.mimetype,
          size: file.size,
          uploadedAt: new Date()
        };
        
        console.log('✅ File attachment saved successfully:', {
          fileName,
          originalName: file.originalname,
          size: file.size,
          savedSize: stats.size
        });
      } catch (fileError: unknown) {
        console.error('❌ File upload error:', fileError);
        const errorMessage = fileError instanceof Error ? fileError.message : 'Unknown error occurred';
        
        // Log detailed error info for debugging
        console.error('File upload error details:', {
          filename: file?.originalname,
          mimetype: file?.mimetype,
          size: file?.size,
          error: errorMessage,
          stack: fileError instanceof Error ? fileError.stack : undefined
        });
        
        res.status(500).json({
          success: false,
          error: `File upload failed: ${errorMessage}`
        });
        return;
      }
    }

    // Create message content with proper default text for media-only messages
    let messageText = message?.trim() || '';
    
    // Ensure there's always text content for media attachments to satisfy schema validation
    if ((!messageText || messageText === '') && file) {
      if (file.mimetype.startsWith('image/')) {
        messageText = '📷 Image';
      } else if (file.mimetype.startsWith('video/')) {
        messageText = '🎥 Video';
      } else if (file.mimetype.startsWith('audio/')) {
        messageText = '🎵 Audio';
      } else {
        messageText = `📎 ${file.originalname || 'File'}`;
      }
      console.log('📝 Added default text for media-only message:', messageText);
    }
    
    const messageContent: any = {
      type: file ? 'attachment' : 'text',
      text: messageText, // Use our guaranteed non-empty text
      timestamp: timestamp || Date.now()
    };

    if (attachmentInfo) {
      messageContent.attachment = attachmentInfo;
    }

    // Create message
    const messageDoc = await MobileMessage.create({
      messageId: new mongoose.Types.ObjectId().toString(),
      chatId: chat.chatId,
      senderId: userId,
      recipientId,
      content: messageContent,
      metadata: {
        sentAt: new Date(),
        platform: 'mobile',
        deviceType: 'iOS', // Could be dynamic based on request headers
        isEncrypted: !!isEncrypted
      },
      status: 'sent',
      isEncrypted: !!isEncrypted
    });    // Update chat metadata
    await MobileChat.findOneAndUpdate(
      { chatId: chat.chatId },
      {
        'metadata.lastActivity': new Date(),
        'metadata.lastMessage': messageDoc._id,
        $inc: { 'metadata.messageCount': 1 }
      }
    );

    // Update user's chatHistory
    if (user) {
      if (!user.chatHistory) user.chatHistory = [];
      let session = user.chatHistory.find(s => s.sessionId === chat.chatId);
      if (!session) {
        session = {
          sessionId: chat.chatId,
          startedAt: new Date(),
          endedAt: undefined,
          messageCount: 1,
          voiceCallDuration: 0,
          encryptionUsed: false // Update if E2E is implemented
        };
        user.chatHistory.push(session);
      } else {
        session.messageCount += 1;
        // Optionally update endedAt or voiceCallDuration if needed
      }
      await user.save();
    }

    // Log message for audit
    await AuditLogModel.create({
      logId: `mobile_msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      event: {
        type: 'api_access',
        action: 'MOBILE_MESSAGE_SENT',
        resource: '/api/chat/mobile/send',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        deviceFingerprint: req.headers['x-device-fingerprint'] as string,
        bleUUID: req.headers['x-ble-uuid'] as string,
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 10,
        authMethod: 'password',
        mfaUsed: false,
        suspiciousActivity: false
      },
      data: {
        metadata: {
          chatId: chat.chatId,
          messageId: messageDoc.messageId,
          recipientId,
          messageLength: message?.length || 0,
          hasAttachment: !!file,
          attachmentName: file?.originalname,
          attachmentSize: file?.size,
          platform: 'mobile'
        }
      },
      compliance: {
        category: 'data_access',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      }
    });

    console.log('✅ Message sent successfully:', messageDoc.messageId);

    const response: any = {
      success: true,
      message: {
        id: messageDoc.messageId,
        chatId: chat.chatId,
        timestamp: messageDoc.metadata.sentAt,
        status: 'sent',
        content: messageContent
      }
    };

    res.json(response);

  } catch (error: any) {
    console.error('Mobile chat send error:', error);
    
    // Handle mongoose validation errors specifically
    if (error.name === 'ValidationError') {
      console.error('Validation error details:', JSON.stringify(error.errors, null, 2));
      
      // Handle the content.text required error specifically
      if (error.errors?.['content.text']) {
        console.error('content.text validation error detected - this should not happen with our fixes');
        
        // Return a specific error for the client
        res.status(400).json({
          success: false,
          error: 'Message text is required',
          code: 'VALIDATION_ERROR',
          field: 'content.text'
        });
        return;
      }
    }
    
    await AuditLogModel.create({
      logId: `mobile_msg_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: (req as any).user?.id,
      event: {
        type: 'system_action',
        action: 'MOBILE_MESSAGE_SEND_ERROR',
        resource: '/api/chat/mobile/send',
        result: 'failure',
        severity: 'medium'
      },
      context: {
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.originalUrl,
        method: req.method
      },
      security: {
        riskScore: 30,
        suspiciousActivity: false
      },
      data: {
        errorDetails: error.message,
        stackTrace: error.stack?.substring(0, 500)
      },
      compliance: {
        category: 'system_admin',
        retention: 'medium',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to send message'
    });
  }
});

/**
 * Get Messages
 * GET /api/chat/messages
 * 
 * Get chat history for mobile app user
 */
router.get('/messages', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;
    const { limit = 50, offset = 0 } = req.query;

    console.log('📱 Mobile chat get messages:', { userId, limit, offset });

    // Get user details
    const user = await UserModel.findById(userId);
    if (!user || user.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    let chatQuery: any;

    if (user.isSuperuser) {
      // Superuser sees all chats
      chatQuery = { participants: userId };
    } else {
      // Regular user only sees chat with superuser
      const superuser = await UserModel.findOne({ isSuperuser: true, status: 'active' });
      
      if (!superuser) {
        res.json({
          success: true,
          messages: [],
          hasMore: false
        });
        return;
      }      const participants = [userId, (superuser._id as any).toString()].sort();
      const chatId = `chat_${participants[0]}_${participants[1]}`;
      chatQuery = { chatId };
    }    // Find chats for this user
    const chats = await MobileChat.find(chatQuery);
    const chatIds = chats.map((chat: IMobileChat) => chat.chatId);

    if (chatIds.length === 0) {
      res.json({
        success: true,
        messages: [],
        hasMore: false
      });
      return;
    }

    // Get messages from these chats
    const messages = await MobileMessage.find({
      chatId: { $in: chatIds }
    })
    .sort({ 'metadata.sentAt': -1 })
    .limit(Number(limit))
    .skip(Number(offset))
    .populate('senderId', 'username isSuperuser')
    .lean();

    // Format messages for mobile app
    const formattedMessages = messages.map((msg: any) => {
      const baseMessage: any = {
        id: msg.messageId,
        text: msg.content.text || '',
        sender: msg.senderId === userId ? 'user' : 'superuser',
        timestamp: new Date(msg.metadata.sentAt).getTime(),
        isDelivered: msg.status !== 'pending',
        isRead: msg.status === 'read',
        metadata: {
          isEncrypted: msg.isEncrypted || msg.metadata?.isEncrypted || false
        }
      };

      // Add attachment info if present
      if (msg.content.attachment) {
        baseMessage.attachment = {
          id: msg.content.attachment.fileName,
          name: msg.content.attachment.originalName,
          type: getFileCategory(msg.content.attachment.mimeType),
          size: msg.content.attachment.size,
          mimeType: msg.content.attachment.mimeType,
          uri: `/api/chat/mobile/attachment/${msg.content.attachment.fileName}`,
          isImage: msg.content.attachment.mimeType && msg.content.attachment.mimeType.startsWith('image/'),
        };
      }

      return baseMessage;
    });    // Check if there are more messages
    const totalCount = await MobileMessage.countDocuments({
      chatId: { $in: chatIds }
    });
    
    const hasMore = (Number(offset) + Number(limit)) < totalCount;

    console.log('✅ Retrieved messages:', { count: formattedMessages.length, hasMore });

    res.json({
      success: true,
      messages: formattedMessages.reverse(), // Reverse to show oldest first
      hasMore,
      totalCount
    });

  } catch (error: any) {
    console.error('Mobile chat get messages error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve messages'
    });
  }
});

/**
 * Get Chat Status
 * GET /api/chat/status
 * 
 * Get online status and connection info
 */
router.get('/status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;

    // Get user details
    const user = await UserModel.findById(userId);
    if (!user || user.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    if (user.isSuperuser) {
      // Superuser status
      const activeUsers = await UserModel.countDocuments({ 
        status: 'active', 
        isSuperuser: false 
      });

      res.json({
        success: true,
        status: {
          isOnline: true,
          userType: 'superuser',
          activeUsers,
          canReceiveMessages: true
        }
      });
    } else {
      // Regular user status
      const superuser = await UserModel.findOne({ 
        isSuperuser: true, 
        status: 'active' 
      });

      res.json({
        success: true,
        status: {
          isOnline: true,
          userType: 'user',
          superuserAvailable: !!superuser,
          canSendMessages: !!superuser
        }
      });
    }

  } catch (error: any) {
    console.error('Mobile chat status error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get chat status'
    });
  }
});

/**
 * Get Chat Users List
 * GET /api/chat/users
 * 
 * For regular users: returns only superuser
 * For superuser: returns all active users with their display names
 */
router.get('/users', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as any).user?.id;
    const username = (req as any).user?.username;

    console.log('📱 Mobile chat users request:', { userId, username });

    // Get current user details
    const currentUser = await UserModel.findById(userId);
    if (!currentUser || currentUser.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    let chatUsers: any[] = [];

    if (currentUser.isSuperuser) {
      // Superuser sees all active users with their display names
      const activeUsers = await UserModel.find({ 
        status: 'active', 
        isSuperuser: false 
      }).select('username profile createdAt');

      chatUsers = activeUsers.map(user => ({
        id: `chat_${user._id}`,
        userId: (user._id as any).toString(),
        username: user.username,
        displayName: user.profile?.displayName || user.username || 'Mobile User',
        userType: 'user',
        isOnline: true, // For now, assume all users are online
        lastMessageTime: user.createdAt?.getTime() || Date.now(),
        unreadCount: 0 // TODO: Implement actual unread count
      }));

      console.log('📱 Superuser chat list:', { count: chatUsers.length, users: chatUsers.map(u => u.displayName) });

    } else {
      // Regular user only sees superuser
      const superuser = await UserModel.findOne({ 
        isSuperuser: true, 
        status: 'active' 
      }).select('username profile');

      if (superuser) {
        chatUsers = [{
          id: 'chat_superuser',
          userId: (superuser._id as any).toString(),
          username: superuser.username,
          displayName: superuser.profile?.displayName || 'Administrator',
          userType: 'superuser',
          isOnline: true,
          lastMessageTime: Date.now() - 600000, // 10 minutes ago
          unreadCount: 0
        }];
      }

      console.log('📱 User chat list:', { superuserFound: !!superuser, displayName: superuser?.profile?.displayName });
    }

    res.json({
      success: true,
      users: chatUsers,
      currentUser: {
        username: currentUser.username,
        displayName: currentUser.profile?.displayName || currentUser.username,
        userType: currentUser.isSuperuser ? 'superuser' : 'user'
      }
    });

  } catch (error: any) {
    console.error('Mobile chat users error:', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get chat users'
    });
  }
});

/**
 * Download Attachment
 * GET /api/chat/mobile/attachment/:fileName
 * 
 * Download a chat attachment file
 */
router.get('/attachment/:fileName', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { fileName } = req.params;
    const userId = (req as any).user?.id;
    
    // Validate user
    const user = await UserModel.findById(userId);
    if (!user || user.status !== 'active') {
      res.status(401).json({
        success: false,
        error: 'User account not valid'
      });
      return;
    }

    // Find message with this attachment
    const message = await MobileMessage.findOne({
      'content.attachment.fileName': fileName,
      $or: [
        { senderId: userId },
        { recipientId: userId }
      ]
    });

    if (!message || !message.content.attachment) {
      res.status(404).json({
        success: false,
        error: 'Attachment not found or access denied'
      });
      return;
    }

    const filePath = message.content.attachment.filePath;
    
    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      res.status(404).json({
        success: false,
        error: 'File not found on server'
      });
      return;
    }

    // Set headers for file serving - different approach for images vs documents
    const attachment = message.content.attachment;
    
    if (attachment.mimeType.startsWith('image/')) {
      // For images, set headers for inline viewing
      res.setHeader('Content-Type', attachment.mimeType);
      res.setHeader('Content-Disposition', `inline; filename="${attachment.originalName}"`);
      res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
    } else {
      // For other files, set download headers
      res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`);
      res.setHeader('Content-Type', attachment.mimeType);
    }
    
    // Stream file to response
    const fileBuffer = await fs.readFile(filePath);
    res.send(fileBuffer);

  } catch (error: any) {
    console.error('Download attachment error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download attachment'
    });
  }
});

/**
 * Helper function to categorize file types
 */
function getFileCategory(mimeType: string): string {
  if (mimeType.startsWith('application/pdf')) return 'PDF';
  if (mimeType.startsWith('application/msword') || 
      mimeType.includes('wordprocessingml')) return 'Word Document';
  if (mimeType.startsWith('application/vnd.ms-excel') || 
      mimeType.includes('spreadsheetml')) return 'Excel';
  if (mimeType.startsWith('application/vnd.ms-powerpoint') || 
      mimeType.includes('presentationml')) return 'PowerPoint';
  if (mimeType.startsWith('text/')) return 'Text File';
  if (mimeType.startsWith('audio/')) return 'Audio';
  if (mimeType.startsWith('video/')) return 'Video';
  if (mimeType.includes('zip')) return 'Archive';
  
  return 'Document';
}

export default router;
