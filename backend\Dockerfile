# Secure, production-ready Dockerfile for Node.js backend (API only)
FROM node:20-alpine

WORKDIR /usr/src/app

# Install dependencies
COPY backend/package.json ./
RUN npm ci || npm install

# Copy shared directory first
COPY shared /usr/src/app/shared

# Copy all the backend application code
COPY backend .

# Fix the path-to-regexp issue with a completely silent approach
RUN cp ./node_modules/path-to-regexp/dist/index.js ./node_modules/path-to-regexp/dist/index.js.bak && \
    sed -i 's/throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`)/return `unnamed_param_${i}`/g' ./node_modules/path-to-regexp/dist/index.js

# Manually create TypeScript build
RUN npm run build || npx tsc

# Create necessary directories and set permissions
RUN mkdir -p dist/logs && \
    addgroup -S appgroup && \
    adduser -S appuser -G appgroup && \
    chown -R appuser:appgroup /usr/src/app

# Expose port
EXPOSE 3000

# Use non-root user for security
USER appuser

CMD ["node", "dist/server.js"]
