/**
 * Voice RTC Service with Real-time Voice Morphing
 * Handles secure voice calls with automatic voice modulation
 * Uses Expo-compatible APIs for SDK 53
 */

import { NativeModules, NativeEventEmitter, Alert } from 'react-native';

// Safe crypto import with fallback for Expo Go
let Crypto: any = null;
try {
  Crypto = require('expo-crypto');
} catch (error) {
  console.log('📱 expo-crypto not available, using fallback implementation');
  Crypto = {
    digestStringAsync: async (algorithm: string, data: string) => {
      // Simple fallback hash function
      let hash = 0;
      for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }
      return Math.abs(hash).toString(16);
    }
  };
}

// Import the native voice module - with fallback for development
const { CCLCVoice } = NativeModules;
let voiceEventEmitter: NativeEventEmitter | null = null;

try {
  voiceEventEmitter = CCLCVoice ? new NativeEventEmitter(CCLCVoice) : null;
} catch (error) {
  console.warn('VoiceService: Failed to initialize NativeEventEmitter, using fallback mode');
  voiceEventEmitter = null;
}

interface VoiceCall {
  id: string;
  recipientId: string;
  status: 'connecting' | 'connected' | 'ended' | 'failed';
  startTime: Date;
  endTime?: Date;
  recordingPath?: string;
  morphingProfile: string;
}

interface VoiceMorphingProfile {
  id: string;
  name: string;
  pitchShift: number;
  formantShift: number;
  noise: number;
  distortion: number;
  reverb: number;
}

export class VoiceService {
  private static instance: VoiceService;
  private activeCall: VoiceCall | null = null;
  private currentProfile: VoiceMorphingProfile;
  private recordingActive = false;
  private eventEmitter: NativeEventEmitter | null = null;

  // Default morphing profiles
  private morphingProfiles: VoiceMorphingProfile[] = [
    {
      id: 'superuser',
      name: 'Superuser Voice',
      pitchShift: -0.3,
      formantShift: -0.2,
      noise: 0.1,
      distortion: 0.15,
      reverb: 0.2
    },
    {
      id: 'agent',
      name: 'Agent Voice',
      pitchShift: 0.2,
      formantShift: 0.1,
      noise: 0.05,
      distortion: 0.1,
      reverb: 0.1
    },
    {
      id: 'secure',
      name: 'Secure Channel',
      pitchShift: -0.5,
      formantShift: -0.3,
      noise: 0.2,
      distortion: 0.3,
      reverb: 0.3
    }
  ];

  private constructor() {
    this.currentProfile = this.morphingProfiles[0]; // Default to superuser
    this.setupEventListeners();
  }

  static getInstance(): VoiceService {
    if (!VoiceService.instance) {
      VoiceService.instance = new VoiceService();
    }
    return VoiceService.instance;
  }

  private setupEventListeners() {
    // Listen to native module events
    if (!voiceEventEmitter) {
      console.warn('⚠️ Voice module not available - using fallback (Expo Go environment)');
      return;
    }

    voiceEventEmitter.addListener('onRecordingStarted', (data) => {
      console.log('🎤 Recording started:', data);
    });

    voiceEventEmitter.addListener('onRecordingStopped', (data) => {
      console.log('🎤 Recording stopped:', data);
      if (this.activeCall) {
        this.activeCall.recordingPath = data.recordingPath;
        this.uploadRecording(data.recordingPath);
      }
    });

    voiceEventEmitter.addListener('onPlaybackStarted', (data) => {
      console.log('🎤 Playback started:', data);
    });

    voiceEventEmitter.addListener('onPlaybackStopped', (data) => {
      console.log('🎤 Playback stopped:', data);
    });

    voiceEventEmitter.addListener('onVoiceMorphingApplied', (data) => {
      console.log('🎤 Voice morphing applied:', data);
    });
  }

  /**
   * Start voice call with automatic morphing
   */
  async startVoiceCall(recipientId: string, userRole: 'superuser' | 'agent'): Promise<VoiceCall> {
    try {
      console.log('🎤 Starting voice call with morphing...');

      // Set morphing profile based on user role
      this.currentProfile = userRole === 'superuser' 
        ? this.morphingProfiles[0] 
        : this.morphingProfiles[1];

      // Generate call ID
      const callId = this.generateCallId();

      // Initialize call object
      this.activeCall = {
        id: callId,
        recipientId,
        status: 'connecting',
        startTime: new Date(),
        morphingProfile: this.currentProfile.id
      };

      // In Expo Go, simulate the voice call functionality
      if (!CCLCVoice) {
        console.warn('⚠️ Voice module not available - simulating voice call (Expo Go environment)');
        this.activeCall.status = 'connected';
        return this.activeCall;
      }

      // Initialize WebRTC with morphing
      await this.initializeWebRTC();

      // Start recording for admin panel
      await this.startRecording(callId);

      // Apply voice morphing filters
      await this.applyVoiceMorphing(this.currentProfile);

      // Connect to recipient
      await this.connectToRecipient(recipientId);

      this.activeCall.status = 'connected';
      return this.activeCall;

    } catch (error) {
      console.error('❌ Voice call failed:', error);
      if (this.activeCall) {
        this.activeCall.status = 'failed';
      }
      throw error;
    }
  }

  /**
   * Initialize WebRTC with voice morphing pipeline
   */
  private async initializeWebRTC(): Promise<void> {
    try {
      // Request microphone permission
      const hasPermission = await this.requestMicrophonePermission();
      if (!hasPermission) {
        throw new Error('Microphone permission denied');
      }

      if (CCLCVoice) {
        // Initialize audio context with morphing chain
        await CCLCVoice.initializeAudioContext({
          sampleRate: 48000,
          bufferSize: 1024,
          morphingEnabled: true,
          recordingEnabled: true
        });
      }

      console.log('✅ WebRTC initialized with voice morphing');
    } catch (error) {
      console.error('❌ WebRTC initialization failed:', error);
      throw error;
    }
  }

  /**
   * Apply real-time voice morphing filters
   */
  private async applyVoiceMorphing(profile: VoiceMorphingProfile): Promise<void> {
    try {
      const morphingConfig = {
        pitchShift: profile.pitchShift,
        formantShift: profile.formantShift,
        noise: profile.noise,
        distortion: profile.distortion,
        reverb: profile.reverb,
        realTime: true
      };

      if (CCLCVoice) {
        await CCLCVoice.applyVoiceMorphing(morphingConfig);
      } else {
        console.log('🎤 Simulating voice morphing:', profile.name);
      }
      console.log('✅ Voice morphing applied:', profile.name);
    } catch (error) {
      console.error('❌ Voice morphing failed:', error);
      throw error;
    }
  }

  /**
   * Start recording for admin panel
   */
  private async startRecording(callId: string): Promise<void> {
    try {
      const timestamp = Date.now();
      const recordingPath = `call_${callId}_${timestamp}.m4a`;
      
      if (CCLCVoice) {
        await CCLCVoice.startRecording(recordingPath);
      } else {
        console.log('🎤 Simulating recording start:', recordingPath);
      }

      if (this.activeCall) {
        this.activeCall.recordingPath = recordingPath;
      }

      this.recordingActive = true;
      console.log('✅ Recording started:', recordingPath);
    } catch (error) {
      console.error('❌ Recording failed:', error);
    }
  }

  /**
   * End voice call and save recording
   */
  async endVoiceCall(): Promise<void> {
    try {
      if (!this.activeCall) {
        return;
      }

      console.log('🎤 Ending voice call...');

      // Stop recording
      if (this.recordingActive) {
        await this.stopRecording();
      }

      // Close WebRTC connection
      if (CCLCVoice) {
        await CCLCVoice.closeConnection();
      } else {
        console.log('🎤 Simulating connection close');
      }

      // Save call metadata to backend for admin panel
      await this.saveCallMetadata();

      // Update call status
      this.activeCall.status = 'ended';
      this.activeCall.endTime = new Date();

      console.log('✅ Voice call ended');
      this.activeCall = null;
    } catch (error) {
      console.error('❌ Error ending call:', error);
    }
  }

  /**
   * Stop recording and upload to backend
   */
  private async stopRecording(): Promise<void> {
    try {
      let recordingInfo;
      if (CCLCVoice) {
        recordingInfo = await CCLCVoice.stopRecording();
      } else {
        recordingInfo = {
          path: this.activeCall?.recordingPath || 'simulated_recording.m4a',
          duration: 30 // Simulate 30 second recording
        };
      }
      
      // Upload recording to backend for admin panel access
      await this.uploadRecording(recordingInfo);
      
      this.recordingActive = false;
      console.log('✅ Recording stopped and uploaded');
    } catch (error) {
      console.error('❌ Stop recording failed:', error);
    }
  }

  /**
   * Upload recording to backend
   */
  private async uploadRecording(recordingInfo: any): Promise<void> {
    try {
      console.log('📤 Simulating recording upload to admin panel...', {
        callId: this.activeCall?.id,
        duration: recordingInfo.duration,
        morphingProfile: this.currentProfile.id
      });

      // In Expo Go, we'll simulate the upload
      console.log('✅ Recording upload simulated (Expo Go environment)');
    } catch (error) {
      console.error('❌ Recording upload failed:', error);
    }
  }

  /**
   * Save call metadata for admin panel
   */
  private async saveCallMetadata(): Promise<void> {
    try {
      if (!this.activeCall) return;

      const metadata = {
        callId: this.activeCall.id,
        recipientId: this.activeCall.recipientId,
        startTime: this.activeCall.startTime,
        endTime: this.activeCall.endTime,
        duration: this.activeCall.endTime 
          ? (this.activeCall.endTime.getTime() - this.activeCall.startTime.getTime()) / 1000
          : 0,
        morphingProfile: this.activeCall.morphingProfile,
        recordingPath: this.activeCall.recordingPath,
        status: this.activeCall.status
      };

      console.log('💾 Simulating call metadata save:', metadata);
      console.log('✅ Call metadata save simulated (Expo Go environment)');
    } catch (error) {
      console.error('❌ Save metadata failed:', error);
    }
  }

  // Helper methods
  private generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async requestMicrophonePermission(): Promise<boolean> {
    try {
      if (CCLCVoice) {
        const permission = await CCLCVoice.requestMicrophonePermission();
        return permission === 'granted';
      } else {
        // In Expo Go, simulate permission grant
        console.log('🎤 Simulating microphone permission grant');
        return true;
      }
    } catch (error) {
      console.error('❌ Microphone permission failed:', error);
      return false;
    }
  }

  private async connectToRecipient(recipientId: string): Promise<void> {
    // WebRTC signaling implementation
    if (CCLCVoice) {
      await CCLCVoice.connectToRecipient(recipientId);
    } else {
      console.log('🔗 Simulating connection to recipient:', recipientId);
    }
  }

  private async decryptVoiceData(encryptedData: string): Promise<any> {
    // Decrypt voice data using Expo Crypto
    try {
      // In Expo Go, we'll simulate decryption
      console.log('🔐 Simulating voice data decryption (Expo Go environment)');
      return JSON.parse(encryptedData);
    } catch (error) {
      console.error('Voice data decryption error:', error);
      return null;
    }
  }

  private async applyDemorphing(voiceData: any): Promise<any> {
    // Apply reverse morphing
    if (CCLCVoice) {
      return await CCLCVoice.applyDemorphing(voiceData);
    } else {
      console.log('🎤 Simulating voice demorphing (Expo Go environment)');
      return voiceData;
    }
  }

  // Public getters
  get isCallActive(): boolean {
    return this.activeCall !== null && this.activeCall.status === 'connected';
  }

  get currentCall(): VoiceCall | null {
    return this.activeCall;
  }

  get availableProfiles(): VoiceMorphingProfile[] {
    return [...this.morphingProfiles];
  }
}

export default VoiceService;
export type { VoiceCall, VoiceMorphingProfile };
