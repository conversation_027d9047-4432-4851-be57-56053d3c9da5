/**
 * Verify PPK Authentication System
 * 
 * This script tests the PPK authentication flow by:
 * 1. Checking if the backend is running
 * 2. Testing the API routes for login init and complete
 * 3. Analyzing a test key file (if available)
 * 4. Providing diagnostics about the auth system
 */
import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import { analyzePEMKey } from '../frontend/utils/pemAnalyzer';

// Config
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000';
const TEST_ADMIN = {
  username: 'admin',
  password: 'test-password'
};

// Find a test key in the keys directory
function findTestKey() {
  const keysDir = path.join(__dirname, '..', 'keys');
  if (!fs.existsSync(keysDir)) {
    return null;
  }
  
  const files = fs.readdirSync(keysDir);
  const privateKeyFile = files.find(f => f.includes('private') && f.endsWith('.pem'));
  
  if (!privateKeyFile) {
    return null;
  }
  
  return path.join(keysDir, privateKeyFile);
}

// Test backend connectivity
async function testBackendConnection() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/health`);
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend is running:', data);
      return true;
    } else {
      console.log('❌ Backend returned an error:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Failed to connect to backend:', error.message);
    return false;
  }
}

// Test login init endpoint
async function testLoginInit() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/auth/admin/login/init`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: TEST_ADMIN.username })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Login init successful:', {
        requiresPPK: data.requiresPPK,
        hasChallenge: !!data.challenge
      });
      return data;
    } else {
      console.log('❌ Login init failed:', data);
      return null;
    }
  } catch (error) {
    console.log('❌ Failed to test login init:', error.message);
    return null;
  }
}

// Test key file
function testKeyFile() {
  const keyFile = findTestKey();
  if (!keyFile) {
    console.log('⚠️ No test key file found in keys directory');
    return null;
  }
  
  try {
    const keyContent = fs.readFileSync(keyFile, 'utf-8');
    const analysis = analyzePEMKey(keyContent);
    
    if (analysis.isValid) {
      console.log('✅ Test key is valid:', {
        file: path.basename(keyFile),
        type: analysis.keyType,
        isEncrypted: analysis.isEncrypted
      });
      return { path: keyFile, content: keyContent, analysis };
    } else {
      console.log('❌ Test key is invalid:', analysis.message);
      return null;
    }
  } catch (error) {
    console.log('❌ Failed to read test key:', error.message);
    return null;
  }
}

// Main function
async function main() {
  console.log('=== CCALC PPK Authentication System Verification ===');
  
  // Check backend
  const backendRunning = await testBackendConnection();
  
  // Test login init
  let loginInitData = null;
  if (backendRunning) {
    loginInitData = await testLoginInit();
  } else {
    console.log('⚠️ Skipping login init test because backend is not running');
  }
  
  // Test key file
  const keyData = testKeyFile();
  
  // System assessment
  console.log('\n=== System Assessment ===');
  
  if (!backendRunning) {
    console.log('⚠️ Backend is not running - start it with: node scripts/start-backend.js');
  }
  
  if (backendRunning && !loginInitData) {
    console.log('⚠️ Login initialization failed - check backend logs');
  }
  
  if (backendRunning && loginInitData && !loginInitData.requiresPPK) {
    console.log('⚠️ PPK authentication is not required - check backend auth config');
  }
  
  if (!keyData) {
    console.log('⚠️ No valid test key available - generate one with: node scripts/generate-admin-keys.js');
  }
  
  // Overall status
  console.log('\n=== Overall Status ===');
  
  if (backendRunning && loginInitData && loginInitData.requiresPPK && keyData) {
    console.log('✅ PPK authentication system appears correctly configured');
    console.log('→ Check the frontend implementation in components/PPKUpload.tsx');
    console.log('→ Test the login flow in the frontend application');
  } else {
    console.log('⚠️ PPK authentication system has configuration issues');
    console.log('→ Fix the identified issues and run this verification again');
  }
}

// Run the main function
main().catch(error => {
  console.error('Error during verification:', error);
  process.exit(1);
});
