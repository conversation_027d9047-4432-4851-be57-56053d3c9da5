import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import DataTable from '../../components/admin/DataTable';
import Button from '../../components/admin/Button';
import StatCard from '../../components/admin/StatCard';
import { apiClient } from '../../utils/axiosClient';
import withAdminAuth from '../../components/hoc/withAdminAuth';

interface VoiceCall {
  callId: string;
  callerId: string;
  recipientId: string;
  startTime: string;
  endTime?: string;
  duration: number;
  status: 'connecting' | 'connected' | 'ended' | 'failed' | 'terminated';
  morphingProfile: string;
  recordingPath?: string;
  callQuality: 'excellent' | 'good' | 'fair' | 'poor';
  flaggedForReview: boolean;
  adminNotes?: string;
  reviewedBy?: string;
}

interface VoiceAnalytics {
  totalCalls: number;
  completedCalls: number;
  failedCalls: number;
  averageDuration: number;
  totalDuration: number;
  recordingCount: number;
  flaggedCount: number;
}

export default withAdminAuth(function VoiceCalls() {
  const [calls, setCalls] = useState<VoiceCall[]>([]);
  const [analytics, setAnalytics] = useState<VoiceAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    flagged: '',
    dateFrom: '',
    dateTo: ''
  });
  const [playingCall, setPlayingCall] = useState<string | null>(null);

  useEffect(() => {
    fetchVoiceCalls();
  }, [currentPage, filters]);

  const fetchVoiceCalls = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await apiClient.backend.get(`/api/voice/admin/calls?${params}`);
      const data = response.data.data;

      setCalls(data.calls);
      setAnalytics(data.analytics);
      setTotalPages(data.pagination.pages);
      setError('');
    } catch (err: any) {
      console.error('Voice calls fetch error:', err);
      setError(err.response?.data?.error || 'Failed to load voice calls');
    } finally {
      setLoading(false);
    }
  };

  const playRecording = async (callId: string) => {
    try {
      setPlayingCall(callId);
      
      // Create audio element and play recording
      const audio = new Audio(`/api/voice/admin/recording/${callId}`);
      audio.controls = true;
      
      // Create modal to show audio player
      const audioModal = document.createElement('div');
      audioModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      audioModal.innerHTML = `
        <div class="bg-white p-6 rounded-lg max-w-md w-full mx-4">
          <h3 class="text-lg font-semibold mb-4">Voice Call Recording</h3>
          <p class="text-sm text-gray-600 mb-4">Call ID: ${callId}</p>
          <div class="mb-4">
            <audio controls class="w-full" autoplay>
              <source src="/api/voice/admin/recording/${callId}" type="audio/mpeg">
              Your browser does not support the audio element.
            </audio>
          </div>
          <div class="flex justify-end">
            <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" onclick="this.closest('.fixed').remove()">
              Close
            </button>
          </div>
        </div>
      `;
      
      document.body.appendChild(audioModal);
      
    } catch (error) {
      console.error('Play recording error:', error);
      alert('Failed to load recording');
    } finally {
      setPlayingCall(null);
    }
  };

  const flagCall = async (callId: string, flagged: boolean, notes?: string) => {
    try {
      await apiClient.backend.post(`/api/voice/admin/flag/${callId}`, {
        flagged,
        adminNotes: notes
      });

      // Refresh calls
      fetchVoiceCalls();
    } catch (error) {
      console.error('Flag call error:', error);
      alert('Failed to update call flag');
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'connected': return 'text-green-600';
      case 'ended': return 'text-blue-600';
      case 'failed': return 'text-red-600';
      case 'terminated': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const getQualityColor = (quality: string): string => {
    switch (quality) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'fair': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };
  const columns = [
    {
      header: 'Call ID',
      accessor: (call: VoiceCall) => (
        <div className="font-mono text-sm">
          {call.callId.slice(0, 12)}...
        </div>
      )
    },
    {
      header: 'Participants',
      accessor: (call: VoiceCall) => (
        <div className="text-sm">
          <div><strong>Caller:</strong> {call.callerId}</div>
          <div><strong>Recipient:</strong> {call.recipientId}</div>
        </div>
      )
    },
    {
      header: 'Call Details',
      accessor: (call: VoiceCall) => (
        <div className="text-sm">
          <div><strong>Started:</strong> {formatDateTime(call.startTime)}</div>
          {call.endTime && <div><strong>Ended:</strong> {formatDateTime(call.endTime)}</div>}
          <div><strong>Duration:</strong> {formatDuration(call.duration)}</div>
        </div>
      )
    },
    {
      header: 'Status & Quality',
      accessor: (call: VoiceCall) => (
        <div className="text-sm">
          <div className={`font-semibold ${getStatusColor(call.status)}`}>
            {call.status.toUpperCase()}
          </div>
          <div className={`${getQualityColor(call.callQuality)}`}>
            Quality: {call.callQuality}
          </div>
          <div className="text-gray-600">
            Profile: {call.morphingProfile}
          </div>
        </div>
      )
    },
    {
      header: 'Recording',
      accessor: (call: VoiceCall) => (
        <div className="space-y-2">
          {call.recordingPath ? (
            <Button
              size="sm"
              onClick={() => playRecording(call.callId)}
              disabled={playingCall === call.callId}
            >
              {playingCall === call.callId ? 'Loading...' : '🎵 Play'}
            </Button>
          ) : (
            <span className="text-gray-400">No recording</span>
          )}
        </div>
      )
    },
    {
      header: 'Admin Actions',
      accessor: (call: VoiceCall) => (
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant={call.flaggedForReview ? 'secondary' : 'danger'}
              onClick={() => flagCall(call.callId, !call.flaggedForReview)}
            >
              {call.flaggedForReview ? '✓ Flagged' : '🚩 Flag'}
            </Button>
          </div>
          {call.flaggedForReview && call.adminNotes && (
            <div className="text-xs text-gray-600 mt-1">
              Notes: {call.adminNotes.slice(0, 50)}...
            </div>
          )}
          {call.reviewedBy && (
            <div className="text-xs text-gray-500">
              Reviewed by: {call.reviewedBy}
            </div>
          )}
        </div>
      )
    }
  ];

  return (
    <AdminLayout>
      <Head>
        <title>Voice Calls & Recordings - CCALC Admin</title>
      </Head>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Voice Calls & Recordings</h1>
          <Button onClick={fetchVoiceCalls} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <StatCard
              title="Total Calls"
              value={analytics.totalCalls.toString()}
              icon={<span>📞</span>}
              color="#3B82F6"
              subtitle="All voice calls"
              trend={{ value: 0, isPositive: true }}
            />
            <StatCard
              title="Completed Calls"
              value={analytics.completedCalls.toString()}
              icon={<span>✅</span>}
              color="#10B981"
              subtitle={`${analytics.totalCalls > 0 ? Math.round((analytics.completedCalls / analytics.totalCalls) * 100) : 0}% success rate`}
              trend={{ value: Math.round((analytics.completedCalls / analytics.totalCalls) * 100), isPositive: true }}
            />
            <StatCard
              title="Total Duration"
              value={formatDuration(analytics.totalDuration)}
              icon={<span>⏱️</span>}
              color="#F59E0B"
              subtitle={`Avg: ${formatDuration(Math.round(analytics.averageDuration))}`}
              trend={{ value: 0, isPositive: true }}
            />
            <StatCard
              title="Recordings"
              value={analytics.recordingCount.toString()}
              icon={<span>🎵</span>}
              color={analytics.flaggedCount > 0 ? "#EF4444" : "#10B981"}
              subtitle={`${analytics.flaggedCount} flagged for review`}
              trend={{ value: analytics.flaggedCount, isPositive: analytics.flaggedCount === 0 }}
            />
          </div>
        )}

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-3">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">All Statuses</option>
                <option value="connected">Connected</option>
                <option value="ended">Ended</option>
                <option value="failed">Failed</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Flagged</label>
              <select
                value={filters.flagged}
                onChange={(e) => setFilters({...filters, flagged: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">All Calls</option>
                <option value="true">Flagged Only</option>
                <option value="false">Not Flagged</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
          </div>
        </div>

        {/* Voice Calls Table */}
        <div className="bg-white rounded-lg shadow">          <DataTable
            data={calls}
            columns={columns}
            keyExtractor={(call) => call.callId}
            isLoading={loading}
            emptyMessage="No voice calls found"
          />
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-4 py-4">
              <Button
                variant="secondary"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="secondary"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
});
