import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';

interface BuildFormData {
  version: string;
  platform: string;
  status: string;
  releaseDate: string;
  description: string;
  downloadUrl?: string;
  changelog?: string;
}

export default function BuildForm() {
  const [formData, setFormData] = useState<BuildFormData>({
    version: '',
    platform: 'android',
    status: 'development',
    releaseDate: new Date().toISOString().split('T')[0],
    description: '',
    downloadUrl: '',
    changelog: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEdit, setIsEdit] = useState(false);
  const router = useRouter();
  const { id } = router.query;

  useEffect(() => {
    // Check if admin is logged in
    const checkSession = async () => {
      try {
        const response = await fetch('/api/auth/admin/session', {
          method: 'GET',
        });

        if (!response.ok) {
          router.push('/');
          return;
        }
      } catch (err) {
        router.push('/');
      }
    };

    checkSession();

    // If ID exists, fetch build data for editing
    const fetchBuild = async () => {
      if (id && id !== 'new') {
        setIsEdit(true);
        setLoading(true);
        try {
          const response = await fetch(`/api/builds/${id}`, {
            method: 'GET',
          });

          if (!response.ok) {
            throw new Error('Failed to fetch build data');
          }

          const buildData = await response.json();
          // Format the date for the input field
          const releaseDate = new Date(buildData.releaseDate);
          const formattedDate = releaseDate.toISOString().split('T')[0];
          
          setFormData({
            version: buildData.version || '',
            platform: buildData.platform || 'android',
            status: buildData.status || 'development',
            releaseDate: formattedDate,
            description: buildData.description || '',
            downloadUrl: buildData.downloadUrl || '',
            changelog: buildData.changelog || ''
          });
        } catch (err: any) {
          setError(err.message || 'Failed to load build data');
        } finally {
          setLoading(false);
        }
      }
    };

    if (id) {
      fetchBuild();
    }
  }, [id, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate form
    if (formData.version.trim() === '') {
      setError('Version is required');
      return;
    }

    if (formData.description.trim() === '') {
      setError('Description is required');
      return;
    }

    setLoading(true);

    try {
      const url = isEdit ? `/api/builds/${id}` : '/api/builds';
      const method = isEdit ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save build');
      }

      // Navigate back to builds list
      router.push('/builds');
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving build');
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEdit) {
    return <div className="loading">Loading build data...</div>;
  }

  return (
    <div className="container">
      <Head>
        <title>{isEdit ? 'Edit Build' : 'Add New Build'} - CCALC Admin</title>
      </Head>

      <header>
        <h1>CCALC Admin Portal</h1>
        <nav>
          <ul>
            <li><a href="/dashboard">Dashboard</a></li>
            <li><a href="/users">Users</a></li>
            <li><a href="/builds" className="active">Builds</a></li>
            <li><a href="/controls">Controls</a></li>
          </ul>
        </nav>
      </header>

      <main>
        <div className="content-header">
          <h2>{isEdit ? 'Edit Build' : 'Add New Build'}</h2>
          <button className="secondary-button" onClick={() => router.push('/builds')}>Back to Builds</button>
        </div>

        {error && <div className="error-message">{error}</div>}

        <form onSubmit={handleSubmit} className="form">
          <div className="form-group">
            <label htmlFor="version">Version</label>
            <input
              type="text"
              id="version"
              name="version"
              value={formData.version}
              onChange={handleChange}
              placeholder="e.g., 1.0.0"
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="platform">Platform</label>
            <select
              id="platform"
              name="platform"
              value={formData.platform}
              onChange={handleChange}
            >
              <option value="android">Android</option>
              <option value="ios">iOS</option>
              <option value="desktop">Desktop</option>
              <option value="web">Web</option>
              <option value="universal">Universal</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="status">Status</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
            >
              <option value="development">Development</option>
              <option value="alpha">Alpha</option>
              <option value="beta">Beta</option>
              <option value="released">Released</option>
              <option value="deprecated">Deprecated</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="releaseDate">Release Date</label>
            <input
              type="date"
              id="releaseDate"
              name="releaseDate"
              value={formData.releaseDate}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="downloadUrl">Download URL (optional)</label>
            <input
              type="url"
              id="downloadUrl"
              name="downloadUrl"
              value={formData.downloadUrl}
              onChange={handleChange}
              placeholder="https://example.com/download/app.apk"
            />
          </div>

          <div className="form-group">
            <label htmlFor="changelog">Changelog (optional)</label>
            <textarea
              id="changelog"
              name="changelog"
              value={formData.changelog}
              onChange={handleChange}
              rows={5}
              placeholder="- Fixed bug in chat\n- Added new feature\n- Improved performance"
            />
          </div>

          <div className="form-actions">
            <button type="button" className="cancel-button" onClick={() => router.push('/builds')}>Cancel</button>
            <button type="submit" className="primary-button" disabled={loading}>
              {loading ? 'Saving...' : 'Save Build'}
            </button>
          </div>
        </form>
      </main>

      <style jsx>{`
        .container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          border-bottom: 1px solid #eee;
          padding-bottom: 10px;
        }
        nav ul {
          display: flex;
          list-style: none;
          padding: 0;
        }
        nav li {
          margin-left: 20px;
        }
        nav a {
          text-decoration: none;
          color: #333;
          font-weight: 500;
        }
        nav a.active {
          color: #0070f3;
          font-weight: bold;
        }
        .content-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }
        .form {
          background: #fff;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .form-group {
          margin-bottom: 20px;
        }
        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
        }
        .form-group input[type="text"],
        .form-group input[type="url"],
        .form-group input[type="date"],
        .form-group select,
        .form-group textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 16px;
        }
        .form-group textarea {
          resize: vertical;
          font-family: inherit;
        }
        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          margin-top: 30px;
        }
        .primary-button {
          background-color: #0070f3;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        .primary-button:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
        .secondary-button {
          background-color: #f0f0f0;
          color: #333;
          border: 1px solid #ddd;
          padding: 10px 15px;
          border-radius: 4px;
          cursor: pointer;
        }
        .cancel-button {
          background-color: #f0f0f0;
          color: #333;
          border: 1px solid #ddd;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        }
        .error-message {
          color: #ff4d4f;
          margin-bottom: 15px;
          padding: 10px;
          background-color: #fff1f0;
          border: 1px solid #ffccc7;
          border-radius: 4px;
        }
        .loading {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          font-size: 18px;
        }
      `}</style>
    </div>
  );
}
