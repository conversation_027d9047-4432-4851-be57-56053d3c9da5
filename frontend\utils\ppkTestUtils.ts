/**
 * Test utility for PPK Authentication
 * 
 * This file provides functions to test PPK authentication functionality
 * in the browser console for debugging purposes.
 */

import { importPrivateKey, signChallenge } from './ppkAuth';
import { analyzePEMKey } from './pemAnalyzer';
import { debugPemContent } from './ppkDebug';

// Expose testing functions to the window object for browser console access
declare global {
  interface Window {
    PPKTest: {
      analyzePEM: (pemContent: string) => any;
      debugPEM: (pemContent: string) => any;
      signChallenge: (challenge: string, pemContent: string, passphrase?: string) => Promise<any>;
      verifyUploadComponent: () => void;
    };
  }
}

/**
 * Initialize test utilities - call this in a useEffect in a dev-only component
 */
export function initPPKTestUtils() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  // Create test object
  window.PPKTest = {
    // Test PEM analysis
    analyzePEM: (pemContent: string) => {
      try {
        const result = analyzePEMKey(pemContent);
        console.log('PEM Analysis Result:', result);
        return result;
      } catch (error) {
        console.error('PEM Analysis Error:', error);
        return { error };
      }
    },

    // Debug PEM content
    debugPEM: (pemContent: string) => {
      try {
        const result = debugPemContent(pemContent);
        console.log('PEM Debug Result:', result);
        return result;
      } catch (error) {
        console.error('PEM Debug Error:', error);
        return { error };
      }
    },

    // Test challenge signing
    signChallenge: async (challenge: string, pemContent: string, passphrase?: string) => {
      try {
        console.log('Importing private key...');
        const privateKey = await importPrivateKey(pemContent, passphrase);
        
        console.log('Signing challenge:', challenge);
        const signature = await signChallenge(challenge, privateKey);
        
        console.log('Signature:', signature);
        return { success: true, signature };
      } catch (error) {
        console.error('Challenge Signing Error:', error);
        return { success: false, error };
      }
    },

    // Verify PPKUpload component is working
    verifyUploadComponent: () => {
      const ppkUploadElement = document.querySelector('.ppk-upload');
      
      if (!ppkUploadElement) {
        console.error('PPKUpload component not found in DOM');
        return { found: false };
      }
      
      const fileInput = ppkUploadElement.querySelector('input[type="file"]');
      const hasCorrectAcceptAttribute = fileInput?.getAttribute('accept') === '.pem,.ppk';
      
      console.log('PPKUpload Component Check:', {
        componentFound: !!ppkUploadElement,
        fileInputFound: !!fileInput,
        hasCorrectAcceptAttribute
      });
      
      return {
        found: true,
        fileInputFound: !!fileInput,
        hasCorrectAcceptAttribute
      };
    }
  };
  
  console.log('PPK Test utilities initialized. Access via window.PPKTest in the browser console.');
}

export default initPPKTestUtils;
