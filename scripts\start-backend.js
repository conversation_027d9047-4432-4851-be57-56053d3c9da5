/**
 * Simple utility script to set up the backend server for testing
 */
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const BACKEND_DIR = path.join(__dirname, '..', 'backend');
const SERVER_JS = path.join(BACKEND_DIR, 'dist', 'backend', 'server.js');
const LOG_FILE = path.join(BACKEND_DIR, 'logs', 'server-startup.log');
const PORT = process.env.PORT || 4000;

// Ensure log directory exists
if (!fs.existsSync(path.join(BACKEND_DIR, 'logs'))) {
  fs.mkdirSync(path.join(BACKEND_DIR, 'logs'), { recursive: true });
}

// Output streams
const logStream = fs.createWriteStream(LOG_FILE, { flags: 'a' });

// Start the server
console.log(`Starting backend server on port ${PORT}...`);
const server = spawn('node', [SERVER_JS], {
  cwd: BACKEND_DIR,
  env: {
    ...process.env,
    NODE_ENV: 'development',
    PORT: PORT,
    LOG_LEVEL: 'debug',
    ENABLE_AUTH_DEBUG: 'true'
  },
  stdio: ['ignore', 'pipe', 'pipe']
});

// Handle stdout
server.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(`[SERVER]: ${output.trim()}`);
  logStream.write(`[${new Date().toISOString()}] [STDOUT]: ${output}`);
});

// Handle stderr
server.stderr.on('data', (data) => {
  const output = data.toString();
  console.error(`[SERVER ERROR]: ${output.trim()}`);
  logStream.write(`[${new Date().toISOString()}] [STDERR]: ${output}`);
});

// Handle server exit
server.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
  logStream.end();
});

// Handle script termination
process.on('SIGINT', () => {
  console.log('Stopping server...');
  server.kill();
  process.exit(0);
});

console.log(`Backend server started (PID: ${server.pid})`);
console.log(`Logs are being written to: ${LOG_FILE}`);
console.log('Press Ctrl+C to stop the server');
