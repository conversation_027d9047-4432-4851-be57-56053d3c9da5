import React, { useEffect, useState } from 'react';
import <PERSON> from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import apiClient from '../../utils/apiClient'; // Corrected: Assuming this is your configured Axios client for frontend
import Button from '../../components/admin/Button';

interface Device {
  deviceId: string;
  userId: string;
  fingerprint: string;
  ble?: any;
  metadata?: any;
  security?: any;
  network?: any;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const DeviceIntegrityPage: React.FC = () => {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionStatus, setActionStatus] = useState<string | null>(null);

  const fetchDevices = async () => {
    setLoading(true);
    setError(null);
    setActionStatus(null);
    try {
      const res = await apiClient.get('/api/devices'); // Corrected: Removed .frontend
      setDevices(res.data || []);
    } catch (e: any) {
      setError(e.response?.data?.error || 'Failed to fetch devices. Please check network or API status.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDevices();
  }, []);

  const handleRevoke = async (deviceId: string) => {
    if (!confirm('Are you sure you want to revoke this device? This will deauthorize it immediately and cannot be undone.')) return;
    setActionStatus(`Revoking device ${deviceId}...`);
    setError(null);
    try {
      await apiClient.delete(`/api/devices?deviceId=${deviceId}`); // Corrected: Removed .frontend
      setActionStatus(`Device ${deviceId} revoked successfully.`);
      fetchDevices();
    } catch (e: any) {
      setActionStatus(null);
      setError(`Failed to revoke device ${deviceId}: ${e.response?.data?.error || e.message}`);
    }
  };

  const handleReset = async (deviceId: string) => {
    if (!confirm('Are you sure you want to reset the fingerprint for this device? This action may require the user to re-verify.')) return;
    setActionStatus(`Resetting fingerprint for ${deviceId}...`);
    setError(null);
    try {
      await apiClient.post(`/api/devices/fingerprint/${deviceId}`); // Corrected: Removed .frontend
      setActionStatus(`Device ${deviceId} fingerprint reset successfully.`);
      fetchDevices();
    } catch (e: any) {
      setActionStatus(null);
      setError(`Failed to reset fingerprint for ${deviceId}: ${e.response?.data?.error || e.message}`);
    }
  };

  return (
    <AdminLayout>
      <Head>
        <title>Device Integrity Management | CCALC Admin</title>
      </Head>
      <div className="container">
        <div className="page-header">
          <h1 className="page-title">Device Integrity Monitoring</h1>
          <p className="page-subtitle">Manage user-bound devices, fingerprints, and their operational status.</p>
        </div>

        {error && (
          <div className="alert alert-danger mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 inline-block" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /></svg>
            <span>{error}</span>
            <button onClick={() => setError(null)} className="alert-close" aria-label="Close">×</button>
          </div>
        )}
        {actionStatus && (
          <div className={`alert ${error ? 'alert-info' : 'alert-success'} mb-4`}>
             <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 inline-block" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /></svg>
            <span>{actionStatus}</span>
            <button onClick={() => setActionStatus(null)} className="alert-close" aria-label="Close">×</button>
          </div>
        )}

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Registered Devices</h3>
          </div>
          {loading ? (
            <div className="loading-placeholder">
              <svg className="animate-spin h-8 w-8 text-primary mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p>Loading devices...</p>
            </div>
          ) : devices.length === 0 && !error ? (
            <div className="empty-state">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
              <p>No devices are currently registered.</p>
              <Button onClick={fetchDevices} variant="outline" className="mt-2">Refresh List</Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table-auto w-full">
                <thead className="table-header">
                  <tr>
                    <th className="px-4 py-2 text-left">User ID</th>
                    <th className="px-4 py-2 text-left">Device ID</th>
                    <th className="px-4 py-2 text-left">Fingerprint (short)</th>
                    <th className="px-4 py-2 text-left">Status</th>
                    <th className="px-4 py-2 text-left">Registered On</th>
                    <th className="px-4 py-2 text-left">Last Update</th>
                    <th className="px-4 py-2 text-center">Actions</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {devices.map((d) => (
                    <tr key={d.deviceId} className="table-row">
                      <td className="border-t px-4 py-2">{d.userId}</td>
                      <td className="border-t px-4 py-2 font-mono text-xs">{d.deviceId}</td>
                      <td className="border-t px-4 py-2 font-mono text-xs">{d.fingerprint?.slice(0, 12)}...</td>
                      <td className="border-t px-4 py-2">
                        <span className={`badge ${d.isActive ? 'badge-success' : 'badge-danger'}`}>
                          {d.isActive ? 'Active' : 'Revoked'}
                        </span>
                      </td>
                      <td className="border-t px-4 py-2 text-sm">{new Date(d.createdAt).toLocaleString()}</td>
                      <td className="border-t px-4 py-2 text-sm">{new Date(d.updatedAt).toLocaleString()}</td>
                      <td className="border-t px-4 py-2 text-center whitespace-nowrap">
                        <Button 
                          variant="danger" 
                          size="sm" 
                          onClick={() => handleRevoke(d.deviceId)} 
                          disabled={!d.isActive || loading} 
                          className="mr-2"
                        >
                          Revoke
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleReset(d.deviceId)} 
                          disabled={!d.isActive || loading}
                        >
                          Reset Fingerprint
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(DeviceIntegrityPage);
