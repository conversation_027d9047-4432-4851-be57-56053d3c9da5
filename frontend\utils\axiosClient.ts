import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import tokenManager from './tokenManager';
import { csrfManager } from './csrfManager';
import { isBrowser } from './browserUtils';

// Create axios instances for different endpoints
export const frontendApi = axios.create({
  baseURL: '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

export const backendApi = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Setup CSRF protection for both instances (only in browser)
if (isBrowser()) {
  csrfManager.setupInterceptors(frontendApi);
  csrfManager.setupInterceptors(backendApi);
}

// Request interceptor to add auth token
const addAuthInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    (config) => {
      const token = tokenManager.getAnyToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

// Response interceptor for error handling
const addResponseInterceptor = (instance: AxiosInstance) => {
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    (error) => {
      if (error.response?.status === 401) {
        // Handle unauthorized access
        tokenManager.clearAllTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/?session=expired';
        }
      }
      return Promise.reject(error);
    }
  );
};

// Apply interceptors
addAuthInterceptor(frontendApi);
addAuthInterceptor(backendApi);
addResponseInterceptor(frontendApi);
addResponseInterceptor(backendApi);

// Utility functions for common API calls
export const apiClient = {
  // Frontend API calls
  frontend: {
    get: (url: string, config?: AxiosRequestConfig) => frontendApi.get(url, config),
    post: (url: string, data?: any, config?: AxiosRequestConfig) => frontendApi.post(url, data, config),
    put: (url: string, data?: any, config?: AxiosRequestConfig) => frontendApi.put(url, data, config),
    delete: (url: string, config?: AxiosRequestConfig) => frontendApi.delete(url, config),
  },
  
  // Backend API calls
  backend: {
    get: (url: string, config?: AxiosRequestConfig) => backendApi.get(url, config),
    post: (url: string, data?: any, config?: AxiosRequestConfig) => backendApi.post(url, data, config),
    put: (url: string, data?: any, config?: AxiosRequestConfig) => backendApi.put(url, data, config),
    delete: (url: string, config?: AxiosRequestConfig) => backendApi.delete(url, config),
  },
};

export default apiClient;
