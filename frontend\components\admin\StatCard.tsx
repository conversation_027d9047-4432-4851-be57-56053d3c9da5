import React from 'react';
import Icon from './Icon';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  color,
  subtitle,
  trend 
}) => {
  return (
    <div className="stat-card">
      <div className="stat-card__header">
        <div className="stat-card__title-area">
          <h3 className="stat-card__title">{title}</h3>
          {subtitle && <p className="stat-card__subtitle">{subtitle}</p>}
        </div>
        <div className="stat-card__icon" style={{ backgroundColor: `${color}15` }}>
          <div className="stat-card__icon-inner" style={{ color }}>
            {icon}
          </div>
        </div>
      </div>
      
      <div className="stat-card__value-area">
        <div className="stat-card__value">{value}</div>
        
        {trend && (
          <div className={`stat-card__trend ${trend.isPositive ? 'stat-card__trend--up' : 'stat-card__trend--down'}`}>
            <span className="stat-card__trend-arrow">
              {trend.isPositive ? (
                <Icon name="trend-up" size={16} />
              ) : (
                <Icon name="trend-down" size={16} />
              )}
            </span>
            <span className="stat-card__trend-value">{trend.value}%</span>
          </div>
        )}
      </div>
      
      <div className="stat-card__accent" style={{ backgroundColor: color }}></div>
      
      <style jsx>{`
        .stat-card {
          background: var(--color-card);
          border-radius: var(--radius);
          border: 1px solid var(--color-border);
          padding: var(--spacing-5);
          box-shadow: var(--shadow);
          position: relative;
          overflow: hidden;
          height: 100%;
          display: flex;
          flex-direction: column;
          transition: transform var(--transition), box-shadow var(--transition);
        }
        
        .stat-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }
        
        .stat-card__accent {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
        }
        
        .stat-card__header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-4);
        }
        
        .stat-card__title-area {
          flex: 1;
        }
        
        .stat-card__title {
          margin: 0 0 var(--spacing-1);
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--color-muted);
        }
        
        .stat-card__subtitle {
          margin: 0;
          font-size: 0.75rem;
          color: var(--color-muted);
        }
        
        .stat-card__icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }
        
        .stat-card__icon-inner {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .stat-card__value-area {
          display: flex;
          align-items: baseline;
          gap: var(--spacing-3);
          margin-top: auto;
        }
        
        .stat-card__value {
          font-size: 1.75rem;
          font-weight: 700;
          color: var(--color-text);
        }
        
        .stat-card__trend {
          display: flex;
          align-items: center;
          gap: var(--spacing-1);
          font-size: 0.875rem;
          font-weight: 500;
        }
        
        .stat-card__trend--up {
          color: var(--color-success);
        }
        
        .stat-card__trend--down {
          color: var(--color-error);
        }
        
        .stat-card__trend-arrow {
          display: flex;
          align-items: center;
        }
        
        @media (max-width: 768px) {
          .stat-card {
            padding: var(--spacing-4);
          }
          
          .stat-card__icon {
            width: 40px;
            height: 40px;
          }
          
          .stat-card__value {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
};

export default StatCard;
