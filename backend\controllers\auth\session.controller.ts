import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { generateCsrfToken } from '../../utils/csrf-protection';

/**
 * Session verification controller
 * Used to check if the current authentication token is valid
 * Relies on the auth middleware to validate the token
 */
export async function verifySessionController(req: Request, res: Response): Promise<void> {
  try {
    // If auth middleware passes, the user/admin object will be added to the request
    if (req.user) {
      res.status(200).json({
        success: true,
        valid: true,
        user: {
          id: req.user._id,
          username: req.user.username,
          displayName: req.user.profile?.displayName,
          type: 'user'
        }
      });
      return;
    } else if (req.admin) {
      res.status(200).json({
        success: true,
        valid: true,
        admin: {
          id: req.admin._id,
          username: req.admin.username,
          email: req.admin.email,
          role: req.admin.role,
          type: 'admin'
        }
      });
      return;
    }
    // Should never reach here if auth middleware is working correctly
    res.status(401).json({ error: 'Not authenticated', success: false, valid: false });
  } catch (error) {
    console.error('Session verification error:', error);
    res.status(500).json({ error: 'Internal server error', success: false, valid: false });
  }
}

/**
 * Admin session refresh controller
 * Generates a new JWT token for an authenticated admin
 */
export async function refreshAdminSessionController(req: Request, res: Response): Promise<void> {
  try {
    // Ensure admin is authenticated
    if (!req.admin) {
      res.status(401).json({
        success: false,
        error: 'Admin authentication required'
      });
      return;
    }

    // Generate new JWT token
    const tokenPayload = {
      type: 'admin',
      adminId: req.admin._id,
      username: req.admin.username,
      role: req.admin.role,
      email: req.admin.email    };

    const tokenOptions = {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      issuer: 'ccalc-backend',
      audience: 'ccalc-admin'
    };

    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET!,
      tokenOptions as jwt.SignOptions
    );

    // Generate new CSRF token
    const csrfToken = generateCsrfToken((req.admin as any)._id.toString());

    // Set new token in httpOnly cookie
    res.cookie('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/'
    });

    res.status(200).json({
      success: true,
      message: 'Session refreshed successfully',
      token: token,
      csrfToken: csrfToken,
      admin: {
        id: req.admin._id,
        username: req.admin.username,
        email: req.admin.email,
        role: req.admin.role,
        type: 'admin'
      }
    });

  } catch (error) {
    console.error('Admin session refresh error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to refresh session'
    });
  }
}

/**
 * User session refresh controller
 * Generates a new JWT token for an authenticated user
 */
export async function refreshUserSessionController(req: Request, res: Response): Promise<void> {
  try {
    // Ensure user is authenticated
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'User authentication required'
      });
      return;
    }    // Generate new JWT token
    const tokenPayload = {
      type: 'user',
      id: req.user._id,
      username: req.user.username,
      displayName: req.user.profile?.displayName
    };

    const tokenOptions = {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
      issuer: 'ccalc-backend',
      audience: 'ccalc-user'
    };

    const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET!,
      tokenOptions as jwt.SignOptions
    );

    // Generate new CSRF token
    const csrfToken = generateCsrfToken((req.user as any)._id.toString());

    // Set new token in httpOnly cookie
    res.cookie('user-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/'
    });

    res.status(200).json({
      success: true,
      message: 'Session refreshed successfully',
      token: token,
      csrfToken: csrfToken,
      user: {
        id: req.user._id,
        username: req.user.username,
        displayName: req.user.profile?.displayName,
        type: 'user'
      }
    });

  } catch (error) {
    console.error('User session refresh error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to refresh session'
    });
  }
}
