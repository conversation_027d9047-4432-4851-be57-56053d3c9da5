/**
 * Admin Login Diagnostic Script
 * This script helps verify admin login credentials in the CCALC application
 */
const fetch = require('node-fetch');
const bcrypt = require('bcrypt');
const mongoose = require('mongoose');
require('dotenv').config();

// Database connection
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || '*************************************************************';
    console.log('Connecting to MongoDB:', mongoUri);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Define Admin schema
const adminSchema = new mongoose.Schema({
  username: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  email: { type: String, required: true },
  role: { type: String, required: true, default: 'admin' },
  isActive: { type: Boolean, default: true },
  lastLogin: { type: Date },
  createdAt: { type: Date, default: Date.now },
  ppkEnabled: { type: Boolean, default: false },
  ppkPublicKey: { type: String },
  authMethod: { 
    type: String, 
    enum: ['password', 'ppk', 'both'], 
    default: 'password' 
  }
});

const Admin = mongoose.model('Admin', adminSchema);

// Check if admin exists
const checkAdmin = async (username) => {
  console.log(`Checking if admin user '${username}' exists...`);
  const admin = await Admin.findOne({ username });
  
  if (!admin) {
    console.log(`❌ Admin user '${username}' does not exist`);
    return null;
  }
  
  console.log(`✅ Found admin user: ${admin.username}`);
  console.log(`- Email: ${admin.email}`);
  console.log(`- Role: ${admin.role}`);
  console.log(`- Is Active: ${admin.isActive}`);
  console.log(`- Auth Method: ${admin.authMethod}`);
  console.log(`- PPK Enabled: ${admin.ppkEnabled}`);
  console.log(`- Last Login: ${admin.lastLogin || 'Never'}`);
  
  return admin;
};

// Verify password
const verifyPassword = async (admin, password) => {
  if (!admin || !password) {
    return false;
  }
  
  try {
    console.log('Verifying password...');
    const isMatch = await bcrypt.compare(password, admin.password);
    
    if (isMatch) {
      console.log('✅ Password is correct');
    } else {
      console.log('❌ Password is incorrect');
    }
    
    return isMatch;
  } catch (error) {
    console.error('Password verification error:', error);
    return false;
  }
};

// Create admin if not exists
const createAdmin = async (username, password, email) => {
  try {
    console.log(`Creating new admin user '${username}'...`);
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create admin
    const admin = new Admin({
      username,
      password: hashedPassword,
      email,
      role: 'admin',
      isActive: true,
      authMethod: 'password'
    });
    
    await admin.save();
    console.log(`✅ Created admin user: ${admin.username}`);
    return admin;
  } catch (error) {
    console.error('Admin creation error:', error);
    return null;
  }
};

// Main function
const main = async () => {
  try {
    const username = process.env.ADMIN_USERNAME || 'admin';
    const password = process.env.ADMIN_PASSWORD || 'admin123';
    const email = process.env.ADMIN_EMAIL || '<EMAIL>';
    
    await connectToDatabase();
    
    // Check if admin exists
    let admin = await checkAdmin(username);
    
    // Verify password if admin exists
    if (admin) {
      const isPasswordCorrect = await verifyPassword(admin, password);
      
      if (!isPasswordCorrect) {
        console.log('Do you want to reset the admin password? (y/n)');
        process.stdin.once('data', async (data) => {
          const input = data.toString().trim().toLowerCase();
          
          if (input === 'y' || input === 'yes') {
            // Update admin password
            const hashedPassword = await bcrypt.hash(password, 10);
            admin.password = hashedPassword;
            await admin.save();
            console.log(`✅ Updated password for admin user: ${admin.username}`);
          }
          
          mongoose.connection.close();
          process.exit(0);
        });
      } else {
        mongoose.connection.close();
      }
    } else {
      // Create admin if not exists
      console.log('Admin user not found. Do you want to create a new admin user? (y/n)');
      process.stdin.once('data', async (data) => {
        const input = data.toString().trim().toLowerCase();
        
        if (input === 'y' || input === 'yes') {
          await createAdmin(username, password, email);
        }
        
        mongoose.connection.close();
        process.exit(0);
      });
    }
  } catch (error) {
    console.error('Error:', error);
    mongoose.connection.close();
    process.exit(1);
  }
};

// Run main function
main().catch(console.error);
