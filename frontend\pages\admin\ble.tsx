import React, { useState, useEffect, FormEvent } from 'react';
import Head from 'next/head';
import withAdminAuth from '../../components/hoc/withAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import Button from '../../components/admin/Button';
import { apiClient } from '../../utils/axiosClient';
import { 
  BluetoothDevice, 
  BluetoothRemoteGATTServer, 
  BluetoothRemoteGATTService, 
  BluetoothRemoteGATTCharacteristic,
  BleDevice,
  BleConnectionOptions
} from '../../types/ble';
import { 
  CALCULATOR_SERVICE_UUID,
  CALCULATOR_KEY_CHARACTERISTIC_UUID,
  DEV_CALCULATOR_SERVICE_UUID,
  DEV_CALCULATOR_CHAR_UUID,
  CUSTOM_SERVICE_UUID,
  CUSTOM_CHAR_UUID,
  BLE_ERROR_MESSAGES,
  SCAN_CONFIGS
} from '../../constants/ble';
import { 
  isWebBluetoothSupported,
  validateBleEnvironment,
  parseBleError,
  detectDeviceType,
  retryWithBackoff
} from '../../utils/ble';interface BLEDevice {
  id: string;
  userId: string;
  name: string;
  uuid: string;
  adData: string;
}

const BLEManagementPage: React.FC = () => {
  const [devices, setDevices] = useState<BLEDevice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [form, setForm] = useState({ 
    userId: '', 
    name: '', 
    uuid: '', 
    adData: '', 
    serviceUUID: '', 
    characteristicUUID: '', 
    authData: '' 
  });
  const [success, setSuccess] = useState<string | null>(null);
  const [scanResult, setScanResult] = useState<BluetoothDevice | null>(null); // Store BluetoothDevice
  const [isScanning, setIsScanning] = useState(false);
  const [charValue, setCharValue] = useState('');
  const [charStatus, setCharStatus] = useState<string | null>(null);
  const [writeAuth, setWriteAuth] = useState('');
  const [bleSupported, setBleSupported] = useState(true);  const [connectedDevice, setConnectedDevice] = useState<BluetoothDevice | null>(null);
  const [gattServer, setGattServer] = useState<BluetoothRemoteGATTServer | null>(null);

  // Use standardized UUIDs from constants
  const SERVICE_UUID = process.env.NODE_ENV === 'development' ? DEV_CALCULATOR_SERVICE_UUID : CALCULATOR_SERVICE_UUID;
  const CHAR_UUID = process.env.NODE_ENV === 'development' ? DEV_CALCULATOR_CHAR_UUID : CALCULATOR_KEY_CHARACTERISTIC_UUID;

  useEffect(() => {
    // Use standardized BLE environment validation
    validateBleEnvironment().then(validation => {
      setBleSupported(validation.success);
      if (!validation.success) {
        setError(validation.error || BLE_ERROR_MESSAGES.NOT_SUPPORTED);
      }
    }).catch(() => {
      setBleSupported(false);
      setError(BLE_ERROR_MESSAGES.ENVIRONMENT_ERROR);
    });
    fetchDevices();

    // Cleanup GATT connection
    return () => {
        gattServer?.disconnect();
    }
  }, [gattServer]); // Removed fetchDevices from dependency array as it's stable

  const fetchDevices = async () => {
    setLoading(true);
    setError(null);    try {
      const res = await apiClient.frontend.get('/api/ble/registry');
      setDevices(res.data || []);
    } catch (e: any) {
      setError(`Failed to load BLE devices: ${e.response?.data?.error || e.message}`);
    } finally {
      setLoading(false);
    }
  };

  const encryptAES = async (text: string, keyB64: string): Promise<{ iv: string; content: string; tag: string }> => {
    const keyBytes = Uint8Array.from(atob(keyB64), c => c.charCodeAt(0));
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const cryptoKey = await window.crypto.subtle.importKey('raw', keyBytes, 'AES-GCM', false, ['encrypt']);
    const ciphertextBuffer = await window.crypto.subtle.encrypt({ name: 'AES-GCM', iv }, cryptoKey, new TextEncoder().encode(text));
    
    // Correctly convert Uint8Array to base64 string
    const uint8ArrayToBase64 = (arr: Uint8Array) => btoa(Array.from(arr, byte => String.fromCharCode(byte)).join(''));

    return {
      iv: uint8ArrayToBase64(iv),
      content: uint8ArrayToBase64(new Uint8Array(ciphertextBuffer)),
      tag: '' 
    };
  };

  const handleAddDevice = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!form.userId || !form.name || !form.uuid) {
      setError('User ID, Device Name, and UUID are required.');
      return;
    }
    let encryptedAuthData;
    if (form.authData) {
      try {
        const keyB64 = process.env.NEXT_PUBLIC_BLE_SECRET_KEY;
        if (!keyB64) throw new Error('BLE secret key (NEXT_PUBLIC_BLE_SECRET_KEY) is not configured.');
        encryptedAuthData = await encryptAES(form.authData, keyB64);
      } catch (err: any) {
        setError(`Encryption failed: ${err.message}`);
        return;
      }
    }    setLoading(true);
    try {
      await apiClient.frontend.post('/api/ble/registry', {
        ...form,
        serviceUUID: form.serviceUUID || CUSTOM_SERVICE_UUID,
        characteristicUUID: form.characteristicUUID || CUSTOM_CHAR_UUID,
        authData: encryptedAuthData ? JSON.stringify(encryptedAuthData) : '',
      });
      setSuccess('Device added successfully.');
      setShowAddForm(false);
      setForm({ userId: '', name: '', uuid: '', adData: '', serviceUUID: '', characteristicUUID: '', authData: '' });
      fetchDevices();
    } catch (err: any) {
      if (err.response?.data?.error?.includes('duplicate')) {
        setError('A device with this UUID is already registered for this user.');
      } else {
        setError(`Failed to add device: ${err.response?.data?.error || err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDevice = async (id: string) => {
    setError(null);
    setSuccess(null);
    if (confirm('Are you sure you want to delete this device? This action cannot be undone.')) {
      setLoading(true);      try {
        await apiClient.frontend.delete(`/api/ble/registry/${id}`);
        setSuccess('Device removed successfully.');
        fetchDevices(); 
      } catch (err: any) {
        setError(`Failed to remove device: ${err.response?.data?.error || err.message}`);
      } finally {
        setLoading(false);
      }
    }
  };
  const handleScanForDevice = async () => {
    setError(null);
    setCharStatus(null);
    setScanResult(null);
    setCharValue('');
    setConnectedDevice(null);
    gattServer?.disconnect();
    setGattServer(null);

    // Use standardized BLE support checking
    if (!isWebBluetoothSupported()) {
      setError(BLE_ERROR_MESSAGES.NOT_SUPPORTED);
      return;
    }

    setIsScanning(true);
    try {      // Use standardized scan configuration for calculator devices
      const scanOptions = {
        filters: [
          { services: [CALCULATOR_SERVICE_UUID] },
          { services: [DEV_CALCULATOR_SERVICE_UUID] },
        ],
        optionalServices: [
          form.serviceUUID || SERVICE_UUID,
          CALCULATOR_SERVICE_UUID,
          DEV_CALCULATOR_SERVICE_UUID
        ]
      };
      
      const device = await navigator.bluetooth.requestDevice(scanOptions);      setScanResult(device);
        // Use standardized device type detection
      const deviceInfo = detectDeviceType(device.name, []);
      console.log('Detected device type:', deviceInfo);
      
      // Determine the appropriate service UUID based on device type
      const defaultServiceUUID = deviceInfo === 'calculator' ? SERVICE_UUID : CALCULATOR_SERVICE_UUID;
      
      setForm(f => ({ 
        ...f, 
        name: device.name || f.name, 
        uuid: device.id || f.uuid,
        serviceUUID: f.serviceUUID || defaultServiceUUID
      }));
      setSuccess(`Found device: ${device.name || device.id}. Device type: ${deviceInfo}. You can now attempt to connect and interact.`);    } catch (err: any) {
      // Use standardized error parsing
      const parsedError = parseBleError(err);
      setError(parsedError);
    } finally {
      setIsScanning(false);
    }
  };
  
  const handleConnectAndReadWrite = async () => {
    if (!scanResult) {
      setError("No scanned device to connect to. Please scan first.");
      return;
    }
    setError(null);
    setCharStatus(null);
    setLoading(true);

    try {
      if (connectedDevice && connectedDevice.id === scanResult.id && gattServer?.connected) {
        setCharStatus("Already connected. Performing operations...");
      } else {
        gattServer?.disconnect(); // Disconnect previous if any
        
        // Use retry logic for connection
        const server = await retryWithBackoff(
          () => scanResult.gatt!.connect(),
          3,
          1000
        );
        
        if (!server) throw new Error("Could not connect to GATT server");
        setGattServer(server);
        setConnectedDevice(scanResult);        setCharStatus("Connected to device. Fetching service...");
      }
      
      // Try primary service first, then fallback to development service
      let service;
      let characteristic;
      
      try {
        service = await gattServer?.getPrimaryService(form.serviceUUID || SERVICE_UUID);
        if (!service) throw new Error("Primary service not found.");
        
        characteristic = await service.getCharacteristic(form.characteristicUUID || CHAR_UUID);
        if (!characteristic) throw new Error("Primary characteristic not found.");
        
        setCharStatus("Calculator service found. Fetching characteristic...");
      } catch (primaryError) {
        console.warn('Primary calculator service not found, trying development service:', primaryError);
        
        try {
          service = await gattServer?.getPrimaryService(DEV_CALCULATOR_SERVICE_UUID);
          if (!service) throw new Error("Development service not found.");
          
          characteristic = await service.getCharacteristic(DEV_CALCULATOR_CHAR_UUID);
          if (!characteristic) throw new Error("Development characteristic not found.");
          
          setCharStatus("Development service found. Fetching characteristic...");
        } catch (devError) {
          throw new Error(`Calculator services not available on this device. Primary: ${primaryError}. Dev: ${devError}`);
        }
      }
      
      setCharStatus("Characteristic found. Performing operations...");

      let currentCharacteristicValue = charValue;
      if (characteristic.properties.read) {
        const valueDataView = await characteristic.readValue();
        const decoder = new TextDecoder('utf-8');
        currentCharacteristicValue = decoder.decode(valueDataView);
        setCharValue(currentCharacteristicValue);
        setCharStatus('Read characteristic value: ' + currentCharacteristicValue + '. ');
      } else {
        setCharStatus(prev => (prev || '') + 'Characteristic not readable. ');
      }

      if (writeAuth && (characteristic.properties.write || characteristic.properties.writeWithoutResponse)) {
        const keyB64 = process.env.NEXT_PUBLIC_BLE_SECRET_KEY;
        if (!keyB64) throw new Error('BLE secret key for encryption is not configured.');
        const encryptedPayload = await encryptAES(writeAuth, keyB64);
        
        const encoder = new TextEncoder();
        const dataToWrite = encoder.encode(JSON.stringify(encryptedPayload));

        if (characteristic.properties.write) { 
            await characteristic.writeValue(dataToWrite); // Use standard writeValue
        } else if (characteristic.properties.writeWithoutResponse) {
            await characteristic.writeValueWithoutResponse(dataToWrite);
        } else {
            throw new Error("Characteristic does not support write operations.");
        }        setCharStatus(prev => (prev || '') + 'Wrote encrypted data successfully.');
        setWriteAuth(''); 
      } else if (writeAuth) {
        setCharStatus(prev => (prev || '') + 'Characteristic not writable or no data to write.');
      }

      setSuccess(`Interaction with ${scanResult.name || scanResult.id} complete.`);
    } catch (err: any) {
        // Use standardized error parsing
        const parsedError = parseBleError(err);
        setError(`BLE connect/interact failed: ${parsedError}`);
        
        // Attempt to disconnect on failure to clean up
        gattServer?.disconnect();
        setGattServer(null);
        setConnectedDevice(null);
    } finally {
        setLoading(false);
    }
  };
  return (
    <AdminLayout>
      <Head>
        <title>BLE Device Management | CCALC Admin</title>
        <meta name="description" content="Manage BLE device registry and scanner" />
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
        <div className="container mx-auto px-6 py-8">
          {/* Enhanced Header Section */}
          <div className="mb-8">
            <div className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl shadow-xl p-8">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                <div className="flex-1">
                  <div className="flex items-center mb-4">
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-3 mr-4 shadow-lg">
                      <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.71 7.71L12 2h-1v9.59l-2.29-2.3a1 1 0 00-1.42 1.42L10.59 14H2v1l5.71 5.71a1 1 0 001.42 0L12 18.83l2.87 2.88a1 1 0 001.42 0L22 15.71a1 1 0 000-1.42L17.71 7.71z"/>
                        <circle cx="12" cy="12" r="1.5"/>
                      </svg>
                    </div>
                    <div>
                      <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                        BLE Device Management
                      </h1>
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <svg className="w-4 h-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                        </svg>
                        Last updated: {new Date().toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 text-lg leading-relaxed max-w-4xl">
                    Configure and manage Bluetooth Low Energy devices for calculator communication. 
                    Scan for nearby devices, establish secure connections, and register them for user authentication and encrypted data transmission.
                  </p>
                </div>
                {!bleSupported && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4 max-w-md shadow-sm">
                    <div className="flex items-start">
                      <div className="flex-shrink-0">
                        <svg className="h-6 w-6 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Browser Not Supported</h3>
                        <p className="text-sm text-red-700 mt-1">
                          Web Bluetooth is not available in this browser or environment.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>          {!bleSupported && (
            <div className="mb-6 p-4 bg-amber-50/80 border border-amber-200 rounded-xl shadow-sm backdrop-blur-sm">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-semibold text-amber-800">Limited Functionality</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    Web Bluetooth API is not supported or available. BLE interaction features are disabled. Device registry will still function.
                  </p>
                </div>
              </div>
            </div>
          )}

          {error && (
            <div className="mb-6 p-4 bg-red-50/80 border border-red-200 rounded-xl shadow-sm backdrop-blur-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-semibold text-red-800">Error</h3>
                    <p className="text-sm text-red-700 mt-1">{error}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setError(null)} 
                  className="ml-4 text-red-400 hover:text-red-600 transition-colors duration-200 p-1 rounded-full hover:bg-red-100"
                  aria-label="Close"
                >
                  <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-6 p-4 bg-green-50/80 border border-green-200 rounded-xl shadow-sm backdrop-blur-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-semibold text-green-800">Success</h3>
                    <p className="text-sm text-green-700 mt-1">{success}</p>
                  </div>
                </div>
                <button 
                  onClick={() => setSuccess(null)} 
                  className="ml-4 text-green-400 hover:text-green-600 transition-colors duration-200 p-1 rounded-full hover:bg-green-100"
                  aria-label="Close"
                >
                  <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}          {/* Device Registry Section */}
          <div className="mb-8">
            <div className="bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl shadow-lg p-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex items-center">
                  <div className="bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg p-2 mr-4">
                    <svg className="w-6 h-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">Device Registry</h2>
                    <p className="text-sm text-gray-600">Manage and configure BLE devices for users</p>
                  </div>
                </div>
                <Button 
                  onClick={() => setShowAddForm(!showAddForm)} 
                  className={`${
                    showAddForm 
                      ? 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                  } text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105`}
                >
                  <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    {showAddForm ? 
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" /> : 
                      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                    }
                  </svg>
                  <span>{showAddForm ? 'Cancel' : 'Add New Device'}</span>
                </Button>
              </div>
            </div>
          </div>          {/* Add Device Form */}
          {showAddForm && (
            <div className="bg-white/90 backdrop-blur-sm border border-white/40 rounded-2xl shadow-2xl p-8 mb-8 transform transition-all duration-300 animate-in slide-in-from-top-2">
              <div className="flex items-center mb-8">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-3 mr-4 shadow-lg">
                  <svg className="w-7 h-7 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">Add New BLE Device</h3>
                  <p className="text-gray-600 mt-1">Configure a new Bluetooth Low Energy device for user access</p>
                </div>
              </div>
              <form onSubmit={handleAddDevice} className="space-y-8">
                <div className="grid lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <label htmlFor="userId" className="block text-sm font-semibold text-gray-700 mb-3">
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                          User ID *
                        </span>
                      </label>
                      <input 
                        id="userId" 
                        type="text" 
                        placeholder="Enter user ID" 
                        value={form.userId} 
                        onChange={e => setForm(f => ({ ...f, userId: e.target.value }))} 
                        required 
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                      />
                    </div>
                    <div>
                      <label htmlFor="deviceNameAdd" className="block text-sm font-semibold text-gray-700 mb-3">
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                          </svg>
                          Device Name *
                        </span>
                      </label>
                      <input 
                        id="deviceNameAdd" 
                        type="text" 
                        placeholder="e.g., John's Smart Band" 
                        value={form.name} 
                        onChange={e => setForm(f => ({ ...f, name: e.target.value }))} 
                        required 
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                      />
                    </div>
                    <div>
                      <label htmlFor="deviceUUIDAdd" className="block text-sm font-semibold text-gray-700 mb-3">
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-purple-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 0a1 1 0 100 2h.01a1 1 0 100-2H9zm2 0a1 1 0 100 2h.01a1 1 0 100-2H11z" clipRule="evenodd" />
                          </svg>
                          Device UUID/ID *
                        </span>
                      </label>
                      <input 
                        id="deviceUUIDAdd" 
                        type="text" 
                        placeholder="Device unique identifier" 
                        value={form.uuid} 
                        onChange={e => setForm(f => ({ ...f, uuid: e.target.value }))} 
                        required 
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                      />
                    </div>
                  </div>
                  <div className="space-y-6">
                    <div>
                      <label htmlFor="adData" className="block text-sm font-semibold text-gray-700 mb-3">
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-indigo-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                          </svg>
                          Advertisement Data
                          <span className="text-gray-400 ml-1">(Optional)</span>
                        </span>
                      </label>
                      <input 
                        id="adData" 
                        type="text" 
                        placeholder="Raw advertisement data string" 
                        value={form.adData} 
                        onChange={e => setForm(f => ({ ...f, adData: e.target.value }))} 
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                      />
                    </div>
                    <div>
                      <label htmlFor="serviceUUIDAdd" className="block text-sm font-semibold text-gray-700 mb-3">
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-cyan-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                          </svg>
                          Service UUID
                          <span className="text-gray-400 ml-1">(Optional)</span>
                        </span>
                      </label>
                      <input 
                        id="serviceUUIDAdd" 
                        type="text" 
                        placeholder={`Defaults to ${SERVICE_UUID.substring(0,8)}... (Calculator Service)`} 
                        value={form.serviceUUID} 
                        onChange={e => setForm(f => ({ ...f, serviceUUID: e.target.value }))} 
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                      />
                      <p className="text-xs text-gray-500 mt-2 bg-gray-50 p-2 rounded-lg">
                        <strong>Production:</strong> {CALCULATOR_SERVICE_UUID.substring(0,8)}... | <strong>Development:</strong> {DEV_CALCULATOR_SERVICE_UUID.substring(0,8)}...
                      </p>
                    </div>
                    <div>
                      <label htmlFor="characteristicUUIDAdd" className="block text-sm font-semibold text-gray-700 mb-3">
                        <span className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" clipRule="evenodd" />
                          </svg>
                          Characteristic UUID
                          <span className="text-gray-400 ml-1">(Optional)</span>
                        </span>
                      </label>
                      <input 
                        id="characteristicUUIDAdd" 
                        type="text" 
                        placeholder={`Defaults to ${CHAR_UUID.substring(0,8)}... (Key Characteristic)`} 
                        value={form.characteristicUUID} 
                        onChange={e => setForm(f => ({ ...f, characteristicUUID: e.target.value }))} 
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                      />
                      <p className="text-xs text-gray-500 mt-2 bg-gray-50 p-2 rounded-lg">
                        <strong>Production:</strong> {CALCULATOR_KEY_CHARACTERISTIC_UUID.substring(0,8)}... | <strong>Development:</strong> {DEV_CALCULATOR_CHAR_UUID.substring(0,8)}...
                      </p>
                    </div>
                  </div>
                </div>
                <div>
                  <label htmlFor="authDataAdd" className="block text-sm font-semibold text-gray-700 mb-3">
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                      Auth String for Encryption
                      <span className="text-gray-400 ml-1">(Optional)</span>
                    </span>
                  </label>
                  <input 
                    id="authDataAdd" 
                    type="text" 
                    placeholder="Data to encrypt for device auth/write" 
                    value={form.authData} 
                    onChange={e => setForm(f => ({ ...f, authData: e.target.value }))} 
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm shadow-sm"
                  />
                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start">
                      <svg className="w-4 h-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                      <p className="text-sm text-blue-700">
                        This data will be AES-GCM encrypted before being stored or used for characteristic writes.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <Button 
                    type="button" 
                    onClick={() => setShowAddForm(false)} 
                    disabled={loading}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium"
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={loading}
                    className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    {loading ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </span>
                    ) : (
                      'Save Device'
                    )}
                  </Button>
                </div>
              </form>
            </div>
          )}<div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
          <div className="flex items-center mb-6">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-2 mr-3">
              <svg className="w-6 h-6 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H4a2 2 0 01-2-2V5zm3.293 1.293a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 01-1.414-1.414L7.586 10 5.293 7.707a1 1 0 010-1.414zM11 12a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Device Interaction</h3>
              <p className="text-sm text-gray-600 mt-1">Scan for BLE devices and interact with them in real-time</p>
            </div>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-800">
                  <strong>Instructions:</strong> Ensure your BLE device is discoverable, then click "Scan for BLE Device" to detect it. 
                  You can override default Service/Characteristic UUIDs using the fields in the "Add New Device" form above.
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 mb-6">
            <Button 
              onClick={handleScanForDevice} 
              disabled={isScanning || !bleSupported}
              className={`${isScanning || !bleSupported ? 'bg-gray-400' : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'} text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 disabled:opacity-50 shadow-md hover:shadow-lg`}
            >
              <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
              </svg>
              <span>{isScanning ? 'Scanning...' : (scanResult ? 'Re-Scan Device' : 'Scan for BLE Device')}</span>
            </Button>
            {scanResult && (
               <Button 
                  onClick={handleConnectAndReadWrite}
                  disabled={loading || !scanResult?.id || !bleSupported}
                  className={`${loading || !bleSupported ? 'bg-gray-400' : 'bg-green-600 hover:bg-green-700'} text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 disabled:opacity-50 shadow-md hover:shadow-lg`}
               >
                  <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                     <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{loading ? 'Connecting...' : (connectedDevice && connectedDevice.id === scanResult.id ? 'Interact Again' : 'Connect & Interact')}</span>
               </Button>
          )}
        </div>        {scanResult && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="bg-blue-600 rounded-full p-2 mr-3">
                  <svg className="w-5 h-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm4 14a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-bold text-lg text-blue-900">
                    {scanResult.name || "Unnamed Device"}
                  </h4>
                  <p className="text-sm text-blue-700 font-mono bg-blue-100 px-2 py-1 rounded inline-block">
                    ID: {scanResult.id}
                  </p>
                </div>
              </div>
              {connectedDevice && connectedDevice.id === scanResult.id && gattServer?.connected && (
                <div className="flex items-center bg-green-100 text-green-800 px-3 py-2 rounded-lg">
                  <svg className="w-4 h-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-semibold">Connected</span>
                </div>
              )}
            </div>
            
            {charValue && (
              <div className="mb-4 p-3 bg-white border border-gray-200 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 mb-2">Last Read Value:</label>
                <code className="block bg-gray-50 text-gray-800 px-3 py-2 rounded border font-mono text-sm">
                  {charValue}
                </code>
              </div>
            )}
            
            {charStatus && (
              <div className={`mb-4 p-4 rounded-lg border ${
                charStatus.toLowerCase().includes('success') || charStatus.toLowerCase().includes('complete') ? 
                  'bg-green-50 text-green-800 border-green-200' : 
                (charStatus.toLowerCase().includes('fail') || charStatus.toLowerCase().includes('error')) ? 
                  'bg-red-50 text-red-800 border-red-200' : 
                  'bg-blue-50 text-blue-800 border-blue-200'
              }`}>
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3 mt-0.5">
                    {charStatus.toLowerCase().includes('success') || charStatus.toLowerCase().includes('complete') ? (
                      <svg className="w-5 h-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    ) : charStatus.toLowerCase().includes('fail') || charStatus.toLowerCase().includes('error') ? (
                      <svg className="w-5 h-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium">Device Status</p>
                    <p className="text-sm mt-1">{charStatus}</p>
                  </div>
                </div>
              </div>
            )}
            
            <div className="border-t border-blue-200 pt-4">
              <label htmlFor="writeData" className="block text-sm font-medium text-gray-700 mb-3">
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                  </svg>
                  Data to Write (Will be encrypted)
                </div>
              </label>
              <div className="space-y-3">
                <input 
                  id="writeData" 
                  type="text" 
                  placeholder="Enter string to encrypt and write to device characteristic" 
                  value={writeAuth} 
                  onChange={e => setWriteAuth(e.target.value)} 
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400"
                  disabled={!scanResult?.id || loading || !bleSupported}
                />
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-start">
                    <svg className="w-4 h-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <p className="text-sm text-yellow-800">
                      Data will be AES-GCM encrypted and sent when you click "{connectedDevice && connectedDevice.id === scanResult.id ? 'Interact Again' : 'Connect & Interact'}" above.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        </div>

        <div className="bg-white/90 backdrop-blur-sm border border-white/20 rounded-xl shadow-2xl p-6">
          <h3 className="text-2xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Registered BLE Devices</h3>
          {loading && devices.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <div className="animate-spin h-8 w-8 border-4 border-blue-200 border-t-blue-600 rounded-full mx-auto mb-4"></div>
              <p>Loading devices...</p>
            </div>
          ) : !loading && devices.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <svg className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-lg font-medium">No BLE devices registered yet</p>
              <p className="text-sm mt-1">Use the "Add New BLE Device" button to add one.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UUID</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ad Data</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {devices.map(d => (
                    <tr key={d.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{d.userId}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{d.name}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-mono">{d.uuid}</code>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-mono">{d.adData || 'N/A'}</code>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Button 
                          onClick={() => handleDeleteDevice(d.id)} 
                          disabled={loading}
                          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 disabled:opacity-50"
                        >
                          Delete
                        </Button>
                      </td>
                    </tr>
                  ))}                </tbody>
              </table>
            </div>
          )}
        </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(BLEManagementPage);
