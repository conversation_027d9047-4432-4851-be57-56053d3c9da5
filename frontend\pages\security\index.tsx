import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import DashboardCard from '../../components/admin/DashboardCard';
import DataTable from '../../components/admin/DataTable';
import Button from '../../components/admin/Button';
import Icon from '../../components/admin/Icon';
import { apiClient } from '../../utils/axiosClient';

interface SecurityEvent {
  _id: string;
  eventType: string;
  timestamp: Date;
  ipAddress: string;
  userId?: string;
  details: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface SecurityStats {
  totalEvents: number;
  criticalEvents: number;
  recentEvents: SecurityEvent[];
  failedLogins: number;
  suspiciousActivities: number;
}

export default function Security() {
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [auditInProgress, setAuditInProgress] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchSecurityData = async () => {
      try {
        await apiClient.frontend.get('/api/auth/admin/session');
        const response = await apiClient.frontend.get('/api/security/stats');
        setSecurityStats(response.data);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'An error occurred while fetching security data.');
        if (err.response?.status === 401) {
          router.push('/admin/login'); // Corrected redirect to admin login
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSecurityData();
  }, [router]);

  const handleRunAudit = async () => {
    if (auditInProgress) return;
    
    try {
      setAuditInProgress(true);
      setError('');
      
      await apiClient.frontend.post('/api/security/audit');
      
      const response = await apiClient.frontend.get('/api/security/stats');
      setSecurityStats(response.data);
      
      // Consider using a more integrated notification system if available
      alert('Security audit completed successfully.'); 
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'Security audit failed.');
    } finally {
      setAuditInProgress(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="loading-container">
          <div className="loading-spinner animate-spin"></div> {/* Added animate-spin */}
          <p className="text-muted">Loading security data...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Security Dashboard - CCALC Admin</title>
      </Head>
      
      <div className="page-header md:page-header-col mb-6"> {/* Added mb-6 and responsive class */}
        <h1 className="page-title">Security Dashboard</h1> {/* Changed to h1 for semantic correctness */}
        <Button 
          variant="primary"
          onClick={handleRunAudit}
          disabled={auditInProgress}
          isLoading={auditInProgress}                leftIcon={<Icon name="shield" size={16} />} /* Changed icon name */
        >
          Run Security Audit
        </Button>
      </div>

      {error && (
        <div className="alert alert-error mb-4"> {/* Added mb-4 */}
          <Icon name="alert-circle" size={20} /> {/* Adjusted size for consistency */}
          <span>{error}</span>
          <button onClick={() => setError('')} className="alert-close">                <Icon name="close" size={18}/>
          </button>
        </div>
      )}

      {securityStats && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8"> {/* Responsive grid and mb-8 */}
            {/* Total Events Stat Item */}
            <div className="stat-item">              <div className="stat-icon bg-accent-10 text-accent">
                <Icon name="info" size={20} /> {/* Changed icon */}
              </div>
              <div className="stat-content">
                <div className="stat-value">{securityStats.totalEvents}</div>
                <div className="stat-label">Total Events</div>
              </div>
            </div>
            
            {/* Critical Events Stat Item */}
            <div className="stat-item stat-item--critical">              <div className="stat-icon">
                <Icon name="warning-triangle" size={20} /> {/* Changed icon */}
              </div>
              <div className="stat-content">
                <div className="stat-value">{securityStats.criticalEvents}</div>
                <div className="stat-label">Critical Events</div>
              </div>
            </div>
            
            {/* Failed Logins Stat Item */}
            <div className="stat-item stat-item--warning">              <div className="stat-icon">
                <Icon name="lock" size={20} /> {/* Changed icon */}
              </div>
              <div className="stat-content">
                <div className="stat-value">{securityStats.failedLogins}</div>
                <div className="stat-label">Failed Logins</div>
              </div>
            </div>

            {/* Suspicious Activities Stat Item */}
            <div className="stat-item stat-item--suspicious">              <div className="stat-icon">
                <Icon name="view" size={20} /> {/* Changed icon */}
              </div>
              <div className="stat-content">
                <div className="stat-value">{securityStats?.suspiciousActivities || 0}</div>
                <div className="stat-label">Suspicious Activities</div>
              </div>
            </div>
          </div>

          <DashboardCard title="Recent Security Events">
            <DataTable
              columns={[
                { header: 'Type', accessor: 'eventType', className: 'font-medium' },
                { 
                  header: 'Timestamp', 
                  accessor: (event: SecurityEvent) => new Date(event.timestamp).toLocaleString(),
                  className: 'text-sm text-muted'
                },
                { header: 'IP Address', accessor: 'ipAddress', className: 'text-sm' },
                { 
                  header: 'User ID', 
                  accessor: (event: SecurityEvent) => event.userId || <span className="text-muted">N/A</span>, 
                  className: 'text-sm'
                },
                { 
                  header: 'Severity', 
                  accessor: (event: SecurityEvent) => (
                    <span className={`badge badge--${event.severity.toLowerCase()}`}>
                      {event.severity}
                    </span>
                  ),
                  className: 'text-center'
                },
                { header: 'Details', accessor: 'details', className: 'text-sm' }
              ]}
              data={securityStats.recentEvents}
              keyExtractor={(event: SecurityEvent) => event._id}
              emptyMessage="No recent security events found."
            />
          </DashboardCard>
        </>
      )}
    </AdminLayout>
  );
}
