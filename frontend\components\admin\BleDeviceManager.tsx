import React, { useState, useEffect } from 'react';
import CustomBleDeviceScanner from './CustomBleDeviceScanner';
import { 
  BleDevice,
  BleServiceInfo,
  BleCharacteristicInfo,
  BleDeviceManagerProps,
  DiscoveredBleDevice
} from '../../types/ble';
import { 
  CONNECTION_CONFIG,
  BLE_ERROR_MESSAGES,
  CALCULATOR_SERVICE_UUID,
  DEV_CALCULATOR_SERVICE_UUID
} from '../../constants/ble';
import { 
  isWebBluetoothSupported,
  validateBleEnvironment,
  parseBleError,
  detectDeviceType,
  retryWithBackoff
} from '../../utils/ble';

const BleDeviceManager: React.FC<BleDeviceManagerProps> = ({
  onDeviceSelected,
  onDeviceDisconnected,
  onError,
  selectedServiceUUIDs = [],
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'permitted' | 'scan' | 'details'>('permitted');
  const [selectedDevice, setSelectedDevice] = useState<BleDevice | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [permittedDevices, setPermittedDevices] = useState<BleDevice[]>([]);
  const [newDevice, setNewDevice] = useState<BluetoothDevice | null>(null);

  // Load permitted devices on component mount
  useEffect(() => {
    loadPermittedDevices();
  }, []);

  // Handle new device selection
  useEffect(() => {
    if (newDevice) {
      handleNewDeviceConnected(newDevice);
    }
  }, [newDevice]);

  const loadPermittedDevices = async () => {
    try {
      await validateBleEnvironment();
      
      // Try to get devices from browser's Web Bluetooth API (if available)
      if (navigator.bluetooth && 'getDevices' in navigator.bluetooth) {
        try {
          const devices = await (navigator.bluetooth as any).getDevices();
          const deviceList: BleDevice[] = devices.map((device: BluetoothDevice) => ({
            id: device.id,
            name: device.name,
            connected: device.gatt?.connected || false,
            gatt: device.gatt,
            lastConnected: undefined
          }));
          
          setPermittedDevices(deviceList);
        } catch (error) {
          console.warn('getDevices not available, starting with empty device list');
          setPermittedDevices([]);
        }
      } else {
        setPermittedDevices([]);
      }
    } catch (error) {
      console.warn('Web Bluetooth not available for device loading:', error);
      // Don't show error to user for device loading - just start with empty list
      setPermittedDevices([]);
    }
  };

  const handleNewDeviceConnected = async (device: BluetoothDevice) => {
    try {
      setIsConnecting(true);
      
      const bleDevice: BleDevice = {
        id: device.id,
        name: device.name,
        connected: device.gatt?.connected || false,
        gatt: device.gatt,
        lastConnected: new Date()
      };

      // Connect if not already connected
      if (!device.gatt?.connected) {
        await device.gatt?.connect();
        bleDevice.connected = true;
      }

      // Scan device services
      const services = await scanDeviceServices(device);
      bleDevice.services = services;

      setSelectedDevice(bleDevice);
      onDeviceSelected(bleDevice);
      
      // Update permitted devices list
      setPermittedDevices(prev => {
        const existing = prev.find(d => d.id === device.id);
        if (existing) {
          return prev.map(d => d.id === device.id ? bleDevice : d);
        }
        return [...prev, bleDevice];
      });

      setActiveTab('details');
    } catch (error) {
      console.error('Error connecting to new device:', error);
      const parsedError = parseBleError(error);
      onError(`Failed to connect to device: ${parsedError}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const scanDeviceServices = async (device: BluetoothDevice): Promise<BleServiceInfo[]> => {
    if (!device.gatt?.connected) {
      throw new Error('Device not connected');
    }

    try {
      const services = await device.gatt.getPrimaryServices();
      const serviceInfos: BleServiceInfo[] = [];

      for (const service of services) {
        const characteristics = await service.getCharacteristics();
        const charInfos: BleCharacteristicInfo[] = [];

        for (const char of characteristics) {
          charInfos.push({
            uuid: char.uuid,
            properties: {
              read: char.properties.read,
              write: char.properties.write,
              writeWithoutResponse: char.properties.writeWithoutResponse,
              notify: char.properties.notify,
              indicate: char.properties.indicate,
              broadcast: char.properties.broadcast,
              authenticatedSignedWrites: char.properties.authenticatedSignedWrites,
              reliableWrite: char.properties.reliableWrite,
              writableAuxiliaries: char.properties.writableAuxiliaries
            }
          });
        }

        serviceInfos.push({
          uuid: service.uuid,
          characteristics: charInfos
        });
      }

      return serviceInfos;
    } catch (error) {
      console.error('Error scanning device services:', error);
      return [];
    }
  };

  const connectToPermittedDevice = async (device: BleDevice) => {
    try {
      setIsConnecting(true);
      
      if (!device.gatt) {
        throw new Error('Device GATT server not available');
      }

      if (device.gatt.connected) {
        // Already connected
        setSelectedDevice(device);
        onDeviceSelected(device);
        setActiveTab('details');
        return;
      }      // Connect to the device using retry logic
      await retryWithBackoff(
        async () => {
          await device.gatt!.connect();
        },
        CONNECTION_CONFIG.MAX_RETRY_ATTEMPTS,
        1000 // 1 second base delay
      );
      
      // Update device state
      const updatedDevice = {
        ...device,
        connected: true,
        lastConnected: new Date()
      };      // Scan services if not already done
      if (!updatedDevice.services) {
        const services = await scanDeviceServices(device.gatt.device as any);
        updatedDevice.services = services;
      }

      setSelectedDevice(updatedDevice);
      onDeviceSelected(updatedDevice);
      
      // Update permitted devices list
      setPermittedDevices(prev =>
        prev.map(d => d.id === device.id ? updatedDevice : d)
      );

      setActiveTab('details');
    } catch (error) {
      console.error('Error connecting to permitted device:', error);
      const parsedError = parseBleError(error);
      onError(`Failed to connect: ${parsedError}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectDevice = (device: BleDevice) => {
    try {
      if (device.gatt?.connected) {
        device.gatt.disconnect();
      }

      const updatedDevice = { ...device, connected: false };
      
      if (selectedDevice?.id === device.id) {
        setSelectedDevice(null);
      }

      // Update permitted devices list
      setPermittedDevices(prev =>
        prev.map(d => d.id === device.id ? updatedDevice : d)
      );

      onDeviceDisconnected(device.id);
    } catch (error) {
      console.error('Error disconnecting device:', error);
      const parsedError = parseBleError(error);
      onError(`Failed to disconnect: ${parsedError}`);
    }
  };

  const handleScanForNewDevice = async () => {
    try {
      setIsScanning(true);
      await requestDevice();
    } catch (error) {
      console.error('Error scanning for devices:', error);
      const parsedError = parseBleError(error);
      onError(`Scan failed: ${parsedError}`);
    } finally {
      setIsScanning(false);
    }
  };

  const handleCustomDeviceSelected = async (discoveredDevice: DiscoveredBleDevice) => {
    try {
      setIsConnecting(true);
      
      // Use Web Bluetooth API to connect to the selected device
      const device = await navigator.bluetooth.requestDevice({
        filters: [
          { name: discoveredDevice.name || undefined },
          // If we have services from advertising data, filter by them
          ...(discoveredDevice.advertisingData?.services?.map(service => ({ services: [service] })) || [])
        ].filter(filter => Object.keys(filter).length > 0), // Remove empty filters
        optionalServices: [
          'battery_service',
          'device_information',
          CALCULATOR_SERVICE_UUID, // Custom calculator service
          DEV_CALCULATOR_SERVICE_UUID, // Dev calculator service
          ...selectedServiceUUIDs,
          ...(discoveredDevice.advertisingData?.services || [])
        ],
        // Fallback to acceptAllDevices if no valid filters
        ...(discoveredDevice.name || discoveredDevice.advertisingData?.services?.length ? {} : { acceptAllDevices: true })
      });

      // Process the selected device
      await handleNewDeviceConnected(device);
    } catch (error) {
      console.error('Error connecting to custom discovered device:', error);
      if (error instanceof Error && error.message.includes('User cancelled')) {
        // User cancelled the native picker, don't show error
        return;
      }
      const parsedError = parseBleError(error);
      onError(`Failed to connect to device: ${parsedError}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const requestDevice = async () => {
    try {
      await validateBleEnvironment();

      const device = await navigator.bluetooth.requestDevice({
        optionalServices: [
          'battery_service',
          'device_information',
          CALCULATOR_SERVICE_UUID, // Custom calculator service
          DEV_CALCULATOR_SERVICE_UUID, // Dev calculator service
          ...selectedServiceUUIDs
        ],
        acceptAllDevices: true
      });

      setNewDevice(device);
      return device;
    } catch (error) {
      console.error('Error requesting device:', error);
      throw error;
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <h2 className="text-2xl font-semibold text-gray-800 mb-6">BLE Device Management</h2>
      
      {/* Tab Navigation */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={() => setActiveTab('permitted')}
          className={`px-4 py-2 rounded-md font-medium ${
            activeTab === 'permitted'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Permitted Devices ({permittedDevices.length})
        </button>
        <button
          onClick={() => setActiveTab('scan')}
          className={`px-4 py-2 rounded-md font-medium ${
            activeTab === 'scan'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Scan New Device
        </button>
        {selectedDevice && (
          <button
            onClick={() => setActiveTab('details')}
            className={`px-4 py-2 rounded-md font-medium ${
              activeTab === 'details'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Device Details
          </button>
        )}
      </div>

      {/* Tab Content */}
      {activeTab === 'permitted' && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Previously Connected Devices</h3>
            <button
              onClick={loadPermittedDevices}
              className="px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm"
            >
              Refresh
            </button>
          </div>
          
          {permittedDevices.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              No previously connected devices found.
              <br />
              Use "Scan New Device" to discover and connect to BLE devices.
            </div>
          ) : (
            <div className="space-y-3">
              {permittedDevices.map((device) => (
                <div
                  key={device.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                >
                  <div>
                    <div className="font-medium text-gray-900">
                      {device.name || 'Unknown Device'}
                    </div>
                    <div className="text-sm text-gray-500">
                      ID: {device.id}
                    </div>
                    {device.lastConnected && (
                      <div className="text-xs text-gray-400">
                        Last connected: {device.lastConnected.toLocaleString()}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        device.connected
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {device.connected ? 'Connected' : 'Disconnected'}
                    </span>
                    
                    {device.connected ? (
                      <button
                        onClick={() => disconnectDevice(device)}
                        className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
                      >
                        Disconnect
                      </button>
                    ) : (
                      <button
                        onClick={() => connectToPermittedDevice(device)}
                        disabled={isConnecting}
                        className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 text-sm"
                      >
                        {isConnecting ? 'Connecting...' : 'Connect'}
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'scan' && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Discover New BLE Device</h3>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  <strong>Custom Device Scanner:</strong> This implementation provides an enhanced scanning 
                  interface that displays available devices with signal strength and device information, 
                  without requiring the browser's native device picker dialog.
                </p>
              </div>
            </div>
          </div>

          <CustomBleDeviceScanner
            onDeviceSelected={handleCustomDeviceSelected}
            onScanStateChange={setIsScanning}
            onError={onError}
            serviceFilters={[
              'battery_service',
              'device_information',
              CALCULATOR_SERVICE_UUID, // Custom calculator service
              DEV_CALCULATOR_SERVICE_UUID, // Dev calculator service
              ...selectedServiceUUIDs
            ]}
            className="bg-white"
            autoRefresh={true}
            refreshInterval={15000}
          />
        </div>
      )}

      {activeTab === 'details' && selectedDevice && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Device Details</h3>
            <button
              onClick={() => disconnectDevice(selectedDevice)}
              className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm"
            >
              Disconnect
            </button>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-gray-900 mb-2">Basic Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Name:</span>
                <span className="ml-2 text-gray-900">{selectedDevice.name || 'Unknown'}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">ID:</span>
                <span className="ml-2 text-gray-900 font-mono text-xs">{selectedDevice.id}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Connected:</span>
                <span className="ml-2 text-gray-900">{selectedDevice.connected ? 'Yes' : 'No'}</span>
              </div>
              {selectedDevice.lastConnected && (
                <div>
                  <span className="font-medium text-gray-700">Last Connected:</span>
                  <span className="ml-2 text-gray-900 text-xs">
                    {selectedDevice.lastConnected.toLocaleString()}
                  </span>
                </div>
              )}
            </div>
          </div>

          {selectedDevice.services && selectedDevice.services.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Services & Characteristics</h4>
              <div className="space-y-4">
                {selectedDevice.services.map((service, serviceIndex) => (
                  <div key={serviceIndex} className="border border-gray-200 rounded-lg p-4">
                    <div className="font-medium text-gray-900 mb-3">
                      Service: <span className="font-mono text-sm text-blue-600">{service.uuid}</span>
                    </div>
                    <div className="space-y-2">
                      {service.characteristics.map((char, charIndex) => (
                        <div key={charIndex} className="bg-gray-50 rounded p-3">
                          <div className="font-medium text-gray-800 text-sm mb-2">
                            Characteristic: <span className="font-mono text-xs text-blue-600">{char.uuid}</span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(char.properties).map(([prop, enabled]) => (
                              enabled && (
                                <span
                                  key={prop}
                                  className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium"
                                >
                                  {prop}
                                </span>
                              )
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Web Bluetooth Support Status */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <WebBluetoothStatus />
      </div>
    </div>
  );
};

// Web Bluetooth Support Status Component
const WebBluetoothStatus: React.FC = () => {
  const [bluetoothSupported, setBluetoothSupported] = useState<boolean | null>(null);
  const [bluetoothAvailable, setBluetoothAvailable] = useState<boolean | null>(null);

  useEffect(() => {
    checkBluetoothSupport();
  }, []);

  const checkBluetoothSupport = async () => {
    try {
      await validateBleEnvironment();
      setBluetoothSupported(true);
      setBluetoothAvailable(true);
    } catch (error) {
      const supported = isWebBluetoothSupported();
      setBluetoothSupported(supported);
      
      if (supported) {
        try {
          const available = await navigator.bluetooth.getAvailability();
          setBluetoothAvailable(available);
        } catch (availabilityError) {
          console.warn('Could not check Bluetooth availability:', availabilityError);
          setBluetoothAvailable(false);
        }
      } else {
        setBluetoothAvailable(false);
      }
    }
  };

  const getStatusColor = () => {
    if (!bluetoothSupported) return 'bg-red-100 text-red-800 border-red-200';
    if (!bluetoothAvailable) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-green-100 text-green-800 border-green-200';
  };

  const getStatusText = () => {
    if (!bluetoothSupported) return 'Not Supported';
    if (!bluetoothAvailable) return 'Disabled';
    return 'Available';
  };

  const getStatusIcon = () => {
    if (!bluetoothSupported) {
      return (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
        </svg>
      );
    }
    if (!bluetoothAvailable) {
      return (
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      );
    }
    return (
      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    );
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-medium text-gray-900">Web Bluetooth Status</h4>
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor()}`}>
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </div>
      </div>

      {!bluetoothSupported && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h5 className="font-medium text-red-900 mb-2">Web Bluetooth Not Supported</h5>
          <p className="text-sm text-red-700 mb-3">
            Your browser doesn't support Web Bluetooth API. BLE features will not work.
          </p>
          <div className="text-sm text-red-600">
            <strong>Supported Browsers:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Google Chrome (version 56+)</li>
              <li>Microsoft Edge (version 79+)</li>
              <li>Opera (version 43+)</li>
              <li>Samsung Internet (version 6.2+)</li>
            </ul>
          </div>
        </div>
      )}

      {bluetoothSupported && !bluetoothAvailable && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h5 className="font-medium text-yellow-900 mb-2">Web Bluetooth Disabled</h5>
          <p className="text-sm text-yellow-700 mb-3">
            Web Bluetooth is supported but currently disabled. Follow these steps to enable it:
          </p>
          <div className="text-sm text-yellow-600">
            <strong>Chrome/Edge Instructions:</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>Navigate to <code className="bg-yellow-100 px-1 rounded">chrome://flags</code> (or <code className="bg-yellow-100 px-1 rounded">edge://flags</code>)</li>
              <li>Search for "Experimental Web Platform features"</li>
              <li>Enable this flag</li>
              <li>Restart your browser</li>
            </ol>
            <p className="mt-3">
              <strong>Alternative:</strong> Ensure HTTPS is used and no security policies block Web Bluetooth.
            </p>
          </div>
        </div>
      )}

      {bluetoothSupported && bluetoothAvailable && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h5 className="font-medium text-green-900 mb-2">Web Bluetooth Ready</h5>
          <p className="text-sm text-green-700">
            Web Bluetooth is supported and enabled. You can now scan for and connect to BLE devices.
          </p>
        </div>
      )}
    </div>
  );
};

export default BleDeviceManager;
