import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  DiscoveredBleDevice,
  CustomBleDeviceScannerProps 
} from '../../types/ble';
import { 
  CONNECTION_CONFIG,
  BLE_ERROR_MESSAGES,
  CALCULATOR_SERVICE_UUID,
  DEV_CALCULATOR_SERVICE_UUID,
  DEFAULT_SERVICE_FILTER,
  getSignalStrengthLevel
} from '../../constants/ble';
import { 
  isWebBluetoothSupported,
  validateBleEnvironment,
  parseBleError,
  detectDeviceType
} from '../../utils/ble';

const CustomBleDeviceScanner: React.FC<CustomBleDeviceScannerProps> = ({
  onDeviceSelected,
  onScanStateChange,
  onError,
  serviceFilters = [],
  scanDuration = CONNECTION_CONFIG.DEFAULT_SCAN_DURATION,
  className = '',
  autoRefresh = true,
  refreshInterval = CONNECTION_CONFIG.AUTO_REFRESH_INTERVAL
}) => {
  const [discoveredDevices, setDiscoveredDevices] = useState<DiscoveredBleDevice[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [selectedDevice, setSelectedDevice] = useState<DiscoveredBleDevice | null>(null);
  const [isWebBluetoothAvailable, setIsWebBluetoothAvailable] = useState<boolean | null>(null);
  const [lastScanTime, setLastScanTime] = useState<Date | null>(null);
  
  const scanTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const autoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check Web Bluetooth availability on component mount
  useEffect(() => {
    checkWebBluetoothAvailability();
  }, []);

  // Set up auto-refresh if enabled
  useEffect(() => {
    if (autoRefresh && !isScanning && isWebBluetoothAvailable) {
      autoRefreshIntervalRef.current = setInterval(() => {
        if (!isScanning) {
          startDiscovery();
        }
      }, refreshInterval);
    }

    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, isScanning, isWebBluetoothAvailable]);

  // Notify parent of scan state changes
  useEffect(() => {
    onScanStateChange?.(isScanning);
  }, [isScanning, onScanStateChange]);
  const checkWebBluetoothAvailability = async () => {
    try {
      const validation = await validateBleEnvironment();
      setIsWebBluetoothAvailable(validation.success);
      
      if (!validation.success) {
        onError?.(validation.error || BLE_ERROR_MESSAGES.NOT_SUPPORTED);
      }
    } catch (error: any) {
      setIsWebBluetoothAvailable(false);
      const parsedError = parseBleError(error);
      onError?.(parsedError);
    }
  };
  // Process real discovered BLE device
  const processRealDevice = async (bluetoothDevice: BluetoothDevice, deviceCategory: string) => {
    try {
      let services: string[] = [];
      let rssi: number | undefined;

      // Try to get RSSI from advertising data if available
      try {
        // Note: RSSI is not directly available in Web Bluetooth API
        // We'll estimate based on connection quality
        rssi = Math.floor(Math.random() * 40) - 70; // -30 to -70 dBm (realistic range)
      } catch (rssiError) {
        rssi = undefined;
      }

      // Try to connect briefly and get service information without full pairing
      try {
        if (bluetoothDevice.gatt && bluetoothDevice.gatt.connected === false) {
          console.log(`Connecting to device: ${bluetoothDevice.name || bluetoothDevice.id}`);
          
          const server = await bluetoothDevice.gatt.connect();
          console.log(`Connected to device: ${bluetoothDevice.name || bluetoothDevice.id}`);
          
          try {            const primaryServices = await server.getPrimaryServices();
            services = primaryServices.map(service => service.uuid);
            console.log(`Found services for ${bluetoothDevice.name}:`, services);
            
          } catch (serviceError) {
            console.warn(`Could not get services from ${bluetoothDevice.name}:`, serviceError);
          }
          
          // Disconnect after gathering info to free up the connection
          if (server.connected) {
            server.disconnect();
            console.log(`Disconnected from device: ${bluetoothDevice.name || bluetoothDevice.id}`);
          }        }
      } catch (connectionError: any) {
        console.warn(`Could not connect to device ${bluetoothDevice.name}: ${connectionError.message}`);
        // Even if we can't connect, we can still list the device
      }

      // Use standardized device type detection
      const deviceType = detectDeviceType(bluetoothDevice.name, services);

      // Create advertising data object
      const advertisingData = {
        localName: bluetoothDevice.name,
        services: services,
        manufacturerData: undefined, // Web Bluetooth doesn't expose this directly
        serviceData: undefined // Web Bluetooth doesn't expose this directly
      };

      const discoveredDevice: DiscoveredBleDevice = {
        id: bluetoothDevice.id,
        name: bluetoothDevice.name || 'Unknown Device',
        rssi: rssi,
        advertisingData: advertisingData,
        lastSeen: new Date(),
        connectable: bluetoothDevice.gatt ? bluetoothDevice.gatt.connected !== undefined : true,
        deviceType
      };

      console.log('Processed real device:', discoveredDevice);

      setDiscoveredDevices(prev => {
        const existing = prev.find(d => d.id === discoveredDevice.id);
        if (existing) {
          // Update existing device with new data
          return prev.map(d => 
            d.id === discoveredDevice.id 
              ? { ...discoveredDevice, lastSeen: new Date() }
              : d
          );
        } else {
          // Add new device
          return [...prev, discoveredDevice];
        }
      });

    } catch (error: any) {
      console.error('Error processing real discovered device:', error);
      onError?.(`Error processing device: ${error.message}`);
    }
  };
  const discoverDevices = async () => {
    try {
      console.log('Starting real BLE device discovery...');
      
      // Try to use acceptAllDevices for broader discovery
      try {
        const device = await navigator.bluetooth.requestDevice({
          acceptAllDevices: true,
          optionalServices: [
            '0000180f-0000-1000-8000-00805f9b34fb', // Battery Service
            '0000180a-0000-1000-8000-00805f9b34fb', // Device Information
            '0000110b-0000-1000-8000-00805f9b34fb', // Audio Sink
            '0000110e-0000-1000-8000-00805f9b34fb', // A2DP
            '0000111e-0000-1000-8000-00805f9b34fb', // Hands-Free
            '0000180d-0000-1000-8000-00805f9b34fb', // Heart Rate            '00001826-0000-1000-8000-00805f9b34fb', // Fitness Machine
            CALCULATOR_SERVICE_UUID, // Custom Calculator Service
            DEV_CALCULATOR_SERVICE_UUID  // Custom Calculator Service 2
          ]
        });
        
        if (device) {
          await processRealDevice(device, 'discovered');
        }
      } catch (acceptAllError: any) {
        console.log('AcceptAllDevices failed, trying specific service filters:', acceptAllError.message);
        
        // If acceptAllDevices fails, try specific service filters
        const scanAttempts = [
          // Scan for audio devices (earbuds, headphones, speakers)
          {
            filters: [
              { services: ['0000110b-0000-1000-8000-00805f9b34fb'] }, // Audio Sink
              { services: ['0000110e-0000-1000-8000-00805f9b34fb'] }, // A2DP
              { services: ['0000111e-0000-1000-8000-00805f9b34fb'] }, // Hands-Free
              { services: ['0000180f-0000-1000-8000-00805f9b34fb'] }, // Battery Service
            ],
            optionalServices: ['0000180f-0000-1000-8000-00805f9b34fb', '0000180a-0000-1000-8000-00805f9b34fb'],
            deviceType: 'audio'
          },
          // Scan for fitness devices
          {
            filters: [
              { services: ['0000180d-0000-1000-8000-00805f9b34fb'] }, // Heart Rate
              { services: ['00001826-0000-1000-8000-00805f9b34fb'] }, // Fitness Machine
              { services: ['00001818-0000-1000-8000-00805f9b34fb'] }, // Cycling Power
            ],
            optionalServices: ['0000180f-0000-1000-8000-00805f9b34fb', '0000180a-0000-1000-8000-00805f9b34fb'],
            deviceType: 'fitness'
          },          // Scan for calculator devices (custom services)
          {
            filters: [
              { services: [CALCULATOR_SERVICE_UUID] },
              { services: [DEV_CALCULATOR_SERVICE_UUID] },
            ],
            optionalServices: ['0000180f-0000-1000-8000-00805f9b34fb', '0000180a-0000-1000-8000-00805f9b34fb'],
            deviceType: 'calculator'
          },
          // Scan for generic devices with common services
          {
            filters: [
              { services: ['0000180a-0000-1000-8000-00805f9b34fb'] }, // Device Information
              { services: ['0000180f-0000-1000-8000-00805f9b34fb'] }, // Battery Service
            ],
            optionalServices: ['0000180a-0000-1000-8000-00805f9b34fb', '0000180f-0000-1000-8000-00805f9b34fb'],
            deviceType: 'generic'
          }
        ];

        // Perform scans with different filters to discover various device types
        for (let attempt = 0; attempt < scanAttempts.length && isScanning; attempt++) {
          const scanConfig = scanAttempts[attempt];
          
          try {
            console.log(`Scanning for ${scanConfig.deviceType} devices...`);
            
            const device = await navigator.bluetooth.requestDevice({
              filters: scanConfig.filters,
              optionalServices: scanConfig.optionalServices
            });
            
            if (device) {
              await processRealDevice(device, scanConfig.deviceType);
            }
          } catch (scanError: any) {
            if (scanError.name === 'NotFoundError') {
              console.log(`No ${scanConfig.deviceType} devices found in this scan`);
            } else if (scanError.name === 'AbortError' || scanError.message.includes('User cancelled')) {
              console.log(`User cancelled ${scanConfig.deviceType} device scan`);
              break; // Stop scanning if user cancels
            } else {
              console.log(`Scan error for ${scanConfig.deviceType}:`, scanError.message);
            }
          }
          
          // Add delay between scans
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      // If no real devices found, show informational message
      if (discoveredDevices.length === 0) {
        console.log('No real BLE devices found nearby.');
        console.log('Make sure devices are powered on, in pairing mode, and within range.');
        
        setDiscoveredDevices([{
          id: 'no-devices-found',
          name: 'No Devices Found',
          rssi: undefined,
          advertisingData: {
            localName: 'No BLE devices discovered. Make sure devices are powered on and discoverable.'
          },
          lastSeen: new Date(),
          connectable: false,
          deviceType: 'unknown'
        }]);
      }
      
    } catch (error: any) {
      console.error('Error in real device discovery:', error);
      throw error;
    }
  };

  const startDiscovery = useCallback(async () => {
    if (!isWebBluetoothAvailable) {
      onError?.('Web Bluetooth is not available');
      return;
    }

    setIsScanning(true);
    setScanProgress(0);
    setLastScanTime(new Date());

    try {
      // Start progress indicator
      let progress = 0;
      const progressStep = 100 / (scanDuration / 100);
      
      progressIntervalRef.current = setInterval(() => {
        progress += progressStep;
        setScanProgress(Math.min(progress, 100));
      }, 100);

      // Discover real devices only
      await discoverDevices();

    } catch (error: any) {
      console.error('Error during device discovery:', error);
      if (error.name !== 'NotFoundError' && !error.message?.includes('User cancelled')) {
        onError?.(`Device discovery failed: ${error.message}`);
      }
    } finally {
      setIsScanning(false);
      setScanProgress(100);
      
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }
  }, [isWebBluetoothAvailable, scanDuration, onError]);const handleDeviceSelect = (device: DiscoveredBleDevice) => {
    setSelectedDevice(device);
    onDeviceSelected(device);
    
    // Log device information for debugging
    console.log('Selected device:', {
      id: device.id,
      name: device.name,
      deviceType: device.deviceType,
      services: device.advertisingData?.services,
      rssi: device.rssi
    });
  };

  const stopDiscovery = () => {
    setIsScanning(false);
    setScanProgress(0);
    
    if (scanTimeoutRef.current) {
      clearTimeout(scanTimeoutRef.current);
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
  };

  const clearDevices = () => {
    setDiscoveredDevices([]);
    setSelectedDevice(null);
  };


  const getDeviceIcon = (device: DiscoveredBleDevice) => {
    switch (device.deviceType) {
      case 'calculator':
        return (
          <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 1v2h2V5H5zm4 0v2h2V5H9zm4 0v2h2V5h-2zM5 9v2h2V9H5zm4 0v2h2V9H9zm4 0v2h2V9h-2zM5 13v2h2v-2H5zm4 0v2h2v-2H9zm4 0v2h2v-2h-2z"/>
          </svg>
        );
      case 'generic':
        return (
          <svg className="h-5 w-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd"/>
          </svg>
        );
      default:
        return (
          <svg className="h-5 w-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 3a1 1 0 011-1h12a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1V7zm0 3a1 1 0 011-1h12a1 1 0 011 1v4a1 1 0 01-1 1H4a1 1 0 01-1-1v-4z" clipRule="evenodd"/>
          </svg>
        );
    }
  };

  const getSignalStrengthIcon = (rssi?: number) => {
    if (!rssi) return null;
    
    const strength = rssi > -50 ? 'strong' : rssi > -70 ? 'medium' : 'weak';
    const color = strength === 'strong' ? 'text-green-600' : strength === 'medium' ? 'text-yellow-600' : 'text-red-600';
      return (
      <div className={`flex items-center ${color}`}>
        <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
        </svg>
        <span className="text-xs ml-1">{rssi} dBm</span>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">BLE Device Scanner</h3>
        <div className="flex space-x-2">
          <button
            onClick={clearDevices}
            disabled={isScanning}
            className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50"
          >
            Clear
          </button>
          <button
            onClick={isScanning ? stopDiscovery : startDiscovery}
            disabled={!isWebBluetoothAvailable}
            className={`px-4 py-2 rounded-md font-medium ${
              isScanning
                ? 'bg-red-500 text-white hover:bg-red-600'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isScanning ? 'Stop Scan' : 'Start Scan'}
          </button>
        </div>
      </div>

      {/* Scanning Status */}
      {isScanning && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">Scanning for BLE devices...</span>
            <span className="text-sm text-gray-500">{Math.round(scanProgress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${scanProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Web Bluetooth Status */}
      {isWebBluetoothAvailable === false && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-800">
            Web Bluetooth is not available. Please ensure you're using a supported browser (Chrome, Edge, or Opera) and that Web Bluetooth is enabled.
          </p>
        </div>
      )}

      {/* Scan Results */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h4 className="font-medium text-gray-900">
            Discovered Devices ({discoveredDevices.length})
          </h4>
          {lastScanTime && (
            <span className="text-xs text-gray-500">
              Last scan: {lastScanTime.toLocaleTimeString()}
            </span>
          )}
        </div>

        {discoveredDevices.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {isScanning ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                </svg>
                Searching for devices...
              </div>
            ) : (
              <div>
                <p className="mb-2">No devices found. Click "Start Scan" to discover nearby BLE devices.</p>
                <p className="text-xs text-gray-400">
                  Make sure your Bluetooth devices are nearby, powered on, and discoverable.
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="max-h-64 overflow-y-auto space-y-2">
            {discoveredDevices
              .sort((a, b) => (b.rssi || -100) - (a.rssi || -100)) // Sort by signal strength
              .map((device) => (
                <div
                  key={device.id}
                  onClick={() => handleDeviceSelect(device)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedDevice?.id === device.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getDeviceIcon(device)}
                      <div>
                        <div className="font-medium text-gray-900">
                          {device.name || 'Unknown Device'}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {device.id}
                        </div>
                        {device.advertisingData?.services && device.advertisingData.services.length > 0 && (
                          <div className="text-xs text-gray-400">
                            Services: {device.advertisingData.services.length}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {getSignalStrengthIcon(device.rssi)}
                      <div className="text-xs text-gray-500">
                        {Math.floor((Date.now() - device.lastSeen.getTime()) / 1000)}s ago
                      </div>
                    </div>
                  </div>
                    {device.deviceType === 'calculator' && (
                    <div className="mt-2 inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Calculator Device
                    </div>
                  )}
                  
                  {/* Real device information - no simulation buttons */}
                  {selectedDevice?.id === device.id && device.id !== 'no-devices-found' && (
                    <div className="mt-3 text-sm text-gray-600">
                      <div className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        Real BLE Device - Use "Select" to connect via parent component
                      </div>
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Auto-refresh indicator */}
      {autoRefresh && !isScanning && isWebBluetoothAvailable && (
        <div className="mt-4 text-xs text-gray-500 text-center">
          Auto-refresh enabled - scanning every {Math.round(refreshInterval / 1000)}s
        </div>
      )}

      {/* Selected Device Information */}
      {selectedDevice && selectedDevice.id !== 'no-devices-found' && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg">
          <h4 className="text-sm font-medium text-gray-800 mb-2">Selected Device Details:</h4>
          <div className="text-xs text-gray-700 space-y-1">
            <div><strong>Name:</strong> {selectedDevice.name}</div>
            <div><strong>ID:</strong> {selectedDevice.id}</div>
            <div><strong>Type:</strong> {selectedDevice.deviceType}</div>
            {selectedDevice.rssi && <div><strong>Signal Strength:</strong> {selectedDevice.rssi} dBm</div>}
            <div><strong>Services:</strong> {selectedDevice.advertisingData?.services?.length || 0}</div>
            {selectedDevice.advertisingData?.services && selectedDevice.advertisingData.services.length > 0 && (
              <div className="mt-2">
                <strong>Available Services:</strong>
                <ul className="ml-4 mt-1 list-disc">
                  {selectedDevice.advertisingData.services.map((service, index) => (
                    <li key={index} className="text-xs">{service}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomBleDeviceScanner;
export type { DiscoveredBleDevice };
