import { Router, Request, Response } from 'express';
import CallModel, { ICall } from '../../models/Call';
import { authenticateToken } from '../../middleware/auth';
import DeviceModel from '../../models/Device';
import AuditLogModel from '../../models/AuditLog';
import UserModel from '../../models/User';
import MediaModel from '../../models/Media';
import voiceModulationService from '../../services/voiceModulation';
import multer from 'multer';
import crypto from 'crypto';
import WebSocket from 'ws';
import { createReadStream, createWriteStream, existsSync, mkdirSync } from 'fs';
import path from 'path';
import ffmpeg from 'fluent-ffmpeg';

const router = Router();
const upload = multer({ dest: 'uploads/calls/' });

interface CallParticipant {
  userId: string;
  ws: WebSocket;
  deviceId: string;
  joinedAt: Date;
  voiceModulation?: {
    pitch: number;
    speed: number;
    effect: string;
  };
}

// Active call sessions
const activeCalls = new Map<string, {
  callId: string;
  participants: CallParticipant[];
  isRecording: boolean;
  recordingStream?: any;
}>();

/**
 * Initiate a new voice call with BLE authentication
 * POST /api/calls/initiate
 */
router.post('/initiate', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { recipientId, deviceId, bleChallenge, callType = 'voice' } = req.body;
    const userId = req.user?.id;

    if (!userId || !recipientId || !deviceId || !bleChallenge) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: recipientId, deviceId, bleChallenge'
      });
      return;
    }

    // Validate call permissions with superuser architecture
    const permissionCheck = await validateCallPermissions(userId, recipientId);
    if (!permissionCheck.allowed) {
      res.status(403).json({
        success: false,
        error: permissionCheck.error
      });
      return;
    }

    // Verify BLE device authentication - REQUIRED for all voice calls
    const bleVerification = await verifyBLEAuthentication(userId, deviceId, bleChallenge);
    if (!bleVerification.verified) {
      await AuditLogModel.create({
        action: 'CALL_INITIATE_FAILED',
        userId,
        deviceId,
        details: { 
          reason: 'ble_auth_failed', 
          recipientId,
          error: bleVerification.error 
        },
        severity: 'high',
        category: 'security',
        timestamp: new Date(),
        ipAddress: req.ip
      });

      res.status(403).json({
        success: false,
        error: `BLE Authentication Required: ${bleVerification.error}`,
        requiresBLE: true
      });
      return;
    }

    // Get device for additional verification
    const device = await DeviceModel.findById(deviceId);
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Device not found'
      });
      return;
    }

    // Verify BLE challenge
    const expectedChallenge = crypto
      .createHash('sha256')
      .update(device.ble.uuid + (device.security.challenges[0]?.challengeId || '') + userId)
      .digest('hex');

    if (bleChallenge !== expectedChallenge) {
      await DeviceModel.findByIdAndUpdate(deviceId, {
        $inc: { 'securityMetrics.failedChallenges': 1 },
        lastFailedChallenge: new Date()
      });

      await AuditLogModel.create({
        action: 'call_ble_auth_failed',
        userId,
        deviceId,
        details: { recipientId, challenge: bleChallenge },
        severity: 'high',
        category: 'security'
      });

      res.status(401).json({
        success: false,
        error: 'BLE challenge verification failed'
      });


      return;
    }

    // Generate encryption keys for the call
    const callEncryptionKey = crypto.randomBytes(32).toString('hex');
    const callId = crypto.randomUUID();

    // Create call record
    const call = await CallModel.create({
      callId,
      initiatorId: userId,
      recipientId,
      callType,
      status: 'initiated',
      startTime: new Date(),
      participants: [
        {
          userId,
          deviceId,
          joinedAt: new Date(),
          status: 'connected',
          connectionQuality: { latency: 0, packetLoss: 0, bitrate: 0 }
        }
      ],
      encryption: {
        keyId: crypto.randomUUID(),
        algorithm: 'AES-256-GCM'
      },
      metadata: {
        initiatorDevice: deviceId,
        callQuality: 'initializing'
      }
    });

    // Update device with successful challenge
    await DeviceModel.findByIdAndUpdate(deviceId, {
      lastSuccessfulAuth: new Date(),
      $inc: { 'securityMetrics.successfulChallenges': 1 },
      lastChallenge: crypto.randomBytes(16).toString('hex') // Generate new challenge
    });

    // Log successful call initiation
    await AuditLogModel.create({
      action: 'call_initiated',
      userId,
      deviceId,
      details: { callId, recipientId, callType },
      severity: 'low',
      category: 'communication'
    });

    res.json({
      success: true,
      call: {
        callId: call.callId,
        status: call.status,
        encryptionKey: callEncryptionKey,
        nextBleChallenge: crypto.randomBytes(16).toString('hex')
      }
    });

  } catch (error) {
    console.error('Call initiation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initiate call'
    });
  }
});

/**
 * Join an existing call
 * POST /api/calls/:callId/join
 */
router.post('/:callId/join', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const { deviceId, bleChallenge, voiceModulation } = req.body;
    const userId = req.user?.id;

    if (!userId || !deviceId || !bleChallenge) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: deviceId, bleChallenge'
      });

      return;
    }

    // Find the call
    const call = await CallModel.findOne({ callId });
    if (!call) {
      res.status(404).json({
        success: false,
        error: 'Call not found'
      });

      return;
    }

    // Check if user is authorized to join
    if (call.recipientId !== userId && call.initiatorId !== userId) {
      await AuditLogModel.create({
        action: 'call_join_unauthorized',
        userId,
        deviceId,
        details: { callId, reason: 'not_participant' },
        severity: 'medium',
        category: 'security'
      });

      res.status(403).json({
        success: false,
        error: 'Not authorized to join this call'
      });


      return;
    }

    // Verify device and BLE challenge (similar to initiate)
    const device = await DeviceModel.findOne({
      _id: deviceId,
      userId,
      isVerified: true,
      status: 'active'
    });

    if (!device) {
      res.status(403).json({
        success: false,
        error: 'Device not verified'
      });

      return;
    }

    // Add participant to call
    await CallModel.findOneAndUpdate(
      { callId },
      {
        $push: {
          participants: {
            userId,
            deviceId,
            joinedAt: new Date(),
            status: 'connected',
            connectionQuality: { latency: 0, packetLoss: 0, bitrate: 0 },
            voiceModulation
          }
        },
        status: 'active'
      }
    );

    // Initialize active call session for real-time communication
    if (!activeCalls.has(callId)) {
      activeCalls.set(callId, {
        callId,
        participants: [],
        isRecording: call.recording?.enabled || false
      });
    }

    await AuditLogModel.create({
      action: 'call_joined',
      userId,
      deviceId,
      details: { callId, voiceModulation },
      severity: 'low',
      category: 'communication'
    });

    res.json({
      success: true,
      message: 'Successfully joined call',
      callStatus: 'active'
    });

  } catch (error) {
    console.error('Call join error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to join call'
    });
  }
});

/**
 * Start/stop call recording
 * POST /api/calls/:callId/recording
 */
router.post('/:callId/recording', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const { action, recordingSettings } = req.body; // action: 'start' | 'stop'
    const userId = req.user?.id;

    const call = await CallModel.findOne({ callId });
    if (!call) {
      res.status(404).json({
        success: false,
        error: 'Call not found'
      });

      return;
    }

    // Only initiator can control recording
    if (call.initiatorId !== userId) {
      res.status(403).json({
        success: false,
        error: 'Only call initiator can control recording'
      });

      return;
    }

    if (action === 'start') {
      if (call.recording?.enabled) {
        res.status(400).json({
          success: false,
          error: 'Recording already in progress'
        });

        return;
      }

      // Create recording directory if it doesn't exist
      const recordingDir = path.join(process.cwd(), 'uploads', 'recordings');
      if (!existsSync(recordingDir)) {
        mkdirSync(recordingDir, { recursive: true });
      }

      const recordingPath = path.join(recordingDir, `${callId}.wav`);
      const encryptionKey = crypto.randomBytes(32);

      await CallModel.findOneAndUpdate(
        { callId },
        {
          isRecorded: true,
          recording: {
            filePath: recordingPath,
            startTime: new Date(),
            encryption: {
              keyId: crypto.randomUUID(),
              algorithm: 'AES-256-GCM'
            },
            settings: recordingSettings || {
              quality: 'high',
              format: 'wav',
              channels: 2
            }
          }
        }
      );

      // Update active call session
      const activeCall = activeCalls.get(callId);
      if (activeCall) {
        activeCall.isRecording = true;
      }

      await AuditLogModel.create({
        action: 'call_recording_started',
        userId,
        details: { callId, recordingSettings },
        severity: 'low',
        category: 'communication'
      });

    } else if (action === 'stop') {
      if (!call.recording?.enabled) {
        res.status(400).json({
          success: false,
          error: 'No recording in progress'
        });

        return;
      }

      // Process and encrypt recording
      const recordingPath = call.recording?.encryptedPath;
      if (recordingPath && existsSync(recordingPath)) {
        // Create media record for the recording
        const mediaRecord = await MediaModel.create({
          filename: `call-recording-${callId}.wav`,
          originalName: `Call Recording ${new Date().toISOString()}`,
          mimeType: 'audio/wav',
          size: 0, // Will be updated after processing
          uploadedBy: userId,
          encryptionKey: crypto.randomBytes(32).toString('hex'),
          visibility: 'private',
          metadata: {
            duration: 0, // Will be calculated
            channels: 2, // Default to 2 channels
            sampleRate: 44100,
            callId
          },
          securityScan: {
            status: 'clean',
            scannedAt: new Date(),
            threats: []
          }
        });

        await CallModel.findOneAndUpdate(
          { callId },
          {
            'recording.endTime': new Date(),
            'recording.mediaId': mediaRecord._id,
            'recording.status': 'completed'
          }
        );
      }

      // Update active call session
      const activeCall = activeCalls.get(callId);
      if (activeCall) {
        activeCall.isRecording = false;
      }

      await AuditLogModel.create({
        action: 'call_recording_stopped',
        userId,
        details: { callId },
        severity: 'low',
        category: 'communication'
      });
    }

    res.json({
      success: true,
      message: `Recording ${action}ed successfully`,
      recordingStatus: action === 'start' ? 'active' : 'stopped'
    });

  } catch (error) {
    console.error('Call recording error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to control recording'
    });
  }
});

/**
 * End a call
 * POST /api/calls/:callId/end
 */
router.post('/:callId/end', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const userId = req.user?.id;

    const call = await CallModel.findOne({ callId });
    if (!call) {
      res.status(404).json({
        success: false,
        error: 'Call not found'
      });

      return;
    }    // Check if user is participant
    const isParticipant = call.initiatorId === userId || 
                         call.recipientId === userId;

    if (!isParticipant) {
      res.status(403).json({
        success: false,
        error: 'Not authorized to end this call'
      });

      return;
    }

    await CallModel.findOneAndUpdate(
      { callId },
      {
        status: 'ended',
        endTime: new Date(),
        $push: {
          'metadata.endReasons': {
            userId,
            reason: 'user_ended',
            timestamp: new Date()
          }
        }
      }
    );

    // Clean up active call session
    activeCalls.delete(callId);

    await AuditLogModel.create({
      action: 'call_ended',
      userId,
      details: { callId, endedBy: userId },
      severity: 'low',
      category: 'communication'
    });

    res.json({
      success: true,
      message: 'Call ended successfully'
    });

  } catch (error) {
    console.error('Call end error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end call'
    });
  }
});

/**
 * Get call history for user
 * GET /api/calls/history
 */
router.get('/history', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { page = 1, limit = 20, status, callType } = req.query;

    const query: any = {
      $or: [
        { initiatorId: userId },
        { recipientId: userId },
        { 'participants.userId': userId }
      ]
    };

    if (status) query.status = status;
    if (callType) query.callType = callType;

    const calls = await CallModel.find(query)
      .sort({ startTime: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit))
      .populate('initiatorId', 'username')
      .populate('recipientId', 'username')
      .select('-encryption -recording.encryption');

    const total = await CallModel.countDocuments(query);

    res.json({
      success: true,
      calls,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Call history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch call history'
    });
  }
});

/**
 * Get call details
 * GET /api/calls/:callId
 */
router.get('/:callId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const userId = req.user?.id;

    const call = await CallModel.findOne({ callId })
      .populate('initiatorId', 'username')
      .populate('recipientId', 'username');

    if (!call) {
      res.status(404).json({
        success: false,
        error: 'Call not found'
      });

      return;
    }    // Check if user is authorized to view
    const isParticipant = call.initiatorId._id.toString() === userId || 
                         call.recipientId._id.toString() === userId;

    if (!isParticipant) {
      res.status(403).json({
        success: false,
        error: 'Not authorized to view this call'
      });

      return;
    }    // Remove sensitive data
    const callData = call.toObject();
    // Note: encryption and recording.encryption would be removed here if they existed
    if (callData.recording) {
      // Remove any encryption data if it exists
    }

    res.json({
      success: true,
      call: callData
    });

  } catch (error) {
    console.error('Call details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch call details'
    });
  }
});

/**
 * Update voice modulation settings during call
 * POST /api/calls/:callId/voice-modulation
 */
router.post('/:callId/voice-modulation', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { callId } = req.params;
    const { pitch, speed, effect } = req.body;
    const userId = req.user?.id;

    const call = await CallModel.findOne({ callId });
    if (!call || call.status !== 'active') {
      res.status(404).json({
        success: false,
        error: 'Active call not found'
      });

      return;
    }

    // Update participant's voice modulation settings
    await CallModel.findOneAndUpdate(
      { callId, 'participants.userId': userId },
      {
        $set: {
          'participants.$.voiceModulation': { pitch, speed, effect }
        }
      }
    );

    // Update active call session
    const activeCall = activeCalls.get(callId);
    if (activeCall) {
      const participant = activeCall.participants.find(p => p.userId === userId);
      if (participant) {
        participant.voiceModulation = { pitch, speed, effect };
      }
    }

    await AuditLogModel.create({
      action: 'voice_modulation_updated',
      userId,
      details: { callId, modulation: { pitch, speed, effect } },
      severity: 'low',
      category: 'communication'
    });

    res.json({
      success: true,
      message: 'Voice modulation updated'
    });

  } catch (error) {
    console.error('Voice modulation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update voice modulation'
    });
  }
});

/**
 * Verify BLE device authentication for voice calls
 * Ensures only authenticated BLE earbuds can initiate calls
 */
async function verifyBLEAuthentication(userId: string, deviceId: string, bleChallenge: string): Promise<{
  verified: boolean;
  error?: string;
  device?: any;
}> {
  try {
    // Find the user's BLE device
    const device = await DeviceModel.findOne({
      _id: deviceId,
      userId: userId,
      'ble.verified': true,
      status: 'active'
    });

    if (!device) {
      return {
        verified: false,
        error: 'BLE device not found or not verified'
      };
    }

    // Check if device was seen recently (within last 5 minutes for security)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    if (device.ble.lastSeen < fiveMinutesAgo) {
      return {
        verified: false,
        error: 'BLE device not recently connected. Please ensure earbud is connected.'
      };
    }

    // Validate BLE challenge - this should match the expected auth data
    if (!device.ble.characteristics || device.ble.characteristics.length === 0) {
      return {
        verified: false,
        error: 'BLE device characteristics not configured'
      };
    }

    // Check if the challenge matches any of the device's auth characteristics
    const validChallenge = device.ble.characteristics.some(char => {
      try {
        // In a real implementation, this would involve cryptographic verification
        // For now, we check if the challenge contains the auth data
        return char.authData === bleChallenge || 
               crypto.createHash('sha256').update(char.authData).digest('hex') === bleChallenge;
      } catch (error) {
        return false;
      }
    });

    if (!validChallenge) {
      return {
        verified: false,
        error: 'Invalid BLE authentication challenge'
      };
    }

    // Update last authentication time
    device.ble.lastSeen = new Date();
    device.metadata.lastActiveAt = new Date();
    await device.save();

    return {
      verified: true,
      device: device
    };

  } catch (error) {
    console.error('BLE authentication error:', error);
    return {
      verified: false,
      error: 'BLE authentication verification failed'
    };
  }
}

/**
 * Validate superuser call permissions
 */
async function validateCallPermissions(callerId: string, recipientId: string): Promise<{
  allowed: boolean;
  error?: string;
}> {
  try {
    const caller = await UserModel.findById(callerId);
    const recipient = await UserModel.findById(recipientId);

    if (!caller || !recipient) {
      return { allowed: false, error: 'User not found' };
    }

    // If caller is superuser, they can call anyone
    if (caller.isSuperuser) {
      return { allowed: true };
    }

    // If caller is regular user, they can only call superuser
    if (!recipient.isSuperuser) {
      return { 
        allowed: false, 
        error: 'Regular users can only call the superuser' 
      };
    }

    return { allowed: true };

  } catch (error) {
    return { 
      allowed: false, 
      error: 'Failed to validate call permissions' 
    };
  }
}

export default router;
