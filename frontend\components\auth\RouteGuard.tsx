/**
 * Route Guard Component
 * Validates authentication and session on route changes
 */

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { sessionManager } from '../../utils/sessionManager';
import { tokenManager } from '../../utils/tokenManager';

interface RouteGuardProps {
  children: React.ReactNode;
}

const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const router = useRouter();
  const [isValidating, setIsValidating] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    // Initial route validation
    const validateRoute = async () => {
      await checkRouteAuth();
    };

    validateRoute();
  }, []);

  useEffect(() => {
    // Validate on route changes
    const handleRouteChange = async (url: string) => {
      setIsValidating(true);
      await checkRouteAuth(url);
    };

    router.events.on('routeChangeStart', handleRouteChange);
    router.events.on('routeChangeComplete', () => setIsValidating(false));
    router.events.on('routeChangeError', () => setIsValidating(false));

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
      router.events.off('routeChangeComplete', () => setIsValidating(false));
      router.events.off('routeChangeError', () => setIsValidating(false));
    };
  }, [router]);

  const checkRouteAuth = async (url?: string) => {
    try {
      const currentPath = url || router.pathname;
      
      // Public routes that don't require authentication
      const publicRoutes = ['/', '/admin/login', '/api'];
      const isPublicRoute = publicRoutes.some(route => 
        currentPath === route || currentPath.startsWith(route)
      );

      if (isPublicRoute) {
        setIsAuthorized(true);
        setIsValidating(false);
        return;
      }

      // Check if user has valid authentication
      const hasAdminToken = tokenManager.isAuthenticated('admin');
      const hasUserToken = tokenManager.isAuthenticated('user');

      if (!hasAdminToken && !hasUserToken) {
        console.log('No valid authentication token found');
        await router.replace('/?session=invalid');
        setIsAuthorized(false);
        setIsValidating(false);
        return;
      }

      // Validate session with server
      const isSessionValid = await sessionManager.validateSession();
      
      if (!isSessionValid) {
        console.log('Session validation failed');
        sessionManager.expireSession('error');
        setIsAuthorized(false);
        setIsValidating(false);
        return;
      }

      // Check route-specific permissions
      if (currentPath.startsWith('/admin')) {
        if (!hasAdminToken) {
          console.log('Admin route requires admin token');
          await router.replace('/?session=invalid');
          setIsAuthorized(false);
          setIsValidating(false);
          return;
        }
      }

      // Update activity on successful validation
      sessionManager.updateActivity();
      
      setIsAuthorized(true);
      setIsValidating(false);
    } catch (error) {
      console.error('Route authentication error:', error);
      await router.replace('/?session=error');
      setIsAuthorized(false);
      setIsValidating(false);
    }
  };

  // Show loading while validating
  if (isValidating) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '40px',
            height: '40px',
            border: '4px solid rgba(255,255,255,0.3)',
            borderTop: '4px solid white',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <div>Validating session...</div>
        </div>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Only render children if authorized
  return isAuthorized ? <>{children}</> : null;
};

export default RouteGuard;
