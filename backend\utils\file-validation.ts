const ALLOWED_FILE_TYPES = {
  // Images
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'image/svg+xml': ['.svg'],
  
  // Videos
  'video/mp4': ['.mp4'],
  'video/webm': ['.webm'],
  'video/quicktime': ['.mov'],
  'video/x-msvideo': ['.avi'],
  
  // Audio
  'audio/mpeg': ['.mp3'],
  'audio/wav': ['.wav'],
  'audio/ogg': ['.ogg'],
  'audio/mp4': ['.m4a'],
  
  // Documents
  'application/pdf': ['.pdf'],
  'text/plain': ['.txt'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  
  // Archives
  'application/zip': ['.zip'],
  'application/x-7z-compressed': ['.7z'],
  'application/x-rar-compressed': ['.rar']
};

const MAX_FILE_SIZES = {
  'image/': 50 * 1024 * 1024, // 50MB
  'video/': 500 * 1024 * 1024, // 500MB
  'audio/': 100 * 1024 * 1024, // 100MB
  'application/pdf': 100 * 1024 * 1024, // 100MB
  'text/': 10 * 1024 * 1024, // 10MB
  'application/': 50 * 1024 * 1024, // 50MB
  'default': 10 * 1024 * 1024 // 10MB
};

export function validateFileType(mimeType: string, fileName: string): {
  isValid: boolean;
  error?: string;
} {  // Check if MIME type is allowed
  if (!(mimeType in ALLOWED_FILE_TYPES)) {
    return {
      isValid: false,
      error: `File type not allowed: ${mimeType}`
    };
  }
  
  // Check if file extension matches MIME type
  const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  const allowedExtensions = (ALLOWED_FILE_TYPES as any)[mimeType] as string[];
  
  if (!allowedExtensions.includes(fileExtension)) {
    return {
      isValid: false,
      error: `File extension ${fileExtension} does not match MIME type ${mimeType}`
    };
  }
  
  return { isValid: true };
}

export function validateFileSize(size: number, mimeType: string): {
  isValid: boolean;
  error?: string;
} {
  let maxSize = MAX_FILE_SIZES.default;
  
  // Find the most specific size limit
  for (const [type, limit] of Object.entries(MAX_FILE_SIZES)) {
    if (mimeType.startsWith(type) && type !== 'default') {
      maxSize = limit;
      break;
    }
  }
  
  if (size > maxSize) {
    return {
      isValid: false,
      error: `File too large: ${size} bytes (max: ${maxSize} bytes)`
    };
  }
  
  return { isValid: true };
}

export function getFileCategory(mimeType: string): string {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('text/') || mimeType.includes('document') || mimeType === 'application/pdf') return 'document';
  if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'archive';
  return 'other';
}
