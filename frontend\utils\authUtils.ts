/**
 * Authentication utilities using unified token management
 */

import tokenManager from './tokenManager';
import { sessionManager } from './sessionManager';
import { clearCsrfToken } from './csrfProtection';
import apiClient from './apiClient';

export const authUtils = {  /**
   * Logout user and clear all tokens
   */
  async logout(): Promise<void> {
    try {
      // Attempt to notify backend of logout
      await apiClient.post('/api/auth/logout');
    } catch (error) {
      console.warn('Failed to notify backend of logout:', error);
    } finally {
      // Clear session
      sessionManager.destroy();
      
      // Clear all tokens
      tokenManager.clearAllTokens();
      
      // Clear CSRF token
      clearCsrfToken();
      
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated(type: 'admin' | 'user' = 'admin'): boolean {
    return tokenManager.isAuthenticated(type);
  },

  /**
   * Get current token
   */
  getToken(type: 'admin' | 'user' = 'admin'): string | null {
    return tokenManager.getToken(type);
  },

  /**
   * Set authentication token
   */
  setToken(token: string, type: 'admin' | 'user' = 'admin'): void {
    tokenManager.setToken(token, type);
  },

  /**
   * Handle authentication failure
   */
  handleAuthFailure(reason: string = 'Authentication failed'): void {
    tokenManager.clearAllTokens();
    if (typeof window !== 'undefined') {
      window.location.href = `/?session=invalid&reason=${encodeURIComponent(reason)}`;
    }
  },

  /**
   * Initialize session after successful authentication
   */
  initializeSession(token: string, type: 'admin' | 'user' = 'admin', csrfToken?: string): void {
    // Set tokens
    tokenManager.setToken(token, type);
    
    // Set CSRF token if provided
    if (csrfToken) {
      const { setCsrfToken } = require('./csrfProtection');
      setCsrfToken(csrfToken);
    }
    
    // Initialize session management
    sessionManager.initialize({
      timeoutMinutes: 30,
      warningMinutes: 5,
      checkIntervalSeconds: 60,
      autoRefresh: true
    });
  },

  /**
   * Handle successful authentication response
   */
  handleAuthSuccess(response: any, type: 'admin' | 'user' = 'admin'): void {
    if (response.token) {
      this.initializeSession(response.token, type, response.csrfToken);
    }
  },
};

export default authUtils;
