import type { Request, Response, NextFunction } from 'express';
import Admin from '../../models/Admin';
import User from '../../models/User';
import Build from '../../models/Build';

/**
 * GET /api/dashboard/stats
 * Returns summary stats for the admin dashboard.
 * Requires admin authentication (middleware should enforce).
 */
export async function dashboardStatsHandler(req: Request, res: Response, next: NextFunction): Promise<void> {
  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }
  try {
    // Defensive: check if Build model has isActive, else count all
    let totalBuilds = 0;
    try {
      // Try to count active builds, fallback to all builds if field missing
      totalBuilds = await Build.countDocuments({ isActive: true });
      if (totalBuilds === 0) {
        totalBuilds = await Build.countDocuments();
      }
    } catch (e) {
      totalBuilds = await Build.countDocuments();
    }

    // Deep stats for dashboard
    const [
      totalUsers,
      activeUsers,
      lockedUsers,
      totalAdmins,
      recentFailedLogins
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ status: 'active' }),
      User.countDocuments({ status: 'locked' }),
      Admin.countDocuments(),
      User.countDocuments({ failedLoginAttempts: { $gte: 3 } })
    ]);

    // System health (simple example)
    let systemHealth = 'OK';
    if (lockedUsers > 0 || recentFailedLogins > 0) systemHealth = 'WARN';
    if (lockedUsers > 10 || recentFailedLogins > 10) systemHealth = 'ERROR';

    // Uptime (use process.uptime() correctly)
    const uptime = typeof process.uptime === 'function' ? `${Math.floor(process.uptime() / 60)} min` : undefined;

    res.status(200).json({
      totalUsers,
      activeUsers,
      lockedUsers,
      totalAdmins,
      totalBuilds,
      systemHealth,
      uptime,
      recentFailedLogins
    });
    return;
  } catch (error) {
    // Log stack in development
    if (process.env.NODE_ENV !== 'production') {
      console.error('Error fetching admin dashboard stats:', error);
    }
    res.status(500).json({ error: 'Failed to fetch dashboard stats', details: process.env.NODE_ENV !== 'production' ? (error as Error).stack : undefined });
    return;
  }
}
