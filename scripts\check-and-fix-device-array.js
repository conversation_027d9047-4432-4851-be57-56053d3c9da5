// Script to check if deviceFingerprintHash exists but devices array is empty, and fix it if needed
const mongoose = require('mongoose');

const mongoUri = '*************************************************************';

const UserSchema = new mongoose.Schema({
  username: String,
  deviceFingerprintHash: String,
  deviceMetadata: Object,
  devices: Array,
  status: String,
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function checkAndFixDeviceArray(username) {
  try {
    await mongoose.connect(mongoUri);
    const user = await User.findOne({ username });
    if (!user) {
      console.log(`❌ User '${username}' not found`);
      return;
    }
    console.log(`User: ${user.username}`);
    console.log(`deviceFingerprintHash: ${user.deviceFingerprintHash}`);
    console.log(`devices array:`, user.devices);
    if (user.deviceFingerprintHash && (!user.devices || user.devices.length === 0)) {
      // Add device entry to devices array
      const deviceEntry = {
        deviceId: user.deviceFingerprintHash,
        fingerprint: user.deviceFingerprintHash,
        deviceType: 'mobile',
        deviceModel: user.deviceMetadata?.model || '',
        os: user.deviceMetadata?.os || '',
        browser: '',
        lastUsed: new Date(),
        isActive: true,
        bleDevices: []
      };
      user.devices = [deviceEntry];
      await user.save();
      console.log('✅ Added device entry to devices array.');
    } else if (user.devices && user.devices.length > 0) {
      console.log('✅ Devices array already populated.');
    } else {
      console.log('ℹ️ No deviceFingerprintHash found.');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Database connection closed');
  }
}

// Usage: node check-and-fix-device-array.js <username>
const username = process.argv[2];
if (!username) {
  console.log('Usage: node check-and-fix-device-array.js <username>');
  process.exit(1);
}
checkAndFixDeviceArray(username);
