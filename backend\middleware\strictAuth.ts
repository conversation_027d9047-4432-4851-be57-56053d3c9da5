/**
 * Enhanced Authent  try {
    const token = req.cookies?.['admin-token'] || req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {ion Middleware with Strict Role-Based Access Control
 * Ensures only admin can access admin panel, users/superusers must use mobile app
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import Admin, { IAdmin } from '../models/Admin';
import User, { IUser } from '../models/User';
import AuditLogModel from '../models/AuditLog';
import { logSecurityEvent } from '../utils/security-logger';

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      admin?: IAdmin;
      user?: IUser;
    }
  }
}

// Admin-only access middleware for admin panel routes
export async function adminPanelOnly(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const token = req.cookies?.['admin-token'] || req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: 'unknown',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'No admin token provided',
        details: { path: req.path }
      });
      
      res.status(401).json({ 
        error: 'Admin access only - Please use admin credentials',
        code: 'ADMIN_TOKEN_REQUIRED'
      });
      return;
    }

    // Verify admin token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    // Extract adminId first to use in logging
    const adminId = decoded.adminId || decoded.id; // Handle both field names
    
    console.log('Admin token decoded:', { 
      id: decoded.id, 
      adminId: decoded.adminId, 
      username: decoded.username, 
      type: decoded.type 
    });
    
    if (decoded.type !== 'admin') {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: decoded.username || 'unknown',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'Invalid admin token type',
        details: { tokenType: decoded.type, userId: adminId, path: req.path }
      });
      
      res.status(403).json({ 
        error: 'Admin panel access denied - Invalid token type',
        code: 'INVALID_ADMIN_TOKEN'
      });
      return;
    }

    // Verify admin exists and is active
    const admin = await Admin.findById(adminId);
    
    console.log('Admin lookup result:', { 
      found: !!admin, 
      adminId: adminId, 
      isActive: admin?.isActive, 
      username: admin?.username,
      role: admin?.role 
    });
    
    if (!admin || !admin.isActive) {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: decoded.username || 'unknown',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'Inactive admin access attempt',
        details: { adminId: adminId, path: req.path }
      });
      
      res.status(403).json({ 
        error: 'Admin account inactive or not found',
        code: 'ADMIN_INACTIVE'
      });
      return;
    }

    // Verify admin role is valid (admin or superadmin)
    if (admin.role !== 'admin' && admin.role !== 'superadmin') {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: admin.username,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'Non-admin role access attempt',
        details: { adminId: admin._id, role: admin.role, path: req.path }
      });
      
      res.status(403).json({ 
        error: 'Insufficient privileges - Admin role required',
        code: 'INSUFFICIENT_ADMIN_PRIVILEGES'
      });
      return;
    }    // Log successful admin access
    await AuditLogModel.create({
      logId: `admin_access_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      adminId: admin._id,
      event: {
        type: 'admin_action',
        action: 'ADMIN_PANEL_ACCESS',
        result: 'success',
        severity: 'low'
      },
      context: {
        ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'unknown',
        endpoint: req.path,
        method: req.method
      },        compliance: {
          category: 'system_admin',
        retention: 'long',
        piiIncluded: false,
        sensitiveData: false,
        exportable: true
      },
      data: {
        metadata: {
          method: req.method,
          path: req.path,
          timestamp: new Date()
        }
      }
    });

    req.admin = admin;
    next();
    
  } catch (error) {
    logSecurityEvent({
      eventType: 'failed_login',
      userType: 'admin',
      username: 'unknown',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      success: false,
      reason: 'Admin token verification error',
      details: { 
        error: error instanceof Error ? error.message : 'Unknown error',
        path: req.path 
      }
    });
    
    res.status(401).json({ 
      error: 'Invalid admin token',
      code: 'ADMIN_TOKEN_INVALID'
    });
  }
}

// Mobile app only access middleware for user/superuser routes
export async function mobileAppOnly(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const deviceFingerprint = req.headers['x-device-fingerprint'];
    
    if (!token) {
      res.status(401).json({ 
        error: 'Mobile app authentication required',
        code: 'MOBILE_TOKEN_REQUIRED'
      });
      return;
    }

    // Verify this is a mobile device request (only requires device fingerprint now)
    if (!deviceFingerprint) {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'user',
        username: 'unknown',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'Non-mobile access attempt',
        details: { 
          path: req.path,
          hasDeviceFingerprint: !!deviceFingerprint
        }
      });
      
      res.status(403).json({ 
        error: 'Mobile device authentication required - Web access not allowed',
        code: 'MOBILE_DEVICE_REQUIRED'
      });
      return;
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    
    if (decoded.type !== 'user') {
      res.status(403).json({ 
        error: 'Invalid user token type',
        code: 'INVALID_USER_TOKEN'
      });
      return;
    }

    // Verify user exists
    const user = await User.findById(decoded.id);
    if (!user) {
      res.status(403).json({ 
        error: 'User account not found',
        code: 'USER_NOT_FOUND'
      });
      return;
    }

    // Check if user has required properties
    if (user.status !== 'active') {
      res.status(403).json({ 
        error: 'User account inactive',
        code: 'USER_INACTIVE'
      });
      return;
    }

    // Verify user role - check if superuser or regular user
    if (!user.isSuperuser && user.status === 'active') {
      // Allow both regular users and superusers
      req.user = user as IUser;
      next();
      return;
    }

    if (user.isSuperuser) {
      // Allow superuser access
      req.user = user as IUser;
      next();
      return;
    }

    // If we get here, deny access
    logSecurityEvent({
      eventType: 'failed_login',
      userType: 'user',
      username: user.username || 'unknown',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      success: false,
      reason: 'Invalid user access',
      details: { userId: user._id, isSuperuser: user.isSuperuser, status: user.status, path: req.path }
    });
    
    res.status(403).json({ 
      error: 'Invalid user access',
      code: 'INVALID_USER_ACCESS'
    });
    return;
    
  } catch (error) {
    res.status(401).json({ 
      error: 'Invalid user token',
      code: 'USER_TOKEN_INVALID'
    });
  }
}

// Block web access for user/superuser accounts
export async function blockWebAccessForUsers(req: Request, res: Response, next: NextFunction): Promise<void> {
  const userAgent = req.get('User-Agent') || '';
  const isWebBrowser = /Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(userAgent);
  
  if (isWebBrowser) {
    const deviceFingerprint = req.headers['x-device-fingerprint'];
    
    // If it looks like a web browser and doesn't have mobile device headers
    // Only require device fingerprint now (BLE UUID removed from authentication)
    if (!deviceFingerprint) {
      logSecurityEvent({
        eventType: 'failed_login',
        userType: 'user',
        username: 'unknown',
        ipAddress: req.ip,
        userAgent: userAgent,
        success: false,
        reason: 'Web access blocked for users',
        details: { path: req.path }
      });
      
      res.status(403).json({
        error: 'Web access not allowed - Please use the mobile application',
        code: 'WEB_ACCESS_DENIED',
        message: 'User and superuser accounts can only access the system through the mobile application'
      });
      return;
    }
  }
  
  next();
}

// Verify admin-only endpoints
export function verifyAdminOnlyEndpoint(req: Request, res: Response, next: NextFunction): void {
  const adminOnlyPaths = [
    '/api/admin',
    '/admin',
    '/api/voice/admin',
    '/api/chat/admin',
    '/api/audit',
    '/api/system'
  ];
  
  const isAdminPath = adminOnlyPaths.some(path => req.path.startsWith(path));
  
  if (isAdminPath) {
    // Ensure we have admin authentication for admin paths
    adminPanelOnly(req, res, next);
  } else {
    next();
  }
}
