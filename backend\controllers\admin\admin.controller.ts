import { Request, Response } from 'express';
import Admin from '../../models/Admin';
import { generatePPKKeyPair, verifySignature } from '../../utils/ppk';
import bcrypt from 'bcrypt';
import { generatePaginationData } from '../../utils/pagination';

/**
 * Get all admins with pagination
 * GET /api/admin/admins
 */
export async function getAdmins(req: Request, res: Response): Promise<void> {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    const total = await Admin.countDocuments();
    const admins = await Admin.find({}, '-password -sessionTokens')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    // Generate pagination data
    const pagination = generatePaginationData(page, limit, total);

    res.status(200).json({
      admins,
      pagination
    });
  } catch (error) {
    console.error('Error fetching admins:', error);
    res.status(500).json({ error: 'Failed to fetch admins' });
  }
}

/**
 * Create new admin user
 * POST /api/admin/admins
 */
export async function createAdmin(req: Request, res: Response): Promise<void> {
  try {
    // Enforce single admin: block if any admin exists
    const adminCount = await Admin.countDocuments();
    if (adminCount > 0) {
      res.status(403).json({ error: 'Only one admin is allowed in the system.' });
      return;
    }

    const { username, email, password, role, ppkEnabled, authMethod } = req.body;

    // Check if username or email already exists
    const existingAdmin = await Admin.findOne({ $or: [{ username }, { email }] });
    if (existingAdmin) {
      res.status(409).json({ error: 'Username or email already exists' });
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create the admin
    const admin = new Admin({
      username,
      email,
      password: hashedPassword,
      role: role || 'admin',
      ppkEnabled: ppkEnabled || false,
      authMethod: authMethod || 'password'
    });

    // Generate PPK keys if PPK is enabled
    if (ppkEnabled) {
      if (authMethod === 'ppk' || authMethod === 'both') {
        try {
          // Generate unencrypted keys for simplicity in this implementation
          const keyPair = generatePPKKeyPair(false);
          
          // Store the public key in the admin document
          admin.ppkPublicKey = keyPair.publicKey;
          admin.ppkKeyVersion = new Date().toISOString();
          admin.ppkLastRotated = new Date();
          
          // Return both keys in the response (private key should be stored securely by the client)
          await admin.save();
          
          res.status(201).json({
            message: 'Admin created with PPK authentication',
            admin: {
              id: admin._id,
              username: admin.username,
              email: admin.email,
              role: admin.role,
              ppkEnabled: admin.ppkEnabled,
              authMethod: admin.authMethod
            },
            ppk: {
              publicKey: keyPair.publicKey,
              privateKey: keyPair.privateKey,
              keyVersion: admin.ppkKeyVersion
            }
          });
          return;
        } catch (error) {
          console.error('Error generating PPK key pair:', error);
          res.status(500).json({ error: 'Failed to generate PPK key pair' });
          return;
        }
      } else {
        res.status(400).json({ error: 'PPK is enabled but authMethod is not compatible' });
        return;
      }
    }

    await admin.save();
    
    res.status(201).json({
      message: 'Admin created successfully',
      admin: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        ppkEnabled: admin.ppkEnabled,
        authMethod: admin.authMethod
      }
    });
  } catch (error) {
    console.error('Error creating admin:', error);
    res.status(500).json({ error: 'Failed to create admin' });
  }
}

/**
 * Update admin
 * PUT /api/admin/admins/:id
 */
export async function updateAdmin(req: Request, res: Response): Promise<void> {
  try {
    const { email, role, isActive, authMethod } = req.body;
    
    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      res.status(404).json({ error: 'Admin not found' });
      return;
    }
    
    // Update fields if provided
    if (email) admin.email = email;
    if (role) admin.role = role;
    if (typeof isActive === 'boolean') admin.isActive = isActive;
    if (authMethod) {
      // Validate authMethod change
      if (authMethod === 'ppk' && !admin.ppkPublicKey) {
        res.status(400).json({ error: 'Cannot set authMethod to ppk without a public key' });
        return;
      }
      admin.authMethod = authMethod;
    }
    
    await admin.save();
    
    res.status(200).json({
      message: 'Admin updated successfully',
      admin: {
        id: admin._id,
        username: admin.username,
        email: admin.email,
        role: admin.role,
        isActive: admin.isActive,
        authMethod: admin.authMethod,
        ppkEnabled: admin.ppkEnabled
      }
    });
  } catch (error) {
    console.error('Error updating admin:', error);
    res.status(500).json({ error: 'Failed to update admin' });
  }
}

/**
 * Generate new PPK key pair for admin
 * POST /api/admin/admins/:id/generate-ppk
 */
export async function generateAdminPPK(req: Request, res: Response): Promise<void> {
  try {
    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      res.status(404).json({ error: 'Admin not found' });
      return;
    }
    
    // Generate unencrypted keys for simplicity
    const keyPair = generatePPKKeyPair(false);
    
    // Update admin with new public key
    admin.ppkPublicKey = keyPair.publicKey;
    admin.ppkKeyVersion = new Date().toISOString();
    admin.ppkLastRotated = new Date();
    admin.ppkEnabled = true;
    
    await admin.save();
    
    res.status(200).json({
      message: 'PPK key pair generated successfully',
      admin: {
        id: admin._id,
        username: admin.username,
        ppkEnabled: admin.ppkEnabled,
        ppkKeyVersion: admin.ppkKeyVersion,
        ppkLastRotated: admin.ppkLastRotated
      },
      ppk: {
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey,
        keyVersion: admin.ppkKeyVersion
      }
    });
  } catch (error) {
    console.error('Error generating PPK key pair:', error);
    res.status(500).json({ error: 'Failed to generate PPK key pair' });
  }
}

/**
 * Verify a PPK signature
 * POST /api/admin/admins/:id/verify-ppk
 */
export async function verifyAdminPPK(req: Request, res: Response): Promise<void> {
  try {
    const { challenge, signature } = req.body;
    
    if (!challenge || !signature) {
      res.status(400).json({ error: 'Challenge and signature are required' });
      return;
    }
    
    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      res.status(404).json({ error: 'Admin not found' });
      return;
    }
    
    if (!admin.ppkPublicKey) {
      res.status(400).json({ error: 'Admin does not have a PPK public key' });
      return;
    }
    
    const isValid = verifySignature(challenge, signature, admin.ppkPublicKey);
    
    res.status(200).json({
      isValid,
      message: isValid ? 'PPK signature verified successfully' : 'PPK signature verification failed'
    });
  } catch (error) {
    console.error('Error verifying PPK signature:', error);
    res.status(500).json({ error: 'Failed to verify PPK signature' });
  }
}

/**
 * Delete admin
 * DELETE /api/admin/admins/:id
 */
export async function deleteAdmin(req: Request, res: Response): Promise<void> {
  try {
    const admin = await Admin.findById(req.params.id);
    if (!admin) {
      res.status(404).json({ error: 'Admin not found' });
      return;
    }
    
    // Instead of deleting, set to inactive for audit purposes
    admin.isActive = false;
    await admin.save();
    
    res.status(200).json({
      message: 'Admin deactivated successfully'
    });
  } catch (error) {
    console.error('Error deactivating admin:', error);
    res.status(500).json({ error: 'Failed to deactivate admin' });
  }
}
