import { Request, Response, NextFunction } from 'express';

// In-memory store for rate limiting (per IP)
const loginAttempts: Record<string, { count: number; lastAttempt: number }> = {};
const WINDOW_MS = 15 * 60 * 1000; // 15 minutes
const MAX_ATTEMPTS = 10;

function getClientIp(req: Request): string {
  const xff = req.headers['x-forwarded-for'];
  if (typeof xff === 'string') return xff.split(',')[0].trim();
  if (Array.isArray(xff)) return xff[0];
  return req.ip || req.connection.remoteAddress || '';
}

export function loginRateLimit(req: Request, res: Response, next: NextFunction) {
  const ip = getClientIp(req);
  const now = Date.now();
  if (!ip) return next();
  if (!loginAttempts[ip]) {
    loginAttempts[ip] = { count: 1, lastAttempt: now };
    return next();
  }
  const attempt = loginAttempts[ip];
  if (now - attempt.lastAttempt > WINDOW_MS) {
    attempt.count = 1;
    attempt.lastAttempt = now;
    return next();
  }
  attempt.count++;
  attempt.lastAttempt = now;
  if (attempt.count > MAX_ATTEMPTS) {
    return res.status(429).json({ error: 'Too many login attempts. Please try again later.' });
  }
  next();
}

// Rate limiting middleware
