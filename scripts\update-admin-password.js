/**
 * Update Admin Password in Docker Container
 * This script connects to the MongoDB inside the Docker container and updates admin credentials
 */

// Update the admin password in MongoDB
const updateAdminPassword = `
db = db.getSiblingDB('ccalc');
const adminUser = db.admins.findOne({ username: 'admin' });

if (adminUser) {
  print('Found admin user: ' + adminUser.username);
  
  // Update the admin password (using a bcrypt hash for 'admin123')
  db.admins.updateOne(
    { _id: adminUser._id },
    { 
      $set: { 
        password: '$2b$10$oKlT3Pzrkl4UqW4/QRplneE02n/Q01NXdEHXQGgAHPO4p9mVi8pxK',
        isActive: true
      } 
    }
  );
  
  print('Admin password updated to "admin123"');
} else {
  print('Admin user not found, creating new admin user');
  
  // Create a new admin user with bcrypt hash for 'admin123'
  db.admins.insertOne({
    username: 'admin',
    email: '<EMAIL>',
    password: '$2b$10$oKlT3Pzrkl4UqW4/QRplneE02n/Q01NXdEHXQGgAHPO4p9mVi8pxK',
    role: 'admin',
    isActive: true,
    authMethod: 'password',
    ppkEnabled: false,
    createdAt: new Date()
  });
  
  print('Created new admin user with password "admin123"');
}

// Verify the admin user
const updatedAdmin = db.admins.findOne({ username: 'admin' });
print('Admin user status: ' + (updatedAdmin.isActive ? 'Active' : 'Inactive'));
print('Admin email: ' + updatedAdmin.email);
`;
