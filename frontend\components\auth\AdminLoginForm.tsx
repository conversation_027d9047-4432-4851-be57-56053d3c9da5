import React, { useState, useEffect } from 'react';
import { useAuth } from '../../utils/authProvider';
import { useRouter } from 'next/router';
import { importPrivateKey } from '../../utils/ppkAuth'; 
import Button from '../admin/Button';
import Icon from '../admin/Icon';

const AdminLoginForm: React.FC = () => {
  const { handleLogin, error: authError, loading: authLoading } = useAuth();
  const router = useRouter();
  
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [activeStep, setActiveStep] = useState(0); // 0 for username/password, 1 for PPK
  const [error, setError] = useState<string | null>(null);
  const [selectedPrivateKey, setSelectedPrivateKey] = useState<CryptoKey | null>(null);
  const [privateKeyFile, setPrivateKeyFile] = useState<File | null>(null);

  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleUsernamePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);    try {
      // Attempt login. AuthProvider's login function should handle the logic
      // to determine if PPK is needed and throw an error or return a specific status.
      await handleLogin(username, password);
      // If login is successful without needing PPK, or if AuthProvider handles redirect:
      router.push('/admin/dashboard');
    } catch (err: any) {
      // Check if the error indicates PPK is required
      // This specific error message "PPK_REQUIRED" should be standardized with AuthProvider
      if (err.message === 'PPK_REQUIRED' || (err.response && err.response.data && err.response.data.requiresPPK)) {
        setActiveStep(1); // Move to PPK step
      } else {
        setError(err.message || 'Login failed. Please check your credentials.');
      }
    }
  };

  const handlePrivateKeyUpload = async (file: File) => {
    setPrivateKeyFile(file);
    setError(null);
    try {
      const fileContent = await readFileAsText(file);
      const importedKey = await importPrivateKey(fileContent);
      setSelectedPrivateKey(importedKey);
    } catch (err: any) {
      setError('Failed to read or import private key file: ' + err.message);
      setSelectedPrivateKey(null);
      setPrivateKeyFile(null);
    }
  };

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  };
  const handlePPKAuthentication = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPrivateKey) {
      setError('Private key is not loaded or is invalid.');
      return;
    }
    setError(null);
    
    try {
      // For PPK authentication, we need to generate a challenge and signature
      // This is a simplified approach - in a real implementation, you'd get a challenge from the server
      const challenge = `${username}-${Date.now()}`;
        // Use the PPK utils to sign the challenge
      const { signChallenge } = await import('../../utils/ppkAuth');
      const signature = await signChallenge(challenge, selectedPrivateKey);
      
      // Pass the challenge and signature to handleLogin
      await handleLogin(username, password, challenge, signature);
      // If successful, AuthProvider should handle state and HOC/useEffect might redirect
      router.push('/admin/dashboard');
    } catch (err: any) {
      setError(err.message || 'PPK Authentication failed.');
    }
  };

  return (
    <div className="card w-full max-w-md shadow-xl bg-base-100">
      <div className="card-body">
        <h2 className="card-title text-2xl justify-center mb-2">Admin Login</h2>
        <p className="text-center text-base-content/70 mb-6 text-sm">
          {activeStep === 0 ? 'Enter your credentials to continue' : 'Complete authentication with your private key'}
        </p>

        {error && 
          <div className="alert alert-error mb-4"> {/* Corrected to alert-error */}
            <Icon name="alert-circle" className="mr-2" />
            <span>{error}</span>
          </div>
        }

        {activeStep === 0 && (
          <form onSubmit={handleUsernamePasswordSubmit} className="space-y-4">
            <div className="form-group">
              <label htmlFor="username" className="form-label">Username</label>
              <input id="username" type="text" value={username} onChange={e => setUsername(e.target.value)} autoFocus required className="input input-bordered w-full" />
            </div>
            <div className="form-group">
              <label htmlFor="password" className="form-label">Password</label>
              <input id="password" type="password" value={password} onChange={e => setPassword(e.target.value)} required className="input input-bordered w-full" />
            </div>
            <div className="form-actions mt-6">
              <Button type="submit" variant="primary" className="w-full" isLoading={authLoading} disabled={authLoading}>
                {authLoading ? 'Verifying...' : 'Login / Next'} 
              </Button>
            </div>
          </form>
        )}

        {activeStep === 1 && (
          <form onSubmit={handlePPKAuthentication} className="space-y-4">
            <div className="flex items-center p-3 rounded-md bg-base-200 border border-base-300 mb-4">
              <Icon name="key" className="text-primary mr-3 text-2xl" /> 
              <div>
                <h3 className="font-semibold text-base-content">Key Authentication Required</h3>
                <p className="text-sm text-base-content/70">Please upload your private key (.pem) file to authenticate.</p>
              </div>
            </div>
            
            <div className="form-group">
              <label htmlFor="privateKeyFile" className="form-label">Private Key File (.pem)</label>
              <input
                id="privateKeyFile"
                type="file"
                accept=".pem"
                onChange={(e) => { // Corrected: 'e' is defined here
                  if (e.target.files && e.target.files[0]) {
                    handlePrivateKeyUpload(e.target.files[0]);
                  }
                }}
                className="file-input file-input-bordered w-full"
              />
              {privateKeyFile && <p className="form-text mt-1 text-xs">Selected: {privateKeyFile.name}</p>}
            </div>
            
            <div className="form-actions grid grid-cols-2 gap-3 mt-6">
              <Button 
                type="button" 
                variant="ghost" 
                onClick={() => {
                  setActiveStep(0);
                  setSelectedPrivateKey(null);
                  setPrivateKeyFile(null);
                  setError(null); 
                }}
                disabled={authLoading}
              >
                Back
              </Button>
              <Button 
                type="submit" 
                variant="primary" 
                isLoading={authLoading} 
                disabled={authLoading || !selectedPrivateKey || !privateKeyFile}
              >
                {authLoading ? 'Authenticating...' : 'Login with Key'}
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default AdminLoginForm;
