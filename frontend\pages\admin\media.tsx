import React, { useEffect, useState, useRef } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { apiClient } from '../../utils/axiosClient';
import { useAdminAuth } from '../../contexts/AdminAuthContext';
import { withAdminAuth } from '../../contexts/AdminAuthContext';

interface MediaItem {
  _id: string;
  mediaId: string;
  createdAt: string;
  file: {
    originalName: string;
    mimeType: string;
    size: number;
    category: string;
    url?: string;
  };
  access: {
    visibility: 'public' | 'private' | 'restricted';
  };
  processing?: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
  };
  uploadedBy?: string;
  tags?: string[];
  description?: string;
}

const MediaPage: React.FC = () => {
  const [media, setMedia] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedVisibility, setSelectedVisibility] = useState<string>('all');
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { admin, checkPermission } = useAdminAuth();

  const fetchMedia = async (pageNum = 1) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams();
      params.append('page', pageNum.toString());
      params.append('limit', '25');
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedVisibility !== 'all') params.append('visibility', selectedVisibility);
      if (searchTerm) params.append('search', searchTerm);

      const res = await apiClient.frontend.get(`/api/media?${params.toString()}`);
      setMedia(res.data.media || []);
      setTotalPages(res.data.pagination?.pages || 1);
    } catch (e: any) {
      setError(e.response?.data?.error || 'Failed to fetch media');
      // Mock data for demo when API fails
      setMedia([
        {
          _id: '1',
          mediaId: 'media_001',
          createdAt: new Date().toISOString(),
          file: {
            originalName: 'example-image.jpg',
            mimeType: 'image/jpeg',
            size: 2048000,
            category: 'image',
            url: '/uploads/example-image.jpg'
          },
          access: {
            visibility: 'public'
          },
          processing: {
            status: 'completed'
          },
          uploadedBy: 'admin',
          tags: ['example', 'test'],
          description: 'Example image file'
        },
        {
          _id: '2',
          mediaId: 'media_002',
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          file: {
            originalName: 'demo-video.mp4',
            mimeType: 'video/mp4',
            size: 15728640,
            category: 'video',
            url: '/uploads/demo-video.mp4'
          },
          access: {
            visibility: 'restricted'
          },
          processing: {
            status: 'processing'
          },
          uploadedBy: 'admin',
          tags: ['demo', 'video'],
          description: 'Demo video file'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMedia(page);
  }, [page, selectedCategory, selectedVisibility, searchTerm]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    setError(null);

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('category', getFileCategory(file.type));
        formData.append('visibility', 'private');

        await apiClient.frontend.post('/api/media/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      }

      setSuccess(`Successfully uploaded ${files.length} file(s)`);
      setIsUploadModalOpen(false);
      fetchMedia(page);
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to upload files');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDeleteMedia = async () => {
    if (!selectedMedia) return;

    try {
      await apiClient.frontend.delete(`/api/media/${selectedMedia._id}`);
      setSuccess('Media deleted successfully');
      setIsDeleteModalOpen(false);
      setSelectedMedia(null);
      fetchMedia(page);
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete media');
    }
  };

  const getFileCategory = (mimeType: string): string => {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.includes('pdf')) return 'document';
    return 'other';
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getVisibilityBadge = (visibility: string) => {
    const badges = {
      public: 'bg-green-100 text-green-800',
      private: 'bg-gray-100 text-gray-800',
      restricted: 'bg-orange-100 text-orange-800'
    };
    return badges[visibility as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800'
    };
    return badges[status as keyof typeof badges] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'image': return '🖼️';
      case 'video': return '🎬';
      case 'audio': return '🎵';
      case 'document': return '📄';
      default: return '📁';
    }
  };

  const filteredMedia = media.filter(item =>
    item.file.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.mediaId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <>
      <Head>
        <title>Media Management | CCALC Admin</title>
        <meta name="description" content="Manage media files and uploads for CCALC." />
      </Head>
      <AdminLayout>
        <div className="min-h-screen bg-gray-50 py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Media Management</h1>
                <p className="mt-2 text-gray-600">Upload, organize, and manage media files</p>
              </div>
              <div className="mt-4 sm:mt-0">
                <button
                  onClick={() => setIsUploadModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Upload Media
                </button>
              </div>
            </div>

            {/* Alerts */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
            {success && (
              <div className="mb-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {success}
              </div>
            )}

            {/* Filters */}
            <div className="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                  <input
                    type="text"
                    placeholder="Search media..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Categories</option>
                    <option value="image">Images</option>
                    <option value="video">Videos</option>
                    <option value="audio">Audio</option>
                    <option value="document">Documents</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Visibility</label>
                  <select
                    value={selectedVisibility}
                    onChange={(e) => setSelectedVisibility(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Visibility</option>
                    <option value="public">Public</option>
                    <option value="private">Private</option>
                    <option value="restricted">Restricted</option>
                  </select>
                </div>
                <div className="flex items-end">
                  <button
                    onClick={() => fetchMedia(1)}
                    className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    Refresh
                  </button>
                </div>
              </div>
            </div>

            {/* Media Grid */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">Loading media...</p>
                </div>
              ) : filteredMedia.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  No media files found
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6">
                    {filteredMedia.map((item) => (
                      <div key={item._id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                        <div className="aspect-w-16 aspect-h-9 bg-gray-100 flex items-center justify-center text-4xl">
                          {item.file.mimeType.startsWith('image/') && item.file.url ? (
                            <img 
                              src={item.file.url} 
                              alt={item.file.originalName}
                              className="w-full h-32 object-cover"
                            />
                          ) : (
                            <span>{getCategoryIcon(item.file.category)}</span>
                          )}
                        </div>
                        <div className="p-4">
                          <h3 className="font-medium text-gray-900 truncate" title={item.file.originalName}>
                            {item.file.originalName}
                          </h3>
                          <p className="text-sm text-gray-500 mt-1">{formatFileSize(item.file.size)}</p>
                          <div className="flex items-center justify-between mt-3">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getVisibilityBadge(item.access.visibility)}`}>
                              {item.access.visibility}
                            </span>
                            {item.processing && (
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadge(item.processing.status)}`}>
                                {item.processing.status}
                              </span>
                            )}
                          </div>
                          <div className="flex justify-between mt-3 space-x-2">
                            <button
                              onClick={() => {
                                setSelectedMedia(item);
                                setIsDetailsModalOpen(true);
                              }}
                              className="flex-1 px-3 py-1 text-xs bg-blue-50 text-blue-700 rounded hover:bg-blue-100"
                            >
                              View
                            </button>
                            <button
                              onClick={() => {
                                setSelectedMedia(item);
                                setIsDeleteModalOpen(true);
                              }}
                              className="flex-1 px-3 py-1 text-xs bg-red-50 text-red-700 rounded hover:bg-red-100"
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                      <div className="text-sm text-gray-700">
                        Page {page} of {totalPages}
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setPage(Math.max(1, page - 1))}
                          disabled={page === 1}
                          className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Previous
                        </button>
                        <button
                          onClick={() => setPage(Math.min(totalPages, page + 1))}
                          disabled={page === totalPages}
                          className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Next
                        </button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>

        {/* Upload Modal */}
        {isUploadModalOpen && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4">Upload Media</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Files
                  </label>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
                    onChange={handleFileUpload}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Supported formats: Images, Videos, Audio, Documents
                  </p>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setIsUploadModalOpen(false);
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                  disabled={uploading}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Details Modal */}
        {isDetailsModalOpen && selectedMedia && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">Media Details</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Filename</label>
                  <p className="text-sm text-gray-900">{selectedMedia.file.originalName}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Media ID</label>
                  <p className="text-sm text-gray-900">{selectedMedia.mediaId}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Size</label>
                    <p className="text-sm text-gray-900">{formatFileSize(selectedMedia.file.size)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Type</label>
                    <p className="text-sm text-gray-900">{selectedMedia.file.mimeType}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Category</label>
                    <p className="text-sm text-gray-900">{selectedMedia.file.category}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Visibility</label>
                    <p className="text-sm text-gray-900">{selectedMedia.access.visibility}</p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Upload Date</label>
                  <p className="text-sm text-gray-900">{new Date(selectedMedia.createdAt).toLocaleString()}</p>
                </div>
                {selectedMedia.description && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="text-sm text-gray-900">{selectedMedia.description}</p>
                  </div>
                )}
                {selectedMedia.tags && selectedMedia.tags.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tags</label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedMedia.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setIsDetailsModalOpen(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {isDeleteModalOpen && selectedMedia && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold mb-4 text-red-600">Delete Media</h3>
              <p className="text-gray-700 mb-6">
                Are you sure you want to delete "{selectedMedia.file.originalName}"? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteMedia}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </>
  );
};

export default withAdminAuth(MediaPage);
