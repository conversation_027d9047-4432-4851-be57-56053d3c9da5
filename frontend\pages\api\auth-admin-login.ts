import type { NextApiRequest, NextApiResponse } from 'next';

// This file exists for backward compatibility
// Redirect to the new path structure
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Redirect this request to the new path
  const { method, body, headers } = req;
  
  try {
    // Check if body is a valid JSON object
    if (typeof body === 'string') {
      try {
        req.body = JSON.parse(body);
      } catch (parseError: any) {
        console.error('JSON parsing error:', parseError);
        return res.status(400).json({ 
          error: 'Invalid JSON in request body',
          message: parseError.message
        });
      }
    }
      // Import the actual handler - using the init endpoint for login
    const actualHandler = (await import('./auth/admin/login/init')).default;

    // Call the actual handler with the request
    return actualHandler(req, res);
  } catch (error: any) {
    console.error('Error forwarding request to new admin login handler:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message || 'Unknown error'
    });
  }
}