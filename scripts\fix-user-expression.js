// Quick fix: Create a script to reset the mobileuser expression with normalized hashing
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

async function fixUserExpression() {
  try {
    await mongoose.connect('***************************************************************');
    
    const User = mongoose.model('User', require('../backend/models/User').userSchema);
    
    const user = await User.findOne({ username: 'mobileuser' });
    if (!user) {
      console.log('User not found');
      return;
    }
    
    const expression = '5+6'; // Normalized version
    const expressionHash = await bcrypt.hash(expression, 12);
    
    user.expressionHash = expressionHash;
    user.unlockExpression = expression;
    await user.save({ validateModifiedOnly: true });
    
    console.log('✅ Expression reset successfully for mobileuser');
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Alternative: Use the admin panel to reset the expression
// Navigate to admin panel at http://localhost:3005
// 1. Click on "mobileuser"  
// 2. Click "Reset Expression" admin action
// 3. Enter "5+6" (without spaces)
// 4. This will hash it with the normalized version

console.log('Please use the admin panel to reset the expression for mobileuser:');
console.log('1. Go to http://localhost:3005');
console.log('2. Click on "mobileuser"');
console.log('3. Click "Reset Expression"');
console.log('4. Enter "5+6" (without spaces)');
console.log('5. Try mobile login again');

// fixUserExpression();