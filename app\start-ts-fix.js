#!/usr/bin/env node

/**
 * Expo start script with TypeScript loading workaround
 * 
 * This script sets the NODE_OPTIONS environment variable to register ts-node
 * as a loader for TypeScript files before starting Expo.
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Expo with TypeScript loading workaround...');

// Set environment variables to help with TypeScript loading
process.env.NODE_OPTIONS = '--experimental-vm-modules';

// Start Expo in a child process
const expo = spawn('npx', ['expo', 'start', '--port', '8082', '--no-dev'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    EXPO_LEGACY_IMPORTS: '1',  // Tell Expo to use legacy imports
    NODE_ENV: 'production'     // Run in production mode
  }
});

// Handle process exit
expo.on('exit', (code) => {
  console.log(`Expo process exited with code ${code}`);
  process.exit(code);
});

// Handle process errors
expo.on('error', (err) => {
  console.error('Failed to start Expo:', err);
  process.exit(1);
});
