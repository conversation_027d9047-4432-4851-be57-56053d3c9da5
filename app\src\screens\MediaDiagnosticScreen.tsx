/**
 * Media Diagnostic Tool
 * Analyzes media storage in the app's document directory
 */

import * as FileSystem from 'expo-file-system';
import * as React from 'react';
import { View, Text, ScrollView, StyleSheet, Button, Alert } from 'react-native';

// File info interface
interface MediaFileInfo {
  name: string;
  path: string;
  size: number;
  type: 'image' | 'video' | 'thumbnail' | 'other';
  isThumb: boolean;
}

// Helper to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
};

export default function MediaDiagnosticScreen() {
  const [fileList, setFileList] = React.useState<MediaFileInfo[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [stats, setStats] = React.useState({ files: 0, size: 0, thumbs: 0 });

  const analyzeMediaStorage = async () => {
    try {
      setLoading(true);
      
      // Define directory paths
      const mediaDir = `${FileSystem.documentDirectory}CCALC_Media/`;
      const thumbsDir = `${FileSystem.documentDirectory}CCALC_Media/Thumbnails/`;
      
      // Check if directories exist
      const mediaDirInfo = await FileSystem.getInfoAsync(mediaDir);
      const thumbsDirInfo = await FileSystem.getInfoAsync(thumbsDir);
      
      if (!mediaDirInfo.exists) {
        Alert.alert('Error', 'Media directory does not exist');
        setLoading(false);
        return;
      }
      
      // Get files from media directory
      const mediaFiles = await FileSystem.readDirectoryAsync(mediaDir);
      
      // Get thumbnail files if directory exists
      let thumbFiles: string[] = [];
      if (thumbsDirInfo.exists) {
        thumbFiles = await FileSystem.readDirectoryAsync(thumbsDir);
      }
      
      // Analyze main media files
      const filesWithInfo = await Promise.all(
        mediaFiles
          .filter(file => !file.startsWith('.') && file !== 'Thumbnails') // Skip hidden files and the Thumbnails directory
          .map(async (file) => {
            const filePath = `${mediaDir}${file}`;
            const info = await FileSystem.getInfoAsync(filePath, { size: true });
            return {
              name: file,
              path: filePath,
              size: info.exists ? (info as any).size || 0 : 0,
              type: file.endsWith('.jpg') || file.endsWith('.jpeg') || file.endsWith('.png') 
                ? 'image' as const
                : file.endsWith('.mov') || file.endsWith('.mp4') 
                ? 'video' as const
                : 'other' as const,
              isThumb: file.includes('_thumb')
            };
          })
      );
      
      // Analyze thumbnail files
      const thumbsWithInfo = await Promise.all(
        thumbFiles.map(async (file) => {
          const filePath = `${thumbsDir}${file}`;
          const info = await FileSystem.getInfoAsync(filePath, { size: true });
          return {
            name: file,
            path: filePath,
            size: info.exists ? (info as any).size || 0 : 0,
            type: 'thumbnail' as const,
            isThumb: true
          };
        })
      );
      
      // Combine and sort by name
      const allFiles = [...filesWithInfo, ...thumbsWithInfo].sort((a, b) => 
        a.name.localeCompare(b.name)
      );
      
      // Calculate stats
      const totalSize = allFiles.reduce((sum, file) => sum + file.size, 0);
      
      setFileList(allFiles);
      setStats({
        files: filesWithInfo.length,
        thumbs: thumbsWithInfo.length,
        size: totalSize
      });
      
    } catch (error) {
      console.error('Failed to analyze media storage:', error);
      Alert.alert('Error', `Failed to analyze media: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    analyzeMediaStorage();
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Media Storage Diagnostic</Text>
      
      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>Media Files: {stats.files}</Text>
        <Text style={styles.statsText}>Thumbnails: {stats.thumbs}</Text>
        <Text style={styles.statsText}>Total Size: {formatFileSize(stats.size)}</Text>
      </View>
      
      <Button 
        title={loading ? "Analyzing..." : "Refresh Analysis"} 
        onPress={analyzeMediaStorage}
        disabled={loading}
      />
      
      <ScrollView style={styles.fileList}>
        {fileList.map((file, index) => (
          <View key={index} style={[
            styles.fileItem, 
            file.isThumb ? styles.thumbItem : null,
            file.type === 'video' ? styles.videoItem : null,
            file.type === 'image' ? styles.imageItem : null
          ]}>
            <Text style={styles.fileName}>{file.name}</Text>
            <Text style={styles.fileDetails}>
              {file.type} • {formatFileSize(file.size)}
            </Text>
          </View>
        ))}
        
        {fileList.length === 0 && !loading && (
          <Text style={styles.emptyText}>No media files found</Text>
        )}
        
        {loading && (
          <Text style={styles.loadingText}>Loading files...</Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f8f8',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsContainer: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  statsText: {
    fontSize: 14,
    marginBottom: 4,
  },
  fileList: {
    marginTop: 16,
  },
  fileItem: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  thumbItem: {
    backgroundColor: '#f0f8ff',
    borderLeftWidth: 3,
    borderLeftColor: '#4a90e2',
  },
  videoItem: {
    backgroundColor: '#fff8f0',
    borderLeftWidth: 3,
    borderLeftColor: '#e2844a',
  },
  imageItem: {
    backgroundColor: '#f0fff8',
    borderLeftWidth: 3,
    borderLeftColor: '#4ae28a',
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  fileDetails: {
    fontSize: 12,
    color: '#666',
  },
  emptyText: {
    textAlign: 'center',
    padding: 24,
    color: '#666',
    fontStyle: 'italic',
  },
  loadingText: {
    textAlign: 'center',
    padding: 24,
    color: '#666',
  },
});
