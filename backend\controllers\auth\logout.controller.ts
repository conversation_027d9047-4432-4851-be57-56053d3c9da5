import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import TokenBlacklist from '../../models/TokenBlacklist';
import securityLogger from '../../utils/security-logger';

/**
 * Logout controller for users and admins
 * Invalidates JWT tokens by adding them to a blacklist
 */
export async function logoutController(req: Request, res: Response): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(400).json({ error: 'No token provided' });
      return;
    }

    const token = authHeader.split(' ')[1];
    
    // Add token to blacklist with expiry
    try {      // Verify token to get expiration time
      const secret = process.env.JWT_SECRET || 'fallback-secret';
      const decoded = jwt.verify(token, secret) as any;
      
      // Calculate expiry date from token's exp claim
      const expiryDate = new Date((decoded.exp || 0) * 1000);
      
      // Hash token for security (prevents token contents from being stored in plain text)
      const hash = crypto.createHash('sha256').update(token).digest('hex');
      
      // Store in blacklist
      await new TokenBlacklist({
        token: hash,
        expiresAt: expiryDate
      }).save();
      
      // Log the token revocation for security audit
      const userType = decoded.adminId ? 'admin' : 'user';
      const username = decoded.username || 'unknown';
      const userId = decoded.adminId || decoded.userId || 'unknown';
      
      securityLogger.logSecurityEvent({
        eventType: 'token_revoked',
        userType,
        username,
        userId,
        ipAddress: req.ip || req.socket.remoteAddress,
        userAgent: req.headers['user-agent'],
        success: true,
        details: {
          tokenExpiry: expiryDate
        }
      });
      
      res.status(200).json({ message: 'Logout successful' });
    } catch (error) {
      // Even if token is invalid, we'll return success since the goal is to logout
      res.status(200).json({ message: 'Logout successful' });
    }
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
