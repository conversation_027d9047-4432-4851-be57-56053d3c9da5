services:
  mongo:
    image: mongo:6.0
    container_name: ccalc-mongo
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example
    ports:
      - "27017:27017"
    volumes:
      - ./docker/mongo-init:/docker-entrypoint-initdb.d
      - ./docker/mongo-data:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - ccalc-network
      
  backend:
    build:
      context: .
      dockerfile: ./backend/Dockerfile
    container_name: ccalc-backend
    depends_on:
      mongo:
        condition: service_healthy
    environment:
      MONGO_URI: *********************************************************
      JWT_SECRET: your-very-secure-jwt-secret
      NODE_ENV: development
      PORT: 3000
      COOKIE_DOMAIN: localhost
      ADMIN_TOKEN_EXPIRY: 8h
      USER_TOKEN_EXPIRY: 24h
    ports:
      - "3000:3000"
    restart: unless-stopped
    volumes:
      - ./backend/logs:/usr/src/app/logs
    networks:
      - ccalc-network
  app:
    build:
      context: ./app
      dockerfile: Dockerfile
    container_name: ccalc-app
    depends_on:
      - mongo
    environment:
      NODE_ENV: production
      PORT: 8081
    ports:
      - "8081:8081"
    restart: unless-stopped
    networks:
      - ccalc-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ccalc-frontend
    depends_on:
      - backend
    environment:
      NODE_ENV: production
      PORT: 3001
      NEXT_PUBLIC_BACKEND_URL: http://backend:3000
      BACKEND_URL: http://backend:3000
      NEXT_PUBLIC_AUTH_COOKIE_DOMAIN: localhost
    ports:
      - "3001:3001"
    restart: unless-stopped
    networks:
      - ccalc-network

  nginx:
    image: nginx:alpine
    container_name: ccalc-nginx
    depends_on:
      - backend
      - frontend
    volumes:
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "80:80"
      - "443:443"
    restart: unless-stopped
    networks:
      - ccalc-network

networks:
  ccalc-network:
    driver: bridge
