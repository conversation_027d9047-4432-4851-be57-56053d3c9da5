@echo off
setlocal enabledelayedexpansion

echo 🚀 CCALC App Build Setup
echo ========================
echo.

REM Check if we're in the app directory
if not exist "package.json" (
    echo ❌ Error: Please run this script from the app directory
    echo cd app && setup-build.bat
    exit /b 1
)

echo 📋 This script will help you configure the build environment.
echo.

REM Get URLs from user
set /p BACKEND_URL="Enter your backend ngrok URL [https://your-backend.ngrok.io]: "
if "!BACKEND_URL!"=="" set BACKEND_URL=https://your-backend.ngrok.io

set /p FRONTEND_URL="Enter your frontend ngrok URL [!BACKEND_URL!]: "
if "!FRONTEND_URL!"=="" set FRONTEND_URL=!BACKEND_URL!

set /p BUILD_ENV="Enter build environment [development]: "
if "!BUILD_ENV!"=="" set BUILD_ENV=development

set /p APP_VERSION="Enter app version [1.0.0]: "
if "!APP_VERSION!"=="" set APP_VERSION=1.0.0

echo.
echo 📝 Creating .env file...

REM Create .env file
(
echo # Environment Configuration for CCALC App
echo # Generated on %date% %time%
echo.
echo # Backend URL - Your ngrok backend URL
echo EXPO_PUBLIC_BACKEND_URL=!BACKEND_URL!
echo.
echo # Frontend URL - Your ngrok frontend URL  
echo EXPO_PUBLIC_FRONTEND_URL=!FRONTEND_URL!
echo.
echo # Build Environment
echo EXPO_PUBLIC_BUILD_ENV=!BUILD_ENV!
echo.
echo # App Version
echo EXPO_PUBLIC_APP_VERSION=!APP_VERSION!
echo.
echo # Debug flags
echo EXPO_PUBLIC_DEBUG_MODE=true
echo EXPO_PUBLIC_LOG_NETWORK=true
) > .env

echo ✅ Created .env file with your configuration
echo.

REM Show the created configuration
echo 📄 Configuration created:
echo ------------------------
type .env
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    call npm install
) else (
    echo ✅ Dependencies already installed
)

REM Check if EAS is configured
if not exist "eas.json" (
    echo ⚠️  EAS configuration not found
    echo Run 'eas build:configure' to set up EAS builds
) else (
    echo ✅ EAS configuration found
)

echo.
echo 🎉 Setup complete!
echo.
echo Next steps:
echo 1. Make sure your backend is running at: !BACKEND_URL!
echo 2. Test the app locally: npm run start
echo 3. Build for iOS: npm run build:ios
echo 4. Build for Android: npm run build:android
echo.
echo For GitHub Actions builds:
echo 1. Add EXPO_TOKEN to your repository secrets
echo 2. Use the 'Build Mobile Apps' workflow
echo 3. Enter your ngrok URLs when prompted
echo.
echo Happy building! 🚀

pause
