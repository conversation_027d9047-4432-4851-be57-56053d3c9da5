// <PERSON>ript to verify that a private key and public key form a valid pair
// Usage: node scripts/verify-keypair.js <publicKeyPath> <privateKeyPath>

const fs = require('fs');
const crypto = require('crypto');
const path = require('path');

// Default to latest dev_admin keys if no args provided
const keysDir = path.resolve(__dirname, '../keys');
const publicKeyPath = process.argv[2] || path.join(keysDir, 'dev_admin_public_2025-06-02T07-54-35-268Z.pem');
const privateKeyPath = process.argv[3] || path.join(keysDir, 'dev_admin_private_2025-06-02T07-54-35-268Z.pem');

const publicKey = fs.readFileSync(publicKeyPath, 'utf8');
const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

const message = 'test-challenge-' + Date.now();

// Sign with private key
const sign = crypto.createSign('SHA256');
sign.update(message);
sign.end();
const signature = sign.sign(privateKey, 'base64');

// Verify with public key
const verify = crypto.createVerify('SHA256');
verify.update(message);
verify.end();
const isValid = verify.verify(publicKey, signature, 'base64');

console.log('Public key path:', publicKeyPath);
console.log('Private key path:', privateKeyPath);
console.log('Test message:', message);
console.log('Signature valid:', isValid);
if (!isValid) {
  process.exit(1);
}
