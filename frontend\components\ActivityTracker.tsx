/**
 * Activity Tracker Component
 * Tracks user activity and updates session manager
 */

import { useEffect, useRef, useCallback } from 'react';
import { sessionManager } from '../utils/sessionManager';

interface ActivityTrackerProps {
  children: React.ReactNode;
  // Throttle activity updates (in milliseconds)
  throttleMs?: number;
  // Events to track
  events?: string[];
}

const ActivityTracker: React.FC<ActivityTrackerProps> = ({
  children,
  throttleMs = 1000, // Update at most once per second
  events = ['click', 'keydown', 'mousemove', 'scroll', 'touchstart', 'focus']
}) => {
  const lastUpdateRef = useRef<number>(0);
  const isInitializedRef = useRef<boolean>(false);

  // Throttled activity update function
  const updateActivity = useCallback(() => {
    const now = Date.now();
    if (now - lastUpdateRef.current >= throttleMs) {
      sessionManager.updateActivity();
      lastUpdateRef.current = now;
    }
  }, [throttleMs]);

  // Activity event handler
  const handleActivity = useCallback((event: Event) => {
    // Don't track activity from automated or system events
    if (event.isTrusted === false) {
      return;
    }

    updateActivity();
  }, [updateActivity]);

  // Initialize activity tracking
  useEffect(() => {
    if (isInitializedRef.current) {
      return;
    }

    isInitializedRef.current = true;

    // Add event listeners for activity tracking
    events.forEach(eventType => {
      document.addEventListener(eventType, handleActivity, {
        passive: true,
        capture: true
      });
    });

    // Track page visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        updateActivity();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Track window focus
    const handleWindowFocus = () => {
      updateActivity();
    };

    window.addEventListener('focus', handleWindowFocus);

    // Initial activity update
    updateActivity();

    // Cleanup function
    return () => {
      events.forEach(eventType => {
        document.removeEventListener(eventType, handleActivity);
      });
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [events, handleActivity, updateActivity]);

  return <>{children}</>;
};

export default ActivityTracker;
