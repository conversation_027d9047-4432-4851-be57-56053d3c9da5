import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';
import axios from 'axios';
import apiClient, { apiEndpoints } from '../../../utils/apiClient';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get token from cookies
    const cookies = parse(req.headers.cookie || '');
    const token = cookies['admin-token']; // Admin token required for user management

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { id } = req.query;
    
    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid user ID' });
    }    // Forward request to backend with appropriate method
    if (req.method === 'GET') {
      try {
        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.users.byId(id)}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        return res.status(200).json(response.data);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          return res.status(error.response.status).json(error.response.data);
        }
        throw error;
      }
    }    else if (req.method === 'PUT') {
      try {
        const response = await axios.put(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.users.byId(id)}`,
          req.body,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        return res.status(200).json(response.data);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          return res.status(error.response.status).json(error.response.data);
        }
        throw error;
      }
    }    else if (req.method === 'DELETE') {
      try {
        const response = await axios.delete(
          `${process.env.NEXT_PUBLIC_BACKEND_URL}${apiEndpoints.users.byId(id)}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        );

        return res.status(200).json(response.data);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
          return res.status(error.response.status).json(error.response.data);
        }
        throw error;
      }
    } 
    else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('User API error:', error);
    return res.status(500).json({ error: 'Failed to process request' });
  }
}
