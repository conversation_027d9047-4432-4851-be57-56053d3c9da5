#!/bin/bash

# CCALC iOS Project Setup Script
# This script sets up the iOS project for Xcode development

set -e

echo "🚀 Setting up CCALC iOS project for Xcode..."

# Change to iOS directory
cd "$(dirname "$0")"

# Check if we're in the correct directory
if [ ! -f "Podfile" ]; then
    echo "❌ Error: Podfile not found. Please run this script from the ios directory."
    exit 1
fi

# Check if node_modules exists in parent directory
if [ ! -d "../node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    cd ..
    npm install
    cd ios
fi

# Check for CocoaPods installation
if ! command -v pod &> /dev/null; then
    echo "❌ CocoaPods not found. Please install CocoaPods first:"
    echo "   sudo gem install cocoapods"
    echo "   Or visit: https://cocoapods.org"
    exit 1
fi

# Install CocoaPods dependencies
echo "🍫 Installing CocoaPods dependencies..."
pod install --repo-update

# Verify Xcode project structure
echo "🔍 Verifying project structure..."

required_files=(
    "CCALC.xcworkspace"
    "CCALC.xcodeproj/project.pbxproj"
    "CCALC.xcodeproj/xcshareddata/xcschemes/CCALC.xcscheme"
    "CCALC/AppDelegate.h"
    "CCALC/AppDelegate.mm"
    "CCALC/main.m"
    "CCALC/Info.plist"
    "CCALC/LaunchScreen.storyboard"
    "CCALC/CCLCVoice.h"
    "CCALC/CCLCVoice.m"
    "CCALC/Images.xcassets/Contents.json"
    ".xcode.env"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ] && [ ! -d "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ All required files are present!"
else
    echo "❌ Missing files:"
    printf '%s\n' "${missing_files[@]}"
    exit 1
fi

echo ""
echo "🎉 Setup complete! Your iOS project is now Xcode-ready."
echo ""
echo "📖 Next Steps:"
echo "1. Open CCALC.xcworkspace in Xcode (NOT .xcodeproj)"
echo "2. Select a device or simulator from the scheme selector"
echo "3. Press Cmd+R to build and run"
echo "4. Press Cmd+U to run tests"
echo ""
echo "🔧 For development:"
echo "- Use 'npm start' in the parent directory to start Metro bundler"
echo "- Use 'npx react-native run-ios' to build and run from command line"
echo ""
echo "⚠️  Important:"
echo "- Always use CCALC.xcworkspace, never CCALC.xcodeproj"
echo "- Make sure Metro bundler is running before launching the app"
echo "- For physical device testing, configure code signing in Xcode"
