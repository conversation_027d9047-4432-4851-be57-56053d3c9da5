#!/bin/bash

# Setup Admin with PPK Authentication
# This script automates the process of generating admin keys and updating the database
#
# Usage:
#   ./setup-admin-ppk.sh [username] [email]

set -e

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR/.."

# Default values
USERNAME=${1:-"admin"}
EMAIL=${2:-"${USERNAME}@ccalc.local"}

echo "==================== CCALC Admin PPK Setup ===================="
echo "This script will:"
echo "1. Generate admin PPK key pair"
echo "2. Update MongoDB with the public key"
echo "3. Enable PPK authentication for the admin"
echo ""
echo "Username: $USERNAME"
echo "Email: $EMAIL"
echo "=============================================================="
echo ""

# Confirm before proceeding
read -p "Continue? (y/n): " CONFIRM
if [[ $CONFIRM != "y" && $CONFIRM != "Y" ]]; then
  echo "Setup cancelled."
  exit 0
fi

# Step 1: Generate keys
echo ""
echo "Generating PPK keys..."
node scripts/generate-admin-keys.js "$USERNAME" "$EMAIL"

# Capture the path of the formatted public key file
echo ""
echo "Finding the generated formatted public key file..."
FORMATTED_KEY_FILE=$(find keys -name "dev_${USERNAME}_public_formatted_*.txt" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -f2- -d" ")

if [ -z "$FORMATTED_KEY_FILE" ]; then
  echo "Error: Could not find the formatted public key file."
  exit 1
fi

echo "Found formatted public key file: $FORMATTED_KEY_FILE"

# Step 2: Update MongoDB
echo ""
echo "Updating MongoDB with the public key..."
node scripts/update-admin-ppk.js "$USERNAME" "$FORMATTED_KEY_FILE"

echo ""
echo "==================== Setup Complete ===================="
echo "Admin '$USERNAME' has been set up with PPK authentication."
echo "You can now log in using both password and PPK key."
echo "The private key file is located in the keys directory."
echo "Make sure to keep it secure!"
echo "======================================================="
