import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import UserModel from '../../models/User';
import crypto from 'crypto';
import securityLogger from '../../utils/security-logger';
import { generateCsrfToken } from '../../utils/csrf-protection';

/**
 * User login controller - MOBILE APP ONLY
 * Blocks web access and only allows mobile device authentication
 * POST /api/auth/user/login
 */
export async function userLoginController(req: Request, res: Response): Promise<void> {
  try {
    // SECURITY: Check if this is a mobile device request
    const userAgent = req.get('User-Agent') || '';
    const deviceFingerprint = req.headers['x-device-fingerprint'] || req.body.deviceFingerprint;
    const bleDeviceId = req.headers['x-ble-device-id'] || req.body.bleUUID;
    
    // Block web browser access - Users must use mobile app only
    const isWebBrowser = /Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(userAgent);    if (isWebBrowser && (!deviceFingerprint || !bleDeviceId)) {
      securityLogger.logSecurityEvent({
        eventType: 'failed_login',
        userType: 'user',
        username: '',
        ipAddress: req.ip,
        userAgent: userAgent,
        success: false,
        reason: 'WEB_ACCESS_BLOCKED_FOR_USER_LOGIN',
        details: {
          hasDeviceFingerprint: !!deviceFingerprint,
          hasBleDeviceId: !!bleDeviceId,
          path: req.path
        }
      });
      
      res.status(403).json({
        error: 'Web access denied - Mobile app authentication required',
        code: 'WEB_ACCESS_DENIED',
        message: 'User and superuser accounts can only authenticate through the CCALC mobile application. Admin access is available at the admin portal.'
      });
      return;
    }
    
    const { username, expression, deviceFingerprint: bodyDeviceFingerprint, bleUUID } = req.body as {
      username: string;
      expression: string;
      deviceFingerprint: string;
      bleUUID: string;
    };

    // Use device fingerprint from header or body
    const finalDeviceFingerprint = deviceFingerprint || bodyDeviceFingerprint;

    if (!username || !expression || !finalDeviceFingerprint || !bleUUID) {
      res.status(400).json({ error: 'Missing required fields: username, expression, deviceFingerprint, bleUUID' });
      return;
    }

    // Rate limiting check
    const ipAddress = req.ip || req.socket.remoteAddress;
    
    const user = await UserModel.findOne({ username });
    
    if (!user) {
      // Use consistent timing to prevent username enumeration attacks
      await bcrypt.compare('dummy', '$2a$10$invalidhashfordummycomparison');
      securityLogger.logLoginAttempt(username, 'user', false, req, undefined, 'User not found');
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }    // Verify user role is not admin (admins must use admin panel)
    if (user.isSuperuser) {
      securityLogger.logSecurityEvent({
        eventType: 'failed_login',
        userType: 'admin',
        username: user.username,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        success: false,
        reason: 'ADMIN_ATTEMPTED_USER_LOGIN'
      });
      
      res.status(403).json({
        error: 'Admin accounts must use the admin panel',
        code: 'ADMIN_USE_ADMIN_PANEL',
        message: 'Admin accounts cannot authenticate through user endpoints. Please use the admin panel.'
      });
      return;
    }

    if (user.status !== 'active') {
      res.status(401).json({ error: 'Account is not active' });
      return;
    }

    // Check account lock status
    if (user.lockUntil && user.lockUntil > new Date()) {
      res.status(401).json({ 
        error: 'Account is temporarily locked due to too many failed attempts',
        lockedUntil: user.lockUntil
      });
      return;
    }

    // Verify expression (calculator-based authentication)
    const isExpressionValid = await bcrypt.compare(expression, user.expressionHash);
    if (!isExpressionValid) {
      // Increment failed login attempts
      user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
      
      // Lock account after too many failed attempts
      if (user.failedLoginAttempts >= 5) {
        user.status = 'locked';
        user.lockUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
        
        securityLogger.logAccountLockout(
          username,
          'user',
          req,
          (user as any)._id.toString()
        );
      }
      
      await user.save();
      
      securityLogger.logLoginAttempt(
        username,
        'user',
        false,
        req,
        (user as any)._id.toString(),
        'Invalid expression',
        { attemptCount: user.failedLoginAttempts }
      );
      
      res.status(401).json({ error: 'Authentication failed' });
      return;
    }

    // Verify device fingerprint
    const fingerprintHash = crypto.createHash('sha256')
      .update(finalDeviceFingerprint)
      .digest('hex');
      
    if (user.deviceFingerprintHash !== fingerprintHash) {
      securityLogger.logLoginAttempt(
        username,
        'user',
        false,
        req,
        (user as any)._id.toString(),
        'Unrecognized device'
      );
      
      res.status(401).json({ error: 'Unrecognized device - Please register this device first' });
      return;
    }

    // Verify BLE UUID
    const bleUUIDHash = crypto.createHash('sha256')
      .update(bleUUID)
      .digest('hex');
      
    if (user.bleUUIDHash !== bleUUIDHash) {
      securityLogger.logLoginAttempt(
        username,
        'user',
        false,
        req,
        (user as any)._id.toString(),
        'Unrecognized BLE device'
      );
      
      res.status(401).json({ error: 'Unrecognized BLE device - Please use authorized device' });
      return;
    }

    // Reset failed attempts and update last login
    user.failedLoginAttempts = 0;
    user.lastLoginAt = new Date();
    user.lockUntil = undefined;
    
    await user.save();

    // Log successful login
    securityLogger.logLoginAttempt(
      username,
      'user',
      true,
      req,
      (user as any)._id.toString(),
      undefined,
      { deviceType: 'mobile' }
    );    // Generate JWT token with mobile device claims
    const tokenPayload = {
      userId: (user as any)._id,
      username: user.username,
      isSuperuser: user.isSuperuser,
      type: 'user',
      deviceType: 'mobile',
      fingerprint: fingerprintHash.substring(0, 16),
      bleId: bleUUIDHash.substring(0, 16)
    };
      const token = jwt.sign(
      tokenPayload,
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: process.env.USER_TOKEN_EXPIRY || '24h' } as jwt.SignOptions
    );

    // Generate CSRF token for authenticated requests
    const csrfToken = generateCsrfToken((user as any)._id.toString());

    res.status(200).json({
      success: true,
      token,
      csrfToken,      user: {
        id: (user as any)._id,
        username: user.username,
        isSuperuser: user.isSuperuser,
        displayName: user.profile?.displayName,
        status: user.status
      },
      message: 'Mobile authentication successful'
    });
  } catch (error) {
    console.error('User login error:', error);
    securityLogger.logSecurityEvent({
      eventType: 'failed_login',
      userType: 'user',
      username: '',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      success: false,
      reason: 'USER_LOGIN_ERROR',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    });
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Generate device registration challenge for a user
 * POST /api/auth/user/device-challenge
 */
export async function deviceChallengeController(req: Request, res: Response): Promise<void> {
  try {
    const { username } = req.body;
    
    if (!username) {
      res.status(400).json({ error: 'Username is required' });
      return;
    }
    
    // Check if user exists
    const user = await UserModel.findOne({ username });
    
    // Generate a challenge regardless of whether user exists (to prevent enumeration)
    const challenge = crypto.randomBytes(32).toString('hex');
    
    if (!user) {
      // Still return a challenge but with a delay to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 500));
      res.status(200).json({ challenge });
      return;
    }
    
    res.status(200).json({
      challenge,
      requiresReset: user.status === 'locked' || !user.deviceFingerprintHash,
      message: 'Device challenge generated - mobile app required'
    });
    
  } catch (error) {
    console.error('Device challenge error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
