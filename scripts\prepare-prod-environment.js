// This script restores the original encrypted key for production use

const fs = require('fs');
const path = require('path');

// Path to key files
const keysDir = path.join(__dirname, '..', 'keys');
const encryptedKeyPath = path.join(keysDir, 'admin_private.pem');
const backupKeyPath = path.join(keysDir, 'admin_private.pem.bak');

// Check if backup exists
if (!fs.existsSync(backupKeyPath)) {
  console.error('Backup key file not found!');
  console.error(`Expected at: ${backupKeyPath}`);
  console.error('Cannot restore encrypted key for production.');
  process.exit(1);
}

// Restore encrypted key from backup
console.log('Restoring encrypted key for production use...');
fs.copyFileSync(backupKeyPath, encryptedKeyPath);

console.log('✅ Production environment prepared!');
console.log('Original encrypted key has been restored.');
console.log('NOTE: This key requires proper passphrase handling in production.');
