#!/bin/bash

# Get Local IP Address for iPhone Development
# This script helps you find your local IP address for backend connection

echo "🔍 Finding your local IP address for iPhone development..."
echo ""

# For Windows (using bash)
if command -v ipconfig >/dev/null 2>&1; then
    echo "🖥️ Windows IP Configuration:"
    ipconfig | grep -E "IPv4.*Address" | head -1
    LOCAL_IP=$(ipconfig | grep -oP '(?<=IPv4 Address.*: )[\d.]+' | head -1)
    
# For macOS/Linux
elif command -v ifconfig >/dev/null 2>&1; then
    echo "🍎 macOS/Linux IP Configuration:"
    ifconfig | grep "inet " | grep -v 127.0.0.1
    LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
fi

echo ""
echo "📱 For iPhone development, update these files:"
echo "1. 📄 app/src/config/app.config.ts"
echo "   Replace 'YOUR_LOCAL_IP' with: $LOCAL_IP"
echo ""
echo "2. 📄 app/src/services/AuthService.ts"
echo "   Update backendUrl to: http://$LOCAL_IP:3000"
echo ""
echo "🔗 Your backend will be accessible at: http://$LOCAL_IP:3000"
echo "🏠 Admin panel will be at: http://$LOCAL_IP:3001"
echo ""
echo "⚠️  Make sure your iPhone and computer are on the same WiFi network!"
echo ""
echo "📋 Quick setup commands:"
echo "sed -i 's/YOUR_LOCAL_IP/$LOCAL_IP/g' app/src/config/app.config.ts"
