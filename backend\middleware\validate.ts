// Joi validation middleware for Express
import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

export function validate(schema: <PERSON><PERSON>.Schema, target: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, res: Response, next: NextFunction): void => {
    const options: Joi.ValidationOptions = { 
      abortEarly: false, 
      allowUnknown: false, 
      stripUnknown: true 
    };
    
    let dataToValidate;
    switch (target) {
      case 'query':
        dataToValidate = req.query;
        break;
      case 'params':
        dataToValidate = req.params;
        break;
      default:
        dataToValidate = req.body;
    }
    
    const { error, value } = schema.validate(dataToValidate, options);
    if (error) {
      res.status(400).json({ 
        error: 'Validation error', 
        details: error.details.map(d => d.message) 
      });
      return;
    }
    
    // Update the appropriate request property
    switch (target) {
      case 'query':
        req.query = value;
        break;
      case 'params':
        req.params = value;
        break;
      default:
        req.body = value;
    }
    
    next();
  };
}

export default validate;
