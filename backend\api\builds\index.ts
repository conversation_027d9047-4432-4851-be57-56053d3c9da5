import { Router, Request, Response } from 'express';
import BuildModel, { IBuild } from '../../models/Build';
import UserModel from '../../models/User';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import crypto from 'crypto';
import { createReadStream, createWriteStream, existsSync, mkdirSync, writeFileSync, readFileSync } from 'fs';
import path from 'path';
import archiver from 'archiver';
import { exec } from 'child_process';
import { promisify } from 'util';

const router = Router();
const execAsync = promisify(exec);

interface BuildConfig {
  platform: 'android' | 'ios' | 'web';
  version: string;
  environment: 'development' | 'staging' | 'production';
  features: string[];
  security: {
    obfuscation: boolean;
    antiDebugging: boolean;
    rootDetection: boolean;
    certificatePinning: boolean;
  };
}

/**
 * Generate per-user secrets and configuration
 */
function generateUserSecrets(userId: string, buildId: string): any {
  const baseSecret = process.env.BUILD_MASTER_KEY || 'default-master-key';
  
  return {
    apiKey: crypto
      .createHash('sha256')
      .update(baseSecret + userId + 'api')
      .digest('hex'),
    encryptionSeed: crypto
      .createHash('sha256')
      .update(baseSecret + userId + 'encryption')
      .digest('hex'),
    deviceBinding: crypto
      .createHash('sha256')
      .update(baseSecret + userId + buildId + 'device')
      .digest('hex'),
    serverEndpoint: process.env.PROD_SERVER_ENDPOINT || 'https://api.ccalc.secure',
    buildId,
    userId,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    features: {
      maxDevices: 3,
      recordingEnabled: true,
      voiceModulation: true,
      ephemeralMessages: true
    }
  };
}

/**
 * Create obfuscated configuration file
 */
function createObfuscatedConfig(secrets: any, obfuscationLevel: number = 3): string {
  let config = JSON.stringify(secrets);
    // Multiple layers of obfuscation
  for (let i = 0; i < obfuscationLevel; i++) {
    const key = crypto.randomBytes(32); // 32 bytes for aes-256
    const iv = crypto.randomBytes(16);  // 16 bytes IV
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    config = cipher.update(config, 'utf8', 'hex') + cipher.final('hex');
    config = key.toString('hex') + '::' + iv.toString('hex') + '::' + config;
  }
  
  return Buffer.from(config).toString('base64');
}

/**
 * Generate a new build for a user
 * POST /api/builds/generate
 */
router.post('/generate', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, platform, version, buildConfig, customFeatures } = req.body;
    const adminId = req.admin?.id;    if (!userId || !platform || !version) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: userId, platform, version'
      });
      return;
    }

    // Verify user exists
    const user = await UserModel.findById(userId);    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Check if user already has an active build for this platform
    const existingBuild = await BuildModel.findOne({
      userId,
      platform,
      status: { $in: ['active', 'pending'] }
    });    if (existingBuild) {
      res.status(400).json({
        success: false,
        error: 'User already has an active build for this platform'
      });
      return;
    }

    const buildId = crypto.randomUUID();
    const userSecrets = generateUserSecrets(userId, buildId);
    
    // Create build record
    const build = await BuildModel.create({
      buildId,
      userId,
      platform,
      version,
      status: 'pending',
      generatedBy: adminId,
      generatedAt: new Date(),
      configuration: {
        ...buildConfig,
        customFeatures: customFeatures || [],
        secrets: {
          hashedKey: crypto.createHash('sha256').update(userSecrets.apiKey).digest('hex'),
          keyFingerprint: userSecrets.apiKey.substring(0, 8)
        }
      },
      security: {
        obfuscation: buildConfig?.security?.obfuscation || true,
        antiDebugging: buildConfig?.security?.antiDebugging || true,
        rootDetection: buildConfig?.security?.rootDetection || true,
        certificatePinning: buildConfig?.security?.certificatePinning || true,
        buildSignature: crypto.randomBytes(32).toString('hex')
      },
      expiry: {
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        autoRevoke: true
      },
      distribution: {
        maxDownloads: 5,
        downloadCount: 0,
        allowedIPs: [],
        requireAuth: true
      }
    });

    // Generate build directory structure
    const buildDir = path.join(process.cwd(), 'builds', buildId);
    if (!existsSync(buildDir)) {
      mkdirSync(buildDir, { recursive: true });
    }

    // Create obfuscated config file
    const obfuscatedConfig = createObfuscatedConfig(userSecrets, 3);
    const configPath = path.join(buildDir, 'config.enc');
    writeFileSync(configPath, obfuscatedConfig);

    // Create build manifest
    const manifest = {
      buildId,
      userId,
      platform,
      version,
      generatedAt: new Date().toISOString(),
      signature: build.security.signed ? 'Valid' : 'Unsigned',
      checksum: crypto.createHash('sha256').update(obfuscatedConfig).digest('hex')
    };
    
    writeFileSync(
      path.join(buildDir, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );

    // Start build process (mock implementation)
    setTimeout(async () => {
      try {
        // Simulate build process
        await BuildModel.findByIdAndUpdate(build._id, {
          status: 'building',
          'buildProcess.startedAt': new Date()
        });

        // Create mock build files
        const buildFiles = {
          'calculator.apk': 'mock-android-build-content',
          'calculator.ipa': 'mock-ios-build-content',
          'calculator.zip': 'mock-web-build-content'
        };

        const buildFile = buildFiles[`calculator.${platform === 'android' ? 'apk' : platform === 'ios' ? 'ipa' : 'zip'}`];
        writeFileSync(path.join(buildDir, `calculator-${platform}.${platform === 'android' ? 'apk' : platform === 'ios' ? 'ipa' : 'zip'}`), buildFile);

        // Update build status
        await BuildModel.findByIdAndUpdate(build._id, {
          status: 'completed',
          'buildProcess.completedAt': new Date(),
          'buildProcess.artifacts': [`calculator-${platform}.${platform === 'android' ? 'apk' : platform === 'ios' ? 'ipa' : 'zip'}`],
          'buildProcess.buildSize': Buffer.byteLength(buildFile),
          fileHash: crypto.createHash('sha256').update(buildFile).digest('hex')
        });

        await AuditLogModel.create({
          action: 'build_completed',
          userId: adminId,
          details: { buildId, userId, platform, version },
          severity: 'low',
          category: 'build_management'
        });

      } catch (error) {
        await BuildModel.findByIdAndUpdate(build._id, {
          status: 'failed',
          'buildProcess.failedAt': new Date(),
          'buildProcess.error': (error as Error).message
        });
      }
    }, 5000); // 5 second delay to simulate build time

    await AuditLogModel.create({
      action: 'build_initiated',
      userId: adminId,
      details: { buildId, userId, platform, version },
      severity: 'low',
      category: 'build_management'
    });

    res.json({
      success: true,
      build: {
        buildId: build.buildId,
        status: build.buildProcess.status,
        estimatedCompletion: new Date(Date.now() + 60000) // 1 minute
      }
    });

  } catch (error) {
    console.error('Build generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate build'
    });
  }
});

/**
 * Get build status
 * GET /api/builds/:buildId/status
 */
router.get('/:buildId/status', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { buildId } = req.params;
    const userId = req.user?.id;
    const isAdmin = req.admin?.id;    const build = await BuildModel.findOne({ buildId });
    if (!build) {
      res.status(404).json({
        success: false,
        error: 'Build not found'
      });
      return;
    }

    // Check authorization (user can only see their own builds unless admin)
    if (!isAdmin && build.userId !== userId) {
      res.status(403).json({
        success: false,
        error: 'Not authorized to view this build'
      });
      return;
    }

    const buildData = build.toObject();
      // Remove sensitive data for non-admin users
    if (!isAdmin) {
      delete (buildData as any).secrets;
      delete (buildData as any).security.certificate;
    }

    res.json({
      success: true,
      build: buildData
    });

  } catch (error) {
    console.error('Build status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch build status'
    });
  }
});

/**
 * Download build file
 * GET /api/builds/:buildId/download
 */
router.get('/:buildId/download', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { buildId } = req.params;
    const userId = req.user?.id;
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    const build = await BuildModel.findOne({ buildId });
    if (!build) {
      res.status(404).json({
        success: false,
        error: 'Build not found'
      });

      return;
    }

    // Check authorization
    if (build.userId !== userId) {
      await AuditLogModel.create({
        action: 'unauthorized_build_download',
        userId,
        details: { buildId, actualUserId: build.userId, clientIP },
        severity: 'high',
        category: 'security'
      });

      res.status(403).json({
        success: false,
        error: 'Not authorized to download this build'
      });


      return;
    }    // Check build status
    if (build.buildProcess.status !== 'completed') {
      res.status(400).json({
        success: false,
        error: `Build is not ready for download. Status: ${build.buildProcess.status}`
      });

      return;
    }    // Check expiry
    if (build.metadata.expiresAt && new Date() > build.metadata.expiresAt) {
      res.status(410).json({
        success: false,
        error: 'Build has expired'
      });

      return;
    }    // Check download limits
    if (build.usage.downloadCount >= build.security.maxInstalls) {
      res.status(429).json({
        success: false,
        error: 'Download limit exceeded'
      });

      return;
    }    // Check IP restrictions (using deviceUDIDs as proxy)
    if (build.security.deviceUDIDs && build.security.deviceUDIDs.length > 0) {
      // For now, allow all IPs - this would need proper IP restriction logic
      // !build.security.deviceUDIDs.includes(clientIP)
    }

    // Find build file
    const buildDir = path.join(process.cwd(), 'builds', buildId);    const fileName = `calculator-${build.config.platform}.${build.config.platform === 'android' ? 'apk' : build.config.platform === 'ios' ? 'ipa' : 'zip'}`;
    const filePath = path.join(buildDir, fileName);

    if (!existsSync(filePath)) {
      res.status(404).json({
        success: false,
        error: 'Build file not found'
      });

      return;
    }    // Update download count
    await BuildModel.findByIdAndUpdate(build._id, {
      $inc: { 'usage.downloadCount': 1 },
      $push: {
        'usage.downloadIPs': clientIP
      }
    });

    await AuditLogModel.create({
      action: 'build_downloaded',
      userId,
      details: { buildId, platform: build.config.platform, clientIP },
      severity: 'low',
      category: 'build_management'
    });

    // Set appropriate headers
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    
    // Stream the file
    const fileStream = createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('Build download error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to download build'
    });
  }
});

/**
 * Revoke a build
 * POST /api/builds/:buildId/revoke
 */
router.post('/:buildId/revoke', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { buildId } = req.params;
    const { reason } = req.body;
    const adminId = req.admin?.id;

    const build = await BuildModel.findOne({ buildId });
    if (!build) {
      res.status(404).json({
        success: false,
        error: 'Build not found'
      });

      return;
    }

    if (build.buildProcess.status === 'revoked') {
      res.status(400).json({
        success: false,
        error: 'Build is already revoked'
      });

      return;
    }

    await BuildModel.findByIdAndUpdate(build._id, {
      status: 'revoked',
      revokedAt: new Date(),
      revokedBy: adminId,
      revocationReason: reason || 'admin_revoked'
    });

    await AuditLogModel.create({
      action: 'build_revoked',
      userId: adminId,
      details: { buildId, userId: build.userId, reason },
      severity: 'medium',
      category: 'build_management'
    });

    res.json({
      success: true,
      message: 'Build revoked successfully'
    });

  } catch (error) {
    console.error('Build revocation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to revoke build'
    });
  }
});

/**
 * Get user's builds
 * GET /api/builds/user/:userId
 */
router.get('/user/:userId', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10, status, platform } = req.query;

    const query: any = { userId };
    if (status) query.status = status;
    if (platform) query.platform = platform;

    const builds = await BuildModel.find(query)
      .select('-configuration.secrets -security.buildSignature')
      .sort({ generatedAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit));

    const total = await BuildModel.countDocuments(query);

    res.json({
      success: true,
      builds,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('User builds fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user builds'
    });
  }
});

/**
 * Get build analytics
 * GET /api/builds/analytics
 */
router.get('/analytics', authenticateToken, async (req: Request, res: Response) => {
  try {
    const { period = '30d' } = req.query;
    
    let dateFilter: Date;
    switch (period) {
      case '7d':
        dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const analytics = await BuildModel.aggregate([
      {
        $match: {
          generatedAt: { $gte: dateFilter }
        }
      },
      {
        $group: {
          _id: null,
          totalBuilds: { $sum: 1 },
          completedBuilds: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          failedBuilds: {
            $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
          },
          revokedBuilds: {
            $sum: { $cond: [{ $eq: ['$status', 'revoked'] }, 1, 0] }
          },
          totalDownloads: { $sum: '$distribution.downloadCount' },
          platformBreakdown: {
            $push: {
              platform: '$platform',
              status: '$status'
            }
          }
        }
      }
    ]);

    // Platform breakdown
    const platformStats = await BuildModel.aggregate([
      {
        $match: {
          generatedAt: { $gte: dateFilter }
        }
      },
      {
        $group: {
          _id: '$platform',
          count: { $sum: 1 },
          completed: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          downloads: { $sum: '$distribution.downloadCount' }
        }
      }
    ]);

    // Daily build trends
    const dailyTrends = await BuildModel.aggregate([
      {
        $match: {
          generatedAt: { $gte: dateFilter }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$generatedAt'
            }
          },
          builds: { $sum: 1 },
          downloads: { $sum: '$distribution.downloadCount' }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    res.json({
      success: true,
      analytics: {
        summary: analytics[0] || {
          totalBuilds: 0,
          completedBuilds: 0,
          failedBuilds: 0,
          revokedBuilds: 0,
          totalDownloads: 0
        },
        platformStats,
        dailyTrends,
        period
      }
    });

  } catch (error) {
    console.error('Build analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch build analytics'
    });
  }
});

/**
 * Update build distribution settings
 * PUT /api/builds/:buildId/distribution
 */
router.put('/:buildId/distribution', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { buildId } = req.params;
    const { maxDownloads, allowedIPs, requireAuth } = req.body;
    const adminId = req.admin?.id;

    const build = await BuildModel.findOne({ buildId });
    if (!build) {
      res.status(404).json({
        success: false,
        error: 'Build not found'
      });

      return;
    }

    const updates: any = {};
    if (maxDownloads !== undefined) updates['distribution.maxDownloads'] = maxDownloads;
    if (allowedIPs !== undefined) updates['distribution.allowedIPs'] = allowedIPs;
    if (requireAuth !== undefined) updates['distribution.requireAuth'] = requireAuth;

    await BuildModel.findByIdAndUpdate(build._id, updates);

    await AuditLogModel.create({
      action: 'build_distribution_updated',
      userId: adminId,
      details: { buildId, updates },
      severity: 'low',
      category: 'build_management'
    });

    res.json({
      success: true,
      message: 'Distribution settings updated successfully'
    });

  } catch (error) {
    console.error('Build distribution update error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update distribution settings'
    });
  }
});

export default router;
