/**
 * Cookie-based Authentication Session API
 * Provides secure session management with HttpOnly cookies
 */
import type { NextApiRequest, NextApiResponse } from 'next';
import ServerApiClient from '../../../utils/serverApiClient';

interface SessionResponse {
  authenticated: boolean;
  userType?: 'admin' | 'user';
  user?: any;
  csrfToken?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<SessionResponse>) {
  if (req.method !== 'GET') {
    return res.status(405).json({ authenticated: false });
  }

  try {
    const serverClient = new ServerApiClient();
    
    // Forward cookies from the request to the backend
    const cookieHeader = req.headers.cookie;
    
    const response = await serverClient.get('/api/auth/session', {
      headers: {
        ...(cookieHeader && { Cookie: cookieHeader }),
      },
    });

    if (!response.ok) {
      return res.status(200).json({ authenticated: false });
    }

    const sessionData = await response.json();
    
    // Forward Set-Cookie headers from backend if any
    const setCookieHeaders = response.headers.get('set-cookie');
    if (setCookieHeaders) {
      res.setHeader('Set-Cookie', setCookieHeaders);
    }

    return res.status(200).json({
      authenticated: true,
      userType: sessionData.userType,
      user: sessionData.user,
      csrfToken: sessionData.csrfToken,
    });

  } catch (error: any) {
    console.error('Session check error:', error);
    return res.status(200).json({ authenticated: false });
  }
}
