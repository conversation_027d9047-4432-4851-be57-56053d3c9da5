import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import DataTable from '../../components/admin/DataTable';
import Button from '../../components/admin/Button';
import StatCard from '../../components/admin/StatCard';
import { apiClient } from '../../utils/axiosClient';
import withAdminAuth from '../../components/hoc/withAdminAuth';

interface ChatMessage {
  messageId: string;
  chatId: string;
  senderId: string;
  recipientId: string;
  content: {
    type: 'text' | 'media';
    text: string;
    timestamp: number;
  };
  metadata: {
    sentAt: string;
    platform: string;
    deviceType: string;
  };
  status: 'sent' | 'delivered' | 'read' | 'failed';
  isEncrypted: boolean;
  flaggedForReview?: boolean;
  adminNotes?: string;
}

interface ChatAnalytics {
  totalMessages: number;
  totalChats: number;
  messagesLast24h: number;
  flaggedMessages: number;
  encryptedMessages: number;
  averageMessageLength: number;
}

export default withAdminAuth(function ChatManagement() {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [analytics, setAnalytics] = useState<ChatAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    senderId: '',
    status: '',
    flagged: '',
    dateFrom: '',
    dateTo: '',
    encrypted: ''
  });
  const [selectedChat, setSelectedChat] = useState<string | null>(null);

  useEffect(() => {
    fetchChatMessages();
    fetchChatAnalytics();
  }, [currentPage, filters]);

  const fetchChatMessages = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '50',
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await apiClient.backend.get(`/api/chat/admin/messages?${params}`);
      const data = response.data.data;

      setMessages(data.messages);
      setTotalPages(data.pagination.pages);
      setError('');
    } catch (err: any) {
      console.error('Chat messages fetch error:', err);
      setError(err.response?.data?.error || 'Failed to load chat messages');
    } finally {
      setLoading(false);
    }
  };

  const fetchChatAnalytics = async () => {
    try {
      const response = await apiClient.backend.get('/api/chat/admin/analytics');
      setAnalytics(response.data.data);
    } catch (error) {
      console.error('Chat analytics fetch error:', error);
    }
  };

  const flagMessage = async (messageId: string, flagged: boolean, notes?: string) => {
    try {
      await apiClient.backend.post(`/api/chat/admin/flag/${messageId}`, {
        flagged,
        adminNotes: notes
      });

      // Refresh messages
      fetchChatMessages();
    } catch (error) {
      console.error('Flag message error:', error);
      alert('Failed to update message flag');
    }
  };

  const viewFullConversation = async (chatId: string) => {
    try {
      const response = await apiClient.backend.get(`/api/chat/admin/conversation/${chatId}`);
      const conversation = response.data.data;

      // Create modal to show full conversation
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
      
      const messagesHtml = conversation.map((msg: ChatMessage) => `
        <div class="mb-3 p-3 border rounded ${msg.senderId === 'superuser' ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'}">
          <div class="flex justify-between items-start mb-2">
            <span class="font-semibold text-sm">${msg.senderId}</span>
            <span class="text-xs text-gray-500">${new Date(msg.metadata.sentAt).toLocaleString()}</span>
          </div>
          <div class="text-sm">${msg.content.text}</div>
          <div class="text-xs text-gray-400 mt-1">
            Status: ${msg.status} | Platform: ${msg.metadata.platform} | Encrypted: ${msg.isEncrypted ? 'Yes' : 'No'}
          </div>
          ${msg.flaggedForReview ? '<div class="text-xs text-red-600 mt-1">🚩 Flagged for review</div>' : ''}
        </div>
      `).join('');

      modal.innerHTML = `
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-full overflow-hidden flex flex-col">
          <div class="p-4 border-b flex justify-between items-center">
            <h3 class="text-lg font-semibold">Chat Conversation: ${chatId}</h3>
            <button class="text-gray-500 hover:text-gray-700" onclick="this.closest('.fixed').remove()">✕</button>
          </div>
          <div class="p-4 overflow-y-auto flex-1">
            ${messagesHtml}
          </div>
          <div class="p-4 border-t">
            <button class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" onclick="this.closest('.fixed').remove()">
              Close
            </button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
    } catch (error) {
      console.error('View conversation error:', error);
      alert('Failed to load conversation');
    }
  };

  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'delivered': return 'text-green-600';
      case 'read': return 'text-blue-600';
      case 'sent': return 'text-yellow-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };
  const columns = [
    {
      header: 'Message ID',
      accessor: (message: ChatMessage) => (
        <div className="font-mono text-xs">
          {message.messageId.slice(0, 12)}...
        </div>
      )
    },
    {
      header: 'Participants',
      accessor: (message: ChatMessage) => (
        <div className="text-sm">
          <div><strong>From:</strong> {message.senderId}</div>
          <div><strong>To:</strong> {message.recipientId}</div>
          <button
            onClick={() => viewFullConversation(message.chatId)}
            className="text-blue-600 hover:text-blue-800 text-xs mt-1"
          >
            View Chat →
          </button>
        </div>
      )
    },
    {
      header: 'Message Content',
      accessor: (message: ChatMessage) => (
        <div className="text-sm max-w-xs">
          <div className="truncate mb-1">{message.content.text}</div>
          <div className="text-xs text-gray-500">
            Type: {message.content.type} | Length: {message.content.text.length} chars
          </div>
          {message.isEncrypted && (
            <div className="text-xs text-green-600">🔒 Encrypted</div>
          )}
        </div>
      )
    },
    {
      header: 'Timing & Status',
      accessor: (message: ChatMessage) => (
        <div className="text-sm">
          <div>{formatDateTime(message.metadata.sentAt)}</div>
          <div className={`font-semibold ${getStatusColor(message.status)}`}>
            {message.status.toUpperCase()}
          </div>
          <div className="text-xs text-gray-500">
            {message.metadata.platform} - {message.metadata.deviceType}
          </div>
        </div>
      )
    },
    {
      header: 'Admin Actions',
      accessor: (message: ChatMessage) => (
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant={message.flaggedForReview ? 'secondary' : 'danger'}
              onClick={() => flagMessage(message.messageId, !message.flaggedForReview)}
            >
              {message.flaggedForReview ? '✓ Flagged' : '🚩 Flag'}
            </Button>
          </div>
          {message.flaggedForReview && message.adminNotes && (
            <div className="text-xs text-gray-600 mt-1">
              Notes: {message.adminNotes.slice(0, 30)}...
            </div>
          )}
        </div>
      )
    }
  ];

  return (
    <AdminLayout>
      <Head>
        <title>Chat Management - CCALC Admin</title>
      </Head>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Chat Management</h1>
          <Button onClick={fetchChatMessages} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <StatCard
              title="Total Messages"
              value={analytics.totalMessages.toString()}
              icon={<span>💬</span>}
              color="#3B82F6"
              subtitle="All messages"
              trend={{ value: 0, isPositive: true }}
            />
            <StatCard
              title="Active Chats"
              value={analytics.totalChats.toString()}
              icon={<span>👥</span>}
              color="#10B981"
              subtitle="Unique conversations"
              trend={{ value: 0, isPositive: true }}
            />
            <StatCard
              title="Last 24h"
              value={analytics.messagesLast24h.toString()}
              icon={<span>📈</span>}
              color="#8B5CF6"
              subtitle="Recent messages"
              trend={{ value: 15, isPositive: true }}
            />
            <StatCard
              title="Flagged"
              value={analytics.flaggedMessages.toString()}
              icon={<span>🚩</span>}
              color={analytics.flaggedMessages > 0 ? "#EF4444" : "#10B981"}
              subtitle="For review"
              trend={{ value: analytics.flaggedMessages, isPositive: analytics.flaggedMessages === 0 }}
            />
            <StatCard
              title="Encrypted"
              value={analytics.encryptedMessages.toString()}
              icon={<span>🔒</span>}
              color="#10B981"
              subtitle={`${analytics.totalMessages > 0 ? Math.round((analytics.encryptedMessages / analytics.totalMessages) * 100) : 0}% encrypted`}
              trend={{ value: Math.round((analytics.encryptedMessages / analytics.totalMessages) * 100), isPositive: true }}
            />
            <StatCard
              title="Avg Length"
              value={Math.round(analytics.averageMessageLength).toString()}
              icon={<span>📝</span>}
              color="#F59E0B"
              subtitle="Characters per message"
              trend={{ value: 0, isPositive: true }}
            />
          </div>
        )}

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-3">Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sender</label>
              <input
                type="text"
                value={filters.senderId}
                onChange={(e) => setFilters({...filters, senderId: e.target.value})}
                placeholder="User ID"
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">All Statuses</option>
                <option value="sent">Sent</option>
                <option value="delivered">Delivered</option>
                <option value="read">Read</option>
                <option value="failed">Failed</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Flagged</label>
              <select
                value={filters.flagged}
                onChange={(e) => setFilters({...filters, flagged: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">All Messages</option>
                <option value="true">Flagged Only</option>
                <option value="false">Not Flagged</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Encrypted</label>
              <select
                value={filters.encrypted}
                onChange={(e) => setFilters({...filters, encrypted: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">All Messages</option>
                <option value="true">Encrypted Only</option>
                <option value="false">Not Encrypted</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => setFilters({...filters, dateFrom: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => setFilters({...filters, dateTo: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
          </div>
        </div>

        {/* Messages Table */}
        <div className="bg-white rounded-lg shadow">          <DataTable
            data={messages}
            columns={columns}
            keyExtractor={(message) => message.messageId}
            isLoading={loading}
            emptyMessage="No chat messages found"
          />
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-4 py-4">
              <Button
                variant="secondary"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="secondary"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
});
