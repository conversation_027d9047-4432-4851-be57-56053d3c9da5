// Security audit logging utility for authentication events
import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Security events logger
const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'security-audit' },
  transports: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'security-audit.log'),
      maxsize: ********, // 10MB
      maxFiles: 10, 
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
      level: process.env.NODE_ENV === 'production' ? 'error' : 'info'
    })
  ]
});

export interface SecurityEvent {
  eventType: 'login' | 'logout' | 'failed_login' | 'admin_access' | 'token_revoked' | 'account_locked' | 'security_change' | 'device_registration';
  userType: 'admin' | 'user';
  username: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  reason?: string;
  details?: Record<string, any>;
}

export const logSecurityEvent = (event: SecurityEvent): void => {
  // Remove sensitive data from details
  if (event.details) {
    // Remove password, token, etc.
    const { password, token, ...safeDetails } = event.details as any;
    event.details = safeDetails;
  }

  securityLogger.info('Security event', event);
};

// Special handler for login attempts
export const logLoginAttempt = (
  username: string, 
  userType: 'admin' | 'user',
  success: boolean,
  req: any,
  userId?: string,
  reason?: string,
  details?: Record<string, any>
): void => {
  logSecurityEvent({
    eventType: success ? 'login' : 'failed_login',
    userType,
    username,
    userId,
    ipAddress: req.ip || req.socket.remoteAddress,
    userAgent: req.headers['user-agent'],
    success,
    reason,
    details
  });
};

// Log account lockout events
export const logAccountLockout = (
  username: string,
  userType: 'admin' | 'user',
  req: any,
  userId?: string,
  reason: string = 'Too many failed login attempts',
  details?: Record<string, any>
): void => {
  logSecurityEvent({
    eventType: 'account_locked',
    userType,
    username,
    userId,
    ipAddress: req.ip || req.socket.remoteAddress,
    userAgent: req.headers['user-agent'],
    success: true,
    reason,
    details
  });
};

// Log data access events
export const logDataAccess = (
  resource: string,
  action: string,
  userId: string,
  req: any,
  result: 'success' | 'failure' | 'blocked' = 'success',
  details?: Record<string, any>
): void => {
  logSecurityEvent({
    eventType: 'admin_access', // Using existing event type
    userType: 'user',
    username: userId,
    userId,
    ipAddress: req.ip || req.socket.remoteAddress,
    userAgent: req.headers['user-agent'],
    success: result === 'success',
    reason: `Data access: ${action} on ${resource}`,
    details: { resource, action, result, ...details }
  });
};

// Log security violations
export const logSecurityViolation = (
  violationType: string,
  userId: string,
  req: any,
  details?: Record<string, any>
): void => {
  logSecurityEvent({
    eventType: 'failed_login', // Using existing event type for violations
    userType: 'user',
    username: userId,
    userId,
    ipAddress: req.ip || req.socket.remoteAddress,
    userAgent: req.headers['user-agent'],
    success: false,
    reason: `Security violation: ${violationType}`,
    details: { violationType, ...details }
  });
};

export default {
  logSecurityEvent,
  logLoginAttempt,
  logAccountLockout,
  logDataAccess,
  logSecurityViolation
};
