import { Router, Request, Response } from 'express';
import DeviceModel, { IDevice } from '../../models/Device';
import { authenticateToken } from '../../middleware/auth';
import AuditLogModel from '../../models/AuditLog';
import crypto from 'crypto';
import UAParser from 'ua-parser-js';
import geoip from 'geoip-lite';

const router = Router();

/**
 * Generate device fingerprint from request headers and device info
 */
function generateDeviceFingerprint(req: Request, deviceInfo: any): string {
  const ua = new UAParser(req.headers['user-agent']);
  const components = [
    ua.getUA(),    (ua.getBrowser()?.name || 'Unknown') + (ua.getBrowser()?.version || ''),
    (ua.getOS()?.name || 'Unknown') + (ua.getOS()?.version || ''),
    ua.getDevice().model || 'unknown',
    req.headers['accept-language'],
    req.headers['accept-encoding'],
    deviceInfo.screenResolution || '',
    deviceInfo.timezone || '',
    deviceInfo.hardwareSpecs || '',
    deviceInfo.installedFonts || ''
  ];

  return crypto
    .createHash('sha256')
    .update(components.join('|'))
    .digest('hex');
}

/**
 * Calculate device risk score based on various factors
 */
function calculateRiskScore(device: IDevice, clientIP: string): number {
  let riskScore = 0;

  // Geographic risk
  const geo = geoip.lookup(clientIP);
  if (geo) {
    // High-risk countries (example list)
    const highRiskCountries = ['CN', 'RU', 'KP', 'IR'];
    if (highRiskCountries.includes(geo.country)) {
      riskScore += 30;
    }
  }
  // Security violations
  if (device.security) {
    if (device.security.violations?.length > 0) {
      riskScore += device.security.violations.length * 15;
    }
  }

  // Device age and usage patterns
  const daysSinceFirstSeen = Math.floor(
    (Date.now() - device.metadata.registeredAt.getTime()) / (1000 * 60 * 60 * 24)
  );
  
  if (daysSinceFirstSeen < 1) {
    riskScore += 20; // New device
  } else if (daysSinceFirstSeen < 7) {
    riskScore += 10; // Recent device
  }

  // BLE verification status
  if (!device.ble.verified) {
    riskScore += 25;
  }

  return Math.min(100, riskScore);
}

/**
 * Register a new device
 * POST /api/device/register
 */
router.post('/register', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      deviceName,
      deviceInfo,
      bleIdentifier,
      publicKey,
      hardwareSpecs
    } = req.body;
    const userId = req.user?.id;
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    if (!deviceName || !deviceInfo || !bleIdentifier) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: deviceName, deviceInfo, bleIdentifier'
      });

      return;
    }

    // Generate device fingerprint
    const fingerprint = generateDeviceFingerprint(req, deviceInfo);

    // Check if device already exists
    const existingDevice = await DeviceModel.findOne({
      $or: [
        { fingerprint },
        { bleIdentifier },
        { userId, deviceName }
      ]
    });

    if (existingDevice) {      // Update existing device
      existingDevice.metadata.lastActiveAt = new Date();
      existingDevice.network.lastKnownIPs.push(clientIP);
      existingDevice.network.connectionHistory.push({
        ip: clientIP,
        connectedAt: new Date(),
        location: geoip.lookup(clientIP) || undefined
      });
      existingDevice.metadata = { ...existingDevice.metadata, ...deviceInfo };
      
      await existingDevice.save();

      await AuditLogModel.create({
        action: 'device_updated',
        userId,
        deviceId: (existingDevice._id as any).toString(),
        details: { deviceName, fingerprint, clientIP },
        severity: 'low',
        category: 'device_management'
      });

      res.json({
        success: true,
        device: {
          deviceId: existingDevice._id,          isVerified: existingDevice.ble.verified,
          challenge: existingDevice.security.challenges[0]?.challengeId || null,
          riskScore: calculateRiskScore(existingDevice, clientIP)
        }
      });


      return;
    }

    // Create new device
    const challenge = crypto.randomBytes(16).toString('hex');
    const device = await DeviceModel.create({
      userId,
      deviceName,
      fingerprint,
      bleIdentifier,
      publicKey,
      deviceInfo: {
        ...deviceInfo,
        userAgent: req.headers['user-agent'],
        hardwareSpecs
      },
      ipAddresses: [{
        ip: clientIP,
        timestamp: new Date(),
        location: geoip.lookup(clientIP) || undefined
      }],
      firstSeen: new Date(),
      lastSeen: new Date(),
      lastChallenge: challenge,
      isVerified: false,
      status: 'pending_verification',
      securityMetrics: {
        successfulChallenges: 0,
        failedChallenges: 0,
        violations: [],
        suspiciousActivity: []
      }
    });

    const riskScore = calculateRiskScore(device, clientIP);

    await AuditLogModel.create({
      action: 'device_registered',
      userId,
      deviceId: (device._id as any).toString(),
      details: { 
        deviceName, 
        fingerprint, 
        clientIP, 
        riskScore,
        bleIdentifier: bleIdentifier.substring(0, 8) + '...' // Partial for security
      },
      severity: 'low',
      category: 'device_management'
    });

    res.json({
      success: true,
      device: {
        deviceId: device._id,
        challenge,
        riskScore,
        requiresVerification: true
      }
    });

  } catch (error) {
    console.error('Device registration error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to register device'
    });
  }
});

/**
 * Verify device with BLE challenge
 * POST /api/device/verify
 */
router.post('/verify', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId, challengeResponse, deviceSignature } = req.body;
    const userId = req.user?.id;

    if (!deviceId || !challengeResponse) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: deviceId, challengeResponse'
      });

      return;
    }

    const device = await DeviceModel.findOne({ _id: deviceId, userId });
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Device not found'
      });

      return;
    }

    // Verify challenge response
    const expectedResponse = crypto
      .createHash('sha256')
      .update(device.ble.uuid + (device.security.challenges[0]?.challengeId || '') + userId)
      .digest('hex');

    if (challengeResponse !== expectedResponse) {
      // Record failed verification
      await DeviceModel.findByIdAndUpdate(deviceId, {
        $inc: { 'securityMetrics.failedChallenges': 1 },
        $push: {
          'securityMetrics.violations': {
            type: 'failed_verification',
            timestamp: new Date(),
            details: { attemptedResponse: challengeResponse }
          }
        },
        lastFailedChallenge: new Date()
      });

      await AuditLogModel.create({
        action: 'device_verification_failed',
        userId,
        deviceId,
        details: { reason: 'invalid_challenge_response' },
        severity: 'medium',
        category: 'security'
      });

      res.status(401).json({
        success: false,
        error: 'Challenge verification failed'
      });


      return;
    }

    // Verify device signature if provided
    if (deviceSignature && device.fingerprint.hash) {
      // Implementation would depend on your specific signature scheme
      // This is a placeholder for cryptographic signature verification
    }

    // Update device as verified
    const newChallenge = crypto.randomBytes(16).toString('hex');
    await DeviceModel.findByIdAndUpdate(deviceId, {
      isVerified: true,
      status: 'active',
      verifiedAt: new Date(),
      lastSuccessfulAuth: new Date(),
      lastChallenge: newChallenge,
      $inc: { 'securityMetrics.successfulChallenges': 1 }
    });

    await AuditLogModel.create({
      action: 'device_verified',
      userId,
      deviceId,
      details: { verificationMethod: 'ble_challenge' },
      severity: 'low',
      category: 'device_management'
    });

    res.json({
      success: true,
      message: 'Device verified successfully',
      nextChallenge: newChallenge
    });

  } catch (error) {
    console.error('Device verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to verify device'
    });
  }
});

/**
 * Get user's registered devices
 * GET /api/device/list
 */
router.get('/list', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { status, includeInactive = false } = req.query;

    const query: any = { userId };
    if (status) query.status = status;
    if (!includeInactive) query.status = { $ne: 'deactivated' };

    const devices = await DeviceModel.find(query)
      .select('-publicKey -securityMetrics.violations -lastChallenge')
      .sort({ lastSeen: -1 });

    // Calculate current risk scores
    const devicesWithRisk = devices.map(device => ({
      ...device.toObject(),
      currentRiskScore: calculateRiskScore(device, 'current')
    }));

    res.json({
      success: true,
      devices: devicesWithRisk
    });

  } catch (error) {
    console.error('Device list error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch devices'
    });
  }
});

/**
 * Get device security details
 * GET /api/device/:deviceId/security
 */
router.get('/:deviceId/security', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const userId = req.user?.id;

    const device = await DeviceModel.findOne({ _id: deviceId, userId });
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Device not found'
      });

      return;
    }

    const clientIP = req.ip || 'unknown';
    const riskScore = calculateRiskScore(device, clientIP);

    res.json({
      success: true,
      security: {        isVerified: device.ble.verified,
        riskScore,
        lastSuccessfulAuth: device.metadata.lastActiveAt,
        securityMetrics: device.security,
        recentIPs: device.network.lastKnownIPs.slice(-5), // Last 5 IPs
        status: device.security.status
      }
    });

  } catch (error) {
    console.error('Device security details error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch device security details'
    });
  }
});

/**
 * Deactivate a device
 * POST /api/device/:deviceId/deactivate
 */
router.post('/:deviceId/deactivate', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { reason } = req.body;
    const userId = req.user?.id;

    const device = await DeviceModel.findOne({ _id: deviceId, userId });
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Device not found'
      });

      return;
    }

    await DeviceModel.findByIdAndUpdate(deviceId, {
      status: 'deactivated',
      deactivatedAt: new Date(),
      deactivationReason: reason || 'user_requested'
    });

    await AuditLogModel.create({
      action: 'device_deactivated',
      userId,
      deviceId,
      details: { reason: reason || 'user_requested' },
      severity: 'low',
      category: 'device_management'
    });

    res.json({
      success: true,
      message: 'Device deactivated successfully'
    });

  } catch (error) {
    console.error('Device deactivation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to deactivate device'
    });
  }
});

/**
 * Generate new BLE challenge for device
 * POST /api/device/:deviceId/challenge
 */
router.post('/:deviceId/challenge', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const userId = req.user?.id;

    const device = await DeviceModel.findOne({ 
      _id: deviceId, 
      userId, 
      status: 'active' 
    });

    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Active device not found'
      });

      return;
    }

    const newChallenge = crypto.randomBytes(16).toString('hex');
    await DeviceModel.findByIdAndUpdate(deviceId, {
      lastChallenge: newChallenge
    });

    await AuditLogModel.create({
      action: 'challenge_generated',
      userId,
      deviceId,
      details: { challengeId: crypto.createHash('sha256').update(newChallenge).digest('hex').substring(0, 8) },
      severity: 'low',
      category: 'device_management'
    });

    res.json({
      success: true,
      challenge: newChallenge
    });

  } catch (error) {
    console.error('Challenge generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate challenge'
    });
  }
});

/**
 * Report suspicious device activity
 * POST /api/device/:deviceId/report-suspicious
 */
router.post('/:deviceId/report-suspicious', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { deviceId } = req.params;
    const { activityType, description, evidence } = req.body;
    const userId = req.user?.id;

    if (!activityType) {
      res.status(400).json({
        success: false,
        error: 'Activity type is required'
      });

      return;
    }

    const device = await DeviceModel.findById(deviceId);
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'Device not found'
      });

      return;
    }

    // Add to suspicious activity log
    await DeviceModel.findByIdAndUpdate(deviceId, {
      $push: {
        'securityMetrics.suspiciousActivity': {
          type: activityType,
          description,
          evidence,
          reportedBy: userId,
          timestamp: new Date()
        }
      }
    });

    // Create audit log
    await AuditLogModel.create({
      action: 'suspicious_activity_reported',
      userId,
      deviceId,
      details: { activityType, description },
      severity: 'high',
      category: 'security'
    });

    // Auto-deactivate device if high-risk activity
    const highRiskActivities = ['malware_detected', 'unauthorized_access', 'data_breach'];
    if (highRiskActivities.includes(activityType)) {
      await DeviceModel.findByIdAndUpdate(deviceId, {
        status: 'suspended',
        suspendedAt: new Date(),
        suspensionReason: `Auto-suspended due to ${activityType}`
      });

      await AuditLogModel.create({
        action: 'device_auto_suspended',
        userId: 'system',
        deviceId,
        details: { reason: activityType },
        severity: 'critical',
        category: 'security'
      });
    }

    res.json({
      success: true,
      message: 'Suspicious activity reported successfully'
    });

  } catch (error) {
    console.error('Report suspicious activity error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to report suspicious activity'
    });
  }
});

export default router;
