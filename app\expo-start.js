// Special script to start Expo with Node.js ESM compatibility
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Starting CCALC app with custom Expo wrapper...');

// Check if local IP address is available
const getLocalIPAddress = () => {
  try {
    const ipInfo = execSync('ipconfig').toString();
    const wifiMatch = ipInfo.match(/Wireless LAN adapter Wi-Fi[\s\S]*?IPv4 Address.*?: ([\d.]+)/);
    
    if (wifiMatch && wifiMatch[1]) {
      return wifiMatch[1];
    }
    
    // Fallback to any IPv4 address
    const anyIPMatch = ipInfo.match(/IPv4 Address.*?: ([\d.]+)/);
    if (anyIPMatch && anyIPMatch[1]) {
      return anyIPMatch[1];
    }
    
    return '127.0.0.1'; // Localhost fallback
  } catch (error) {
    console.error('Error getting IP address:', error.message);
    return '127.0.0.1';
  }
};

const localIP = getLocalIPAddress();
console.log(`Using local IP address: ${localIP}`);

// Create temporary app.json for Expo to use
const createTempAppJson = () => {
  const appConfigPath = path.join(__dirname, 'app.config.js');
  const tempAppJsonPath = path.join(__dirname, 'app.json');
  
  try {
    // Extract config from app.config.js
    const appConfig = require('./app.config.js').default.expo;
    
    // Add extra config for development
    const tempConfig = {
      ...appConfig,
      extra: {
        ...appConfig.extra,
        backendUrl: `http://${localIP}:3000`,
        frontendUrl: `http://${localIP}:3001`
      }
    };
    
    // Write to temporary app.json
    fs.writeFileSync(tempAppJsonPath, JSON.stringify({ expo: tempConfig }, null, 2));
    console.log('Created temporary app.json with local IP configuration');
    
    return tempAppJsonPath;
  } catch (error) {
    console.error('Error creating temporary app.json:', error);
    throw error;
  }
};

const tempAppJsonPath = createTempAppJson();

// Start Expo using npx
try {
  console.log('Starting Expo with LAN configuration...');
  
  // Use --no-dev flag to avoid TypeScript issues
  execSync('npx expo start --port 8082 --lan --no-dev', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      EXPO_LEGACY_IMPORTS: '1'
    }
  });
} catch (error) {
  console.error('Error starting Expo:', error.message);
} finally {
  // Clean up temporary file
  try {
    if (fs.existsSync(tempAppJsonPath)) {
      fs.unlinkSync(tempAppJsonPath);
      console.log('Removed temporary app.json');
    }
  } catch (err) {
    console.error('Error cleaning up:', err);
  }
}
