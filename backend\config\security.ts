/**
 * CCALC Security Configuration
 * Enforces strict role-based access control
 */

export const SECURITY_CONFIG = {
  // Admin Panel Access Rules
  ADMIN_PANEL: {
    ALLOWED_ROLES: ['admin'],
    BLOCKED_ROLES: ['user', 'superuser'],
    REQUIRE_ADMIN_TOKEN: true,
    BLOCK_WEB_FOR_USERS: true
  },

  // Mobile App Access Rules  
  MOBILE_APP: {
    ALLOWED_ROLES: ['user', 'superuser'],
    BLOCKED_ROLES: ['admin'],
    REQUIRE_DEVICE_FINGERPRINT: true,
    REQUIRE_BLE_DEVICE: true,
    BLOCK_WEB_BROWSERS: true
  },

  // Endpoint Access Control
  ENDPOINTS: {
    ADMIN_ONLY: [
      '/api/admin',
      '/api/audit', 
      '/api/system',
      '/api/builds',
      '/api/expressions',
      '/api/config',
      '/api/debug'
    ],
    MOBILE_ONLY: [
      '/api/users',
      '/api/chat',
      '/api/calls',
      '/api/device'
    ],
    MIXED_ACCESS: [
      '/api/voice',  // Admin for monitoring, mobile for calls
      '/api/media'   // Admin for monitoring, mobile for uploads
    ]
  },

  // Authentication Rules
  AUTH_RULES: {
    ADMIN: {
      ENDPOINT: '/api/auth/admin',
      ALLOWED_METHODS: ['POST'],
      REQUIRES_PPK: true,
      WEB_ACCESS: true,
      TOKEN_TYPE: 'admin'
    },
    USER: {
      ENDPOINT: '/api/auth/user',
      ALLOWED_METHODS: ['POST'],
      REQUIRES_CALCULATOR_AUTH: true,
      WEB_ACCESS: false,
      TOKEN_TYPE: 'user',
      DEVICE_VERIFICATION: true
    }
  },

  // Security Headers
  REQUIRED_HEADERS: {
    MOBILE_REQUESTS: [
      'x-device-fingerprint',
      'x-ble-device-id'
    ],
    ADMIN_REQUESTS: [
      'authorization'
    ]
  },

  // Rate Limiting
  RATE_LIMITS: {
    ADMIN_LOGIN: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes
      MAX_ATTEMPTS: 5
    },
    USER_LOGIN: {
      WINDOW_MS: 15 * 60 * 1000, // 15 minutes  
      MAX_ATTEMPTS: 10
    },
    ADMIN_PANEL: {
      WINDOW_MS: 60 * 1000, // 1 minute
      MAX_REQUESTS: 100
    }
  }
};

export const ACCESS_CONTROL_MESSAGES = {
  ADMIN_REQUIRED: 'Admin access required - Please use admin credentials',
  MOBILE_REQUIRED: 'Mobile app authentication required - Web access denied',
  WRONG_ENDPOINT: 'Please use the correct authentication endpoint for your account type',
  DEVICE_REQUIRED: 'Mobile device verification required',
  ADMIN_USE_PANEL: 'Admin accounts must use the admin panel',
  USER_USE_APP: 'User accounts must use the mobile application'
};

export function isAdminOnlyEndpoint(path: string): boolean {
  return SECURITY_CONFIG.ENDPOINTS.ADMIN_ONLY.some(endpoint => 
    path.startsWith(endpoint)
  );
}

export function isMobileOnlyEndpoint(path: string): boolean {
  return SECURITY_CONFIG.ENDPOINTS.MOBILE_ONLY.some(endpoint => 
    path.startsWith(endpoint)
  );
}

export function isMixedAccessEndpoint(path: string): boolean {
  return SECURITY_CONFIG.ENDPOINTS.MIXED_ACCESS.some(endpoint => 
    path.startsWith(endpoint)
  );
}

export function isWebBrowser(userAgent: string): boolean {
  return /Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(userAgent);
}

export function hasRequiredMobileHeaders(req: any): boolean {
  const deviceFingerprint = req.headers['x-device-fingerprint'];
  const bleDeviceId = req.headers['x-ble-device-id'];
  return !!(deviceFingerprint && bleDeviceId);
}
