// This script helps prepare the environment for development by 
// temporarily using the unencrypted key for testing

const fs = require('fs');
const path = require('path');

// Path to key files
const keysDir = path.join(__dirname, '..', 'keys');
const unencryptedKeyPath = path.join(keysDir, 'admin_private_unencrypted.pem');
const encryptedKeyPath = path.join(keysDir, 'admin_private.pem');
const backupKeyPath = path.join(keysDir, 'admin_private.pem.bak');

// Check if we need to back up the encrypted key
if (fs.existsSync(encryptedKeyPath) && !fs.existsSync(backupKeyPath)) {
  console.log('Backing up encrypted key...');
  fs.copyFileSync(encryptedKeyPath, backupKeyPath);
}

// Check if unencrypted key exists
if (!fs.existsSync(unencryptedKeyPath)) {
  console.error('Unencrypted key file not found!');
  console.error(`Expected at: ${unencryptedKeyPath}`);
  process.exit(1);
}

// Copy unencrypted key to primary location
console.log('Copying unencrypted key for development use...');
fs.copyFileSync(unencryptedKeyPath, encryptedKeyPath);

console.log('✅ Development environment prepared!');
console.log('Unencrypted key is now being used for easier development.');
console.log('NOTE: For production, restore the original encrypted key from .bak file');
