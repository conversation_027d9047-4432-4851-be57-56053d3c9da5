# CCALC Project
Certainly. Below is a **standardized, professional, and detailed `README.md`** suitable for your project. It avoids emojis and informal elements, providing a clear and structured documentation style for use in secure, internal, or professional environments.

---

# PrivateCalcChat

**PrivateCalcChat** is a secure, BLE-authenticated, end-to-end encrypted, voice-masked chat application designed for highly controlled environments. The mobile interface is a covert application disguised as a calculator, with backend control provided through a hardened administrative panel.

This project is not intended for public release or App Store distribution. It is engineered for tightly controlled deployment with user-specific builds and infrastructure-level access controls.

---

## Project Objectives

* Covert chat app interface hidden within a fully functioning calculator UI.
* End-to-end encrypted messaging system with per-user key management.
* Media (images, videos, documents) and voice call support.
* Voice call system that includes tone modulation to anonymize speech.
* BLE (Bluetooth Low Energy) characteristic-based authentication for call permission.
* Fingerprinting and device-binding to prevent app re-use or cloning.
* Admin panel for full lifecycle management of users, builds, metadata, and server control.
* Per-user app builds with embedded secrets to detect unauthorized sharing or duplication.

---

## High-Level Directory Structure

```
project-root/
├── docker/               # Docker and container orchestration
├── backend/              # Node.js + Next.js + REST API + Admin UI
├── app/                  # React Native app (iOS-focused)
├── shared/               # Shared logic across frontend and backend
├── .env / .gitignore     # Configuration files and exclusions
└── README.md             # Project documentation
```

---

## Dockerized Architecture

All critical services are isolated and managed via Docker to enhance security, reproducibility, and portability.

### Services

* **Backend**: Node.js, Express, Next.js (admin panel and APIs).
* **MongoDB**: Encrypted, containerized data store.
* **App Builder**: Optional macOS-based container or local runner to produce per-user iOS builds.
* **NGINX** (optional): TLS termination, routing, IP control.

---

## Backend

The backend is responsible for all admin operations, API endpoints, per-user build generation, BLE validation logic, logging, and storage of encrypted chat, media, and call recordings.

### Main Functional Components

* **API Layer**: RESTful endpoints for users, chats, builds, calls.
* **Admin Panel**: Next.js-based frontend with session-secured access.
* **Device Authentication**: Device fingerprint and BLE metadata are verified during login.
* **Secure Build Generation**: Generates uniquely signed `.ipa` builds using injected secrets.
* **Logging and Audit Trails**: Access logs, login attempts, device metadata are recorded and retrievable.

### Key Directories

```
backend/
├── api/                # REST API routes
├── controllers/        # Request and business logic
├── models/             # Mongoose schemas for data modeling
├── middleware/         # PPK auth, token verification, rate-limiting
├── services/           # Core logic: builds, BLE, encryption, call recording
├── scripts/            # Admin utilities: shutdown, wipe, reset BLE
├── utils/              # Reusable helpers (crypto, validation)
├── views/              # Next.js admin UI
├── config/             # DB connection, environment, constants
├── public/             # Admin UI assets
└── server.js           # Express and Next.js initialization
```

---

## React Native App

The mobile app is iOS-focused and intentionally disguised as a calculator. Only after entering a specific pre-set expression does the app reveal the secure chat interface.

### Functional Highlights

* **Stealth Interface**: Opens as a calculator; private chat is unlocked via preconfigured expressions.
* **Authentication**: Users configure a pattern, PIN, or no-login method at first use. This is device-bound and stored securely.
* **Device Binding**: First login fingerprints the device. Only the originally-registered device can access the app.
* **BLE Enforcement**: Voice calls only proceed when authenticated earbuds are connected, verified by advertisement data and service UUID.
* **Media Support**: Users may send encrypted media (images, videos, documents).
* **Voice Modulation**: Real-time modulation of voice calls, particularly anonymizing superuser speech.
* **Call Logs**: Call metadata and recordings are stored and visible to superuser/admin only.

### Key Directories

```
app/
├── src/
│   ├── components/         # UI elements: calculator, chat, call
│   ├── screens/            # Chat, call, login, auth setup
│   ├── services/           # BLE scanning, encryption, messaging
│   ├── utils/              # Expression matcher, tone modulation
│   ├── hooks/              # React hooks for BLE and secure login
│   ├── config/             # Per-user secrets and env
│   ├── assets/             # Fonts, images, icons
│   └── App.tsx             # App entry point
├── ios/                    # Native modules (BLE, audio)
├── build-config/           # Build-time environment injectors
├── .env.template
├── app.config.js
└── metro.config.js
```

---

## Shared Modules

The shared directory contains logic reused across frontend and backend environments, enforcing cryptographic and structural consistency.

```
shared/
├── constants/              # BLE UUIDs, superuser roles, expression formats
├── encryption/             # AES, RSA, NaCl cryptographic helpers
├── types/                  # TypeScript types for shared objects
├── validators/             # JSON/schema validation utilities
```

---

## Per-User Build Security

Each iOS app build is generated with a unique environment configuration:

* Injects per-user identifiers, expression unlock pattern, and encryption keys.
* Uses a secure build pipeline to compile `.ipa` files with non-transferable bindings.
* Admin panel is used to trigger and manage builds.
* Unauthorized distribution or use triggers logs and optional auto-revoke.

---

## Admin Panel Capabilities

* Create and manage users (username, profile, expression trigger).
* Generate per-user secure iOS builds.
* View decrypted user chats and uploaded media.
* Listen to recorded call logs with voice modulation.
* Logout, reset, or deactivate user access at any time.
* Reset BLE characteristics or fingerprint bindings.
* Shutdown or wipe the entire server with multi-factor PPK-based commands.
* Manage a designated superuser who appears in all users' chat lists and receives all communication.

---

## Security Summary

| Category                  | Mechanism                                                          |
| ------------------------- | ------------------------------------------------------------------ |
| Authentication            | Password + PPK file (admin), PIN/pattern (user)                    |
| Build Security            | Per-user hardcoded secrets and fingerprinting                      |
| Data Encryption           | AES-256 + NaCl (libsodium)                                         |
| Device Binding            | First login fingerprinting with UUID, coordinates, device metadata |
| BLE Device Authentication | Service UUID and advertisement data matching                       |
| Call Voice Anonymization  | Native tone modulation and irreversible voice processing           |
| Message Expiry            | Superuser messages disappear after being seen by user              |
| Storage & Audit           | MongoDB, per-user logs, and access metadata                        |

---

## Development Setup

1. Ensure Docker and Docker Compose are installed.
2. Run the following to start the backend, MongoDB, and admin interface:

```bash
docker-compose up --build
```

3. React Native iOS builds must be run manually from a macOS host or CI using the `build-config/` tools inside the `app/` directory.

---

## License and Confidentiality

This project is confidential and intended for private use only. All source code, data structures, and design patterns are proprietary and must not be disclosed or reused without express permission.

---

## Contributions

This repository is maintained by a restricted internal team. Contributions are not open to the public. All collaborators are expected to comply with internal security policies.

---

## Milestone 1: Backend + Admin Panel + Voice Modulation POC (Admin-Side Mic Capture)

### Goals

* Establish a robust backend with Node.js + Next.js + MongoDB supporting all core features.
* Build a highly functional, secure admin panel interface for managing all aspects of user activity, voice calls, media, and device integrity.
* Implement a voice modulation proof-of-concept with mic capture and real-time processing.
* Placeholder BLE verification logic for binding and validating earbuds per user.
* Dockerize backend and MongoDB services for isolated, repeatable development and deployment.
* Implement secure admin login via password + PPK authentication, and prepare user auth logic via PIN/pattern.

---

### Detailed Tasks

### Backend Setup

## 🔐 Authentication APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| POST | /api/admin/login | Admin login with password + PPK file |
| POST | /api/admin/logout | Ends the current admin session |
| GET | /api/admin/session | Validates session token and returns profile |

---

## 👤 User Management APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/users | List all users |
| POST | /api/users | Create new user with username, expression, etc. |
| GET | /api/users/:id | Fetch specific user details |
| PUT | /api/users/:id | Update user details (profile, status) |
| DELETE | /api/users/:id | Deactivate or delete user |
| POST | /api/users/:id/reset | Reset user device fingerprint/BLE configuration |

---

## 📱 Build Management APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| POST | /api/builds | Trigger a new per-user iOS build |
| GET | /api/builds | List build history/logs |
| GET | /api/builds/:userId | Get build metadata for a specific user |
| POST | /api/builds/:userId/revoke | Revoke a previously issued app build |

---

## 💬 Messaging & Media APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/messages/:userId | Fetch decrypted chat logs for user |
| DELETE | /api/messages/:userId | Purge all messages for a user |
| GET | /api/media/:userId | List all uploaded media files |
| GET | /api/media/:userId/:mediaId | Download/view specific media |
| DELETE | /api/media/:userId/:mediaId | Delete a specific media item |

---

## 📞 Call Log & Recording APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/calls/:userId | List call logs with timestamps |
| GET | /api/calls/:userId/:callId | Download or stream call recording |
| DELETE | /api/calls/:userId/:callId | Remove a specific call log/recording |

---

## 🛠️ System & Utility APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| POST | /api/system/shutdown | Initiate full backend shutdown (admin only) |
| POST | /api/system/wipe | Wipe server data (requires MFA/PPK) |
| POST | /api/system/reset-ble | Reset all BLE characteristics in backend |
| GET | /api/system/logs | Retrieve access logs, login attempts, etc. |
| GET | /api/system/status | Server and service health check |

---

## 🧑‍✈️ Superuser Management APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/superuser | Get current superuser metadata |
| PUT | /api/superuser | Update superuser profile or settings |
| GET | /api/superuser/inbox | View all incoming messages |

---

Here are additional API endpoints worth including:

---

## 📊 Audit & Analytics APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/audit/logs | Retrieve full audit trail (login, access, etc.) |
| GET | /api/audit/user/:userId | User-specific audit logs |
| GET | /api/audit/device/:deviceId | Device-specific activity logs |
| GET | /api/audit/actions | Admin action logs (user deletes, resets, etc.) |

---

## 🧬 Device & Fingerprint Management APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/devices | List all registered devices |
| GET | /api/devices/:userId | List devices for a specific user |
| GET | /api/devices/fingerprint/:deviceId | Get fingerprint metadata for a device |
| DELETE | /api/devices/:deviceId | Revoke and deauthorize a device |

---

## 🔊 Voice Modulation Settings (Optional, if adjustable)

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/calls/modulation-settings | View current modulation profiles |
| PUT | /api/calls/modulation-settings | Update modulation parameters (pitch, tone) |

---

## 📡 BLE Device Registry (If manually managed)

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/ble/registry | View known BLE device UUIDs and metadata |
| POST | /api/ble/registry | Register a new BLE device or characteristic manually |
| DELETE | /api/ble/registry/:id | Remove a BLE device from the trusted list |

---

## 🧾 Expression Pattern APIs

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/users/:id/expression | Get user’s unlock expression |
| PUT | /api/users/:id/expression | Update or rotate expression trigger |
| GET | /api/expressions/validate/:exp | Validate if a pattern is syntactically secure |

---

## 🧪 Diagnostic / Debug APIs (admin-only; hidden or token-locked)

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/debug/app-state | Returns current app state for debug purposes |
| GET | /api/debug/ble-scan | Test BLE scanning and matching functionality |
| GET | /api/debug/build-info | Debug info about current builder env |

---

## 🧩 Configurable Settings APIs (for system-wide behavior tuning)

| Method | Endpoint | Description |
| --- | --- | --- |
| GET | /api/config | Retrieve current backend config |
| PUT | /api/config | Update system-wide flags or behaviors |
| GET | /api/config/security | View security policy settings |
| PUT | /api/config/security | Update encryption, token TTL, etc. |



---

### Admin Panel (Next.js)

#### ⚙️ Authentication

* Implement login screen:

  * Requires admin password (stored securely) and PPK file upload.
  * Validate both locally and against backend auth logic.
  * Session management via secure cookies (HttpOnly + SameSite).
* Logout flow to invalidate session.

#### 🖥️ Dashboard Overview

* Dashboard landing after login:

  * Quick server status indicators (CPU, memory, DB, active users).
  * Log summaries: login attempts, last accessed, flagged activity.
  * Button components:

    * **“Shutdown Server”**: triggers graceful termination of backend (password required).
    * **“Erase Everything”**: wipes all DB and system files (requires different PPK + password).
    * Confirmation prompts and MFA validation flows built in.

#### 👤 User Management Interface

* **User Listing View**:

  * Fetches paginated user list from backend.
  * Select multiple users for batch delete/block.
  * Shows status (active, blocked, device-bound).
* **Create User Form**:

  * Fields: Username, expression/calc logic, superuser toggle, initial auth method (PIN/Pattern/None).
  * Trigger build generation per user using backend API.
* **Detailed User View**:

  * Tabs:

    * Profile Info
    * Chat Logs (with media viewer: image, video, docs)
    * Voice Call Logs (with streaming player)
    * BLE Info
    * Device Fingerprint Metadata
  * Manual reset buttons:

    * BLE characteristic data
    * Fingerprint auth data
    * PIN/Pattern
    * Deactivate account

#### 📡 BLE Scan & Bind Interface

* Interface to:

  * Start browser-based Bluetooth scan (Web Bluetooth API).
  * Display scanned devices and their advertisement data.
  * Select BLE device, view UUID, and characteristics.
  * Bind ad-data + service UUIDs to a specific user in DB.
  * Allow future editing/reset of this data from user view.

#### 🔊 Voice Modulation & Live Mic Capture

* **Live Audio Capture Panel**:

  * Use `MediaDevices.getUserMedia()` for mic capture.
  * Real-time waveform visualizer using AnalyserNode (Web Audio API).
  * Start/Stop capture buttons.
* **Voice Modulation POC**:

  * Stream raw audio via WebSocket to backend or use browser-side audio worklets (experimental).
  * Apply tone shifting, pitch morphing (e.g. via `audioContext.createBiquadFilter` or DSP logic).
  * Configurable modulation profiles (e.g., alien, robotic, randomized).
  * Live stream modulated audio back to browser:

    * Buffered playback through `AudioBufferSourceNode`.
  * Realtime status logs of stream health, latency, modulation profile used.

#### 📞 Voice Call Monitoring

* Embedded call recordings player per user (via streaming API).
* Show BLE validation status before call start:

  * “Connected and Verified”
  * “Device Mismatch”
  * “No BLE Connected”
* Manual override options for BLE rules in testing/dev.

#### 🔐 Superuser Special Panel

* One user can be designated as superuser:

  * All messages from others appear as conversations with superuser.
  * All normal users see only superuser in their chat list.
* Toggle per user in admin panel.
* Voice modulation always on for superuser; others configurable.
* Superuser inbox aggregates all chats across users.

#### 📱 Device Integrity Monitoring

* List all user-bound devices:

  * Device fingerprint (iOS-specific), timestamp, geolocation (if permitted), first login metadata.
* Allow admin to:

  * Invalidate/revoke device binding.
  * Reset fingerprint for re-auth.
  * See location + IP audit trail.
* Enforce single-device lock per user:

  * Automatically block login attempts from second device (tracked via device hash + fingerprint).

#### 🧠 Build Control (Per User)

* UI to trigger app build with unique hardcoded key per user.
* Backend detects redistribution if key mismatch occurs.
* Option to revoke or regenerate build.
* Alert system for suspected unauthorized distribution.

#### 📊 System Logs & Audit Trail

* Embedded console/log viewer:

  * Admin actions (delete, reset, etc.)
  * User login attempts
  * Device switch attempts
  * BLE scanning records
* Real-time filters:

  * By user, by type, by timestamp.

---

### Voice Modulation Service

* Optional service embedded in backend or modular frontend:

  * Voice modulation algorithms: real-time DSP pitch shift, gender swap, robotic/masked filters.
  * Profiles defined per role (superuser/normal).
* WebSocket-based audio stream transport for low latency.
* Secure, non-reversible modulation output with obfuscation.
* Toggle control for modulation: user-wise, call-wise.

---

### BLE Verification (Backend Placeholder)

* BLE registry:

  * Accepts BLE characteristic + ad-data per user.
* Match verification on app call initiation:

  * Valid only if matching BLE device is connected.
* Allow manual reset/update of stored BLE signature via admin panel.
* Backend simulation available for testing in absence of physical device.
