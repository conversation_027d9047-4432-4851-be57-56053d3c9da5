import type { NextApiRequest, NextApiResponse } from 'next';
import { rateLimit } from '../../../../../utils/rateLimit';
import ServerApiClient from '../../../../../utils/serverApiClient';

// IMMEDIATELY LOG ENVIRONMENT VARIABLES FOR DEBUG
console.log('ADMIN LOGIN INIT API HANDLER - ENV VARS:', {
  backendUrl: process.env.NEXT_PUBLIC_BACKEND_URL,
  nodeEnv: process.env.NODE_ENV
});

const limiter = rateLimit({
  interval: 60 * 1000, // 60 seconds
  uniqueTokenPerInterval: 500, // Max 500 users per second
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await limiter.check(res, 10, 'ADMIN_LOGIN_INIT_RATE_LIMIT'); // 10 requests per minute

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { username } = req.body;
    console.log('Admin login init request:', { username });    if (!username) {
      return res.status(400).json({ error: 'Username is required' });
    }
      // Forward request to backend API
    const serverClient = new ServerApiClient();

    try {
      const response = await serverClient.post('/api/auth/admin/login/init', { username });

      console.log('Backend response status:', response.status);
      const data = await response.json();
      console.log('Backend response data:', {
        requiresPPK: data.requiresPPK,
        hasChallenge: !!data.challenge,
        authMethod: data.authMethod,
        message: data.message,
        error: data.error
      });

      if (!response.ok) {
        console.error('Backend login init error:', data);
        return res.status(response.status).json(data);
      }

      // For dev purposes, if no backend connection, simulate a PPK challenge
      if (process.env.NODE_ENV === 'development' && !data.challenge && username.startsWith('admin')) {
        console.log('Development mode: simulating PPK challenge');
        return res.status(200).json({
          requiresPPK: true,
          challenge: Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')
        });
      }

      // Return the response from the backend
      return res.status(200).json(data);
    } catch (fetchError: any) {
      console.error('Failed to contact backend:', fetchError);
      return res.status(502).json({ 
        error: 'Failed to connect to authentication server',
        details: fetchError.message
      });
    }
  } catch (error) {
    console.error('Admin login init error:', error);
    return res.status(500).json({ error: 'Authentication initialization failed' });
  }
}
