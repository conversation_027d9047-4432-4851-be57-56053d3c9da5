<!DOCTYPE html>
<html>
<head>
    <title>CCALC Auth Debug</title>
</head>
<body>
    <h1>CCALC Authentication Debug</h1>
    
    <div>
        <h2>Storage Test</h2>
        <button onclick="testStorage()">Test Storage Operations</button>
        <button onclick="testTokenManager()">Test Token Manager</button>
        <button onclick="clearAll()">Clear All Storage</button>
        <div id="storage-results"></div>
    </div>
    
    <div>
        <h2>API Test</h2>
        <button onclick="testBackendCSRF()">Test Backend CSRF</button>
        <button onclick="testLogin()">Test Login Flow</button>
        <div id="api-results"></div>
    </div>

    <script>
        function testStorage() {
            const results = document.getElementById('storage-results');
            const testToken = 'test-token-' + Date.now();
            
            try {
                // Test localStorage
                localStorage.setItem('test-token', testToken);
                const localResult = localStorage.getItem('test-token');
                
                // Test sessionStorage
                sessionStorage.setItem('test-token', testToken);
                const sessionResult = sessionStorage.getItem('test-token');
                
                // Test cookies
                document.cookie = `test-cookie=${testToken}; path=/`;
                const cookieResult = document.cookie.includes(testToken);
                
                results.innerHTML = `
                    <p>Storage Test Results:</p>
                    <ul>
                        <li>localStorage: ${localResult === testToken ? 'PASS' : 'FAIL'}</li>
                        <li>sessionStorage: ${sessionResult === testToken ? 'PASS' : 'FAIL'}</li>
                        <li>cookies: ${cookieResult ? 'PASS' : 'FAIL'}</li>
                    </ul>
                `;
            } catch (e) {
                results.innerHTML = `<p>Storage test error: ${e.message}</p>`;
            }
        }
        
        function testTokenManager() {
            const results = document.getElementById('storage-results');
            
            // This would require importing tokenManager, so just test basic functionality
            results.innerHTML += `<p>TokenManager test would require module imports</p>`;
        }
        
        function clearAll() {
            localStorage.clear();
            sessionStorage.clear();
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            document.getElementById('storage-results').innerHTML = '<p>All storage cleared</p>';
        }
        
        async function testBackendCSRF() {
            const results = document.getElementById('api-results');
            
            try {
                const response = await fetch('http://localhost:3000/api/csrf-token');
                const data = await response.json();
                results.innerHTML = `<p>Backend CSRF: ${response.ok ? 'PASS' : 'FAIL'} - ${JSON.stringify(data)}</p>`;
            } catch (e) {
                results.innerHTML = `<p>Backend CSRF test error: ${e.message}</p>`;
            }
        }
        
        async function testLogin() {
            const results = document.getElementById('api-results');
            
            try {
                // Test frontend login endpoint
                const response = await fetch('/api/auth/admin/login/complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                results.innerHTML += `<p>Login test: ${response.ok ? 'PASS' : 'FAIL'} - Status: ${response.status}</p>`;
                results.innerHTML += `<p>Response: ${JSON.stringify(data, null, 2)}</p>`;
            } catch (e) {
                results.innerHTML += `<p>Login test error: ${e.message}</p>`;
            }
        }
    </script>
</body>
</html>
