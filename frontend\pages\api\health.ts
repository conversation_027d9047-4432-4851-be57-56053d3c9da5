import type { NextApiRequest, NextApiResponse } from 'next';
import getBackendUrl from '../../utils/getBackendUrl';
import os from 'os';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const backendUrl = getBackendUrl();
    let backendStatus = null;
    
    // Try to connect to backend
    try {
      const backendResponse = await fetch(`${backendUrl}/api/health`, { 
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (backendResponse.ok) {
        backendStatus = await backendResponse.json();
      }
    } catch (fetchError) {
      console.error('Backend health check failed:', fetchError);
      backendStatus = { error: 'Cannot connect to backend' };
    }
    
    return res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'ccalc-frontend',
      hostname: os.hostname(),
      environment: process.env.NODE_ENV || 'development',
      backend: {
        url: backendUrl,
        status: backendStatus
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    return res.status(500).json({ 
      status: 'error',
      error: 'Health check failed'
    });
  }
}
