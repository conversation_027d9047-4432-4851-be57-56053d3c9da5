const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema (simplified)
const UserSchema = new mongoose.Schema({
  username: String,
  expressionHash: String,
  unlockExpression: String,
  status: String,
  createdAt: Date,
});

const User = mongoose.model('User', UserSchema);

async function testExpressions() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    const user = await User.findOne({ username: 'mobileuser' });
    if (!user) {
      console.log('❌ Mobile user not found');
      return;
    }

    console.log('\n📊 User Details:');
    console.log(`Username: ${user.username}`);
    console.log(`Stored Expression: ${user.unlockExpression}`);
    console.log(`Expression Hash: ${user.expressionHash.substring(0, 20)}...`);
    console.log(`Status: ${user.status}`);

    console.log('\n🧮 Testing Expression Variations:');
    
    // Test different expression formats
    const testExpressions = [
      '2+3*4',    // Should be 14 (correct order of operations)
      '2 + 3 * 4', // With spaces
      '(2+3)*4',   // Would be 20 if grouped differently
      '2+3×4',     // Different multiplication symbol
      '2 + 3 × 4', // With spaces and different symbol
    ];

    for (const expr of testExpressions) {
      try {
        const isMatch = await bcrypt.compare(expr, user.expressionHash);
        console.log(`"${expr}" → ${isMatch ? '✅ MATCH' : '❌ No match'}`);
      } catch (error) {
        console.log(`"${expr}" → ❌ Error: ${error.message}`);
      }
    }

    console.log('\n📱 Mobile App Debug Info:');
    console.log('If mobile app is calculating 2+3*4 = 20, it means:');
    console.log('- App is doing left-to-right: (2+3)*4 = 5*4 = 20');
    console.log('- But database expects: 2+(3*4) = 2+12 = 14');
    console.log('\n💡 Solutions:');
    console.log('1. Fix mobile app to use proper order of operations');
    console.log('2. OR change test expression to something simpler like "5+3" = 8');
    console.log('3. OR use parentheses to make order explicit: "(2+3)*4" = 20');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

testExpressions();
