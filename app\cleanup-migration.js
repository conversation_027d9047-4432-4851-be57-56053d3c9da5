/**
 * Cleanup script to remove the legacy migration file
 */

const fs = require('fs');
const path = require('path');

// Path to the file we want to delete
const migrationFilePath = path.join(__dirname, 'src', 'services', 'MediaStorageMigration.ts');

// Check if file exists
if (fs.existsSync(migrationFilePath)) {
  console.log(`📝 Found migration file at: ${migrationFilePath}`);
  console.log('🗑️ Deleting file...');
  
  try {
    // Delete the file
    fs.unlinkSync(migrationFilePath);
    console.log('✅ Successfully deleted MediaStorageMigration.ts');
  } catch (error) {
    console.error('❌ Error deleting file:', error);
  }
} else {
  console.log('✅ MediaStorageMigration.ts file already removed.');
}

console.log('✨ Cleanup complete!');
