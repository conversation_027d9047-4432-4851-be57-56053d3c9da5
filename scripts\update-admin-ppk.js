#!/usr/bin/env node

/**
 * Update Admin PPK Key in MongoDB
 * 
 * This script updates the ppkPublicKey and related fields in the 
 * MongoDB admin document using a formatted public key file.
 * 
 * Usage:
 *   node scripts/update-admin-ppk.js <username> <formatted-key-file-path>
 */

const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');
const readline = require('readline');

// Command line interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Ask a question and return a promise
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// Main function
async function main() {
  try {
    // Get username and key path from arguments or prompt
    let username = process.argv[2];
    let keyFilePath = process.argv[3];
    
    if (!username) {
      username = await question('Enter admin username [admin]: ') || 'admin';
    }
    
    if (!keyFilePath) {
      keyFilePath = await question('Enter path to formatted public key file: ');
      if (!keyFilePath) {
        throw new Error('Formatted public key file path is required');
      }
    }
    
    // Resolve relative path if needed
    if (!path.isAbsolute(keyFilePath)) {
      keyFilePath = path.resolve(process.cwd(), keyFilePath);
    }
    
    // Read the formatted key file
    console.log(`Reading key file: ${keyFilePath}`);
    if (!fs.existsSync(keyFilePath)) {
      throw new Error(`Key file not found: ${keyFilePath}`);
    }
    
    const formattedPublicKey = fs.readFileSync(keyFilePath, 'utf8').trim();
    if (!formattedPublicKey) {
      throw new Error('Key file is empty');
    }
    
    // Get MongoDB connection URI
    const mongoUri = process.env.MONGO_URI || '*************************************************************';
    
    // Connect to MongoDB
    console.log(`Connecting to MongoDB...`);
    const client = new MongoClient(mongoUri);
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Get the admin database and collection
    const db = client.db('ccalc');
    const adminsCollection = db.collection('admins');
    
    // Check if admin exists
    const admin = await adminsCollection.findOne({ username });
    if (!admin) {
      throw new Error(`Admin user '${username}' not found in database`);
    }
    
    console.log(`Found admin user: ${username}`);
    
    // Update the admin document
    const timestamp = new Date().toISOString().replace(/:/g, '-').split('.')[0] + 'Z';
    const result = await adminsCollection.updateOne(
      { username },
      { 
        $set: {
          ppkPublicKey: formattedPublicKey,
          ppkEnabled: true,
          authMethod: "both",
          ppkKeyVersion: timestamp,
          ppkLastRotated: new Date()
        }
      }
    );
    
    if (result.modifiedCount === 0) {
      console.log('No changes made to the admin document (values may already be set)');
    } else {
      console.log(`✅ Successfully updated admin '${username}' with new PPK public key`);
    }
    
    console.log('\nPPK authentication is now enabled for this admin.');
    console.log('The admin can log in using both password and PPK authentication.');
    
    // Close MongoDB connection
    await client.close();
    console.log('MongoDB connection closed');
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the script
main();
