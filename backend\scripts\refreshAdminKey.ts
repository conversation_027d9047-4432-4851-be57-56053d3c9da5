#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to rotate admin PPK keys
 * This script generates a new key pair and updates the admin's public key in the database
 * The old key pair is archived but not deleted
 */

import path from 'path';
import { promises as fs } from 'fs';
import { generatePPKKeyPair, saveKeyPair } from '../utils/ppk';
import { Admin } from '../models/Admin';
import { connectDB } from '../config/database';
import readline from 'readline';
import { promisify } from 'util';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = promisify(rl.question).bind(rl);

async function getPassphrase(): Promise<string> {
  const passphrase = await question('Enter passphrase for key encryption: ');
  const confirm = await question('Confirm passphrase: ');

  if (passphrase !== confirm) {
    throw new Error('Passphrases do not match');
  }

  return passphrase;
}

async function archiveOldKeys(username: string, keysDir: string): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const archiveDir = path.join(keysDir, 'archive', timestamp);
  await fs.mkdir(archiveDir, { recursive: true });

  // Move old keys to archive
  const files = await fs.readdir(keysDir);
  for (const file of files) {
    if (file.startsWith(username) && file.endsWith('.pem')) {
      const oldPath = path.join(keysDir, file);
      const newPath = path.join(archiveDir, file);
      await fs.rename(oldPath, newPath);
    }
  }
}

async function main() {
  try {
    const args = process.argv.slice(2);
    const encrypted = args.includes('--encrypted');
    
    // Connect to database
    await connectDB();

    // Get admin username
    const username = await question('Enter admin username to rotate keys for: ');
    const admin = await Admin.findOne({ username });
    
    if (!admin) {
      throw new Error(`Admin user '${username}' not found`);
    }

    // Get passphrase if encrypted
    let passphrase: string | undefined;
    if (encrypted) {
      passphrase = await getPassphrase();
    }

    // Generate new key pair
    console.log('Generating new PPK key pair...');
    const keyPair = generatePPKKeyPair(encrypted, passphrase);

    // Create keys directory if it doesn't exist
    const keysDir = path.resolve(__dirname, '../../keys');
    await fs.mkdir(keysDir, { recursive: true });

    // Archive old keys
    await archiveOldKeys(username, keysDir);

    // Save new keys
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const publicKeyPath = path.join(keysDir, `${username}_public_${timestamp}.pem`);
    const privateKeyPath = path.join(keysDir, `${username}_private_${timestamp}.pem`);

    await saveKeyPair(keyPair, publicKeyPath, privateKeyPath);

    // Update admin's public key in database
    admin.ppkPublicKey = keyPair.publicKey;
    await admin.save();

    // Create key info file
    const keyInfo = {
      username,
      generatedAt: timestamp,
      encrypted,
      publicKeyPath: path.relative(process.cwd(), publicKeyPath),
      privateKeyPath: path.relative(process.cwd(), privateKeyPath),
      rotationDate: new Date().toISOString()
    };

    const infoPath = path.join(keysDir, `${username}_keys_info_${timestamp}.json`);
    await fs.writeFile(infoPath, JSON.stringify(keyInfo, null, 2));

    console.log('\nPPK keys rotated successfully:');
    console.log(`Public key: ${publicKeyPath}`);
    console.log(`Private key: ${privateKeyPath}`);
    console.log(`Key info: ${infoPath}`);
    console.log('\nIMPORTANT: Update all necessary systems with the new public key!');
    
    if (encrypted) {
      console.log('Store the private key and passphrase securely!');
    }

  } catch (error) {
    console.error('Error rotating PPK keys:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  } finally {
    rl.close();
    process.exit(0);
  }
}

main();