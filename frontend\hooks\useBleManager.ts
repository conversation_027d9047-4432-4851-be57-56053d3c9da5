import { useState, useEffect, useCallback } from 'react';

// Standardized imports
import type { 
  BleDevice, 
  BleServiceInfo, 
  BleCharacteristicInfo, 
  BleConnectionOptions, 
  BleOperationResult,
  UseBleManagerResult,
  BleConnectionStatus,
  BluetoothDevice,
  RequestDeviceOptions
} from '../types/ble';
import { 
  CONNECTION_CONFIG,
  DEFAULT_SERVICE_FILTER,
  BLE_ERROR_MESSAGES 
} from '../constants/ble';
import { 
  isWebBluetoothSupported,
  validateBleEnvironment,
  parseBleError,
  retryWithBackoff,
  logBleOperation
} from '../utils/ble';

const useBleManager = (defaultOptions: BleConnectionOptions = {}): UseBleManagerResult => {
  const [permittedDevices, setPermittedDevices] = useState<BleDevice[]>([]);
  const [connectedDevices, setConnectedDevices] = useState<BleDevice[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<BleDevice | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<BleConnectionStatus>('idle');
  const [lastError, setLastError] = useState<string | null>(null);
  const [notificationCallbacks, setNotificationCallbacks] = useState<Map<string, (value: DataView) => void>>(new Map());

  const {
    autoReconnect = false,
    connectionTimeout = CONNECTION_CONFIG.DEFAULT_CONNECTION_TIMEOUT,
    serviceFilter = DEFAULT_SERVICE_FILTER
  } = defaultOptions;

  // Initialize and load persisted devices
  useEffect(() => {
    loadPersistedDevices();
  }, []);

  // Auto-reconnect to previously connected devices
  useEffect(() => {
    if (autoReconnect && permittedDevices.length > 0) {
      const lastConnectedDevice = permittedDevices
        .filter(d => d.lastConnected)
        .sort((a, b) => (b.lastConnected?.getTime() || 0) - (a.lastConnected?.getTime() || 0))[0];
      
      if (lastConnectedDevice && !lastConnectedDevice.connected) {
        connectToDevice(lastConnectedDevice);
      }
    }
  }, [permittedDevices, autoReconnect]);

  const clearError = useCallback(() => {
    setLastError(null);
  }, []);
  const setError = useCallback((error: string | Error) => {
    const errorMessage = parseBleError(error);
    setLastError(errorMessage);
    setConnectionStatus('error');
    logBleOperation('Error', { error: errorMessage }, 'error');
  }, []);
  const webBluetoothSupportCheck = useCallback((): boolean => {
    return isWebBluetoothSupported();
  }, []);  const loadPersistedDevices = useCallback(async () => {
    try {
      const validation = await validateBleEnvironment();
      if (!validation.success) {
        logBleOperation('BLE Environment Invalid', { error: validation.error }, 'warn');
        setPermittedDevices([]);
        return;
      }

      // Try to get devices from browser's Web Bluetooth API (if available)
      if (navigator.bluetooth && 'getDevices' in navigator.bluetooth) {
        try {
          const devices = await (navigator.bluetooth as any).getDevices();
          const deviceList: BleDevice[] = devices.map((device: BluetoothDevice) => ({
            id: device.id,
            name: device.name,
            connected: device.gatt?.connected || false,
            gatt: device.gatt,
            lastConnected: undefined
          }));
          
          setPermittedDevices(deviceList);
          setConnectedDevices(deviceList.filter(d => d.connected));
          logBleOperation('Device Load', { deviceCount: deviceList.length });
        } catch (error) {
          logBleOperation('Device Load Failed', { error: parseBleError(error) }, 'warn');
          setPermittedDevices([]);
        }
      } else {
        // Fallback: start with empty list
        setPermittedDevices([]);
      }
    } catch (error) {
      logBleOperation('Device Load Error', { error: parseBleError(error) }, 'error');
      setPermittedDevices([]);
    }
  }, []);  const scanForDevices = useCallback(async (options: BleConnectionOptions = {}): Promise<BluetoothDevice | null> => {
    try {
      const validation = await validateBleEnvironment();
      if (!validation.success) {
        setError(validation.error || BLE_ERROR_MESSAGES.ENVIRONMENT_ERROR);
        return null;
      }

      setIsScanning(true);
      setConnectionStatus('scanning');
      clearError();
      logBleOperation('Device Scan Started', { options });      const scanOptions = {
        acceptAllDevices: true,
        optionalServices: [...serviceFilter, ...(options.serviceFilter || [])]
      };

      const device = await navigator.bluetooth.requestDevice(scanOptions);
      
      setIsScanning(false);
      setConnectionStatus('idle');
      logBleOperation('Device Scan Success', { deviceName: device.name, deviceId: device.id });
      return device;
    } catch (error: any) {
      setIsScanning(false);
      setConnectionStatus('error');
      
      if (error.name === 'NotFoundError' || error.message?.includes('User cancelled')) {
        setConnectionStatus('idle');
        logBleOperation('Device Scan Cancelled', {});
        return null;
      }
      
      const errorMessage = parseBleError(error);
      setError(errorMessage);
      logBleOperation('Device Scan Failed', { error: errorMessage }, 'error');
      return null;
    }
  }, [serviceFilter, clearError]);

  const scanDeviceServices = useCallback(async (device: BluetoothDevice): Promise<BleServiceInfo[]> => {
    if (!device.gatt?.connected) {
      throw new Error('Device not connected');
    }

    try {
      const services = await device.gatt.getPrimaryServices();
      const serviceInfos: BleServiceInfo[] = [];

      for (const service of services) {
        try {
          const characteristics = await service.getCharacteristics();
          const charInfos: BleCharacteristicInfo[] = [];

          for (const char of characteristics) {
            charInfos.push({
              uuid: char.uuid,
              properties: {
                read: char.properties.read,
                write: char.properties.write,
                writeWithoutResponse: char.properties.writeWithoutResponse,
                notify: char.properties.notify,
                indicate: char.properties.indicate,
                broadcast: char.properties.broadcast,
                authenticatedSignedWrites: char.properties.authenticatedSignedWrites,
                reliableWrite: char.properties.reliableWrite,
                writableAuxiliaries: char.properties.writableAuxiliaries
              }
            });
          }

          serviceInfos.push({
            uuid: service.uuid,
            characteristics: charInfos
          });
        } catch (serviceError) {
          logBleOperation('Service Scan Warning', { serviceUuid: service.uuid, error: parseBleError(serviceError) }, 'warn');
        }
      }

      return serviceInfos;
    } catch (error) {
      console.error('Error scanning device services:', error);
      return [];
    }
  }, []);
  const connectToDevice = useCallback(async (device: BleDevice | BluetoothDevice): Promise<BleOperationResult> => {
    try {
      setIsConnecting(true);
      setConnectionStatus('connecting');
      clearError();
      logBleOperation('Device Connection Started', { deviceId: device.id, deviceName: device.name });

      let bluetoothDevice: BluetoothDevice;
      let bleDevice: BleDevice;

      if ('gatt' in device && device.gatt) {
        // It's already a BleDevice
        bluetoothDevice = device.gatt.device;
        bleDevice = device as BleDevice;
      } else {
        // It's a BluetoothDevice
        bluetoothDevice = device as BluetoothDevice;
        bleDevice = {
          id: bluetoothDevice.id,
          name: bluetoothDevice.name,
          connected: false,
          gatt: bluetoothDevice.gatt
        };
      }

      // Connect with retry logic
      const server = await retryWithBackoff(async () => {
        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error(BLE_ERROR_MESSAGES.CONNECTION_TIMEOUT)), connectionTimeout);
        });

        // Connect to GATT server
        const connectPromise = async () => {
          if (!bluetoothDevice.gatt?.connected) {
            await bluetoothDevice.gatt?.connect();
          }
          return bluetoothDevice.gatt!;
        };

        return Promise.race([connectPromise(), timeoutPromise]);
      }, CONNECTION_CONFIG.MAX_RETRY_ATTEMPTS);

      // Update device state
      bleDevice.connected = true;
      bleDevice.lastConnected = new Date();
      bleDevice.gatt = server;

      // Scan services
      const services = await scanDeviceServices(bluetoothDevice);
      bleDevice.services = services;

      // Add disconnect listener
      bluetoothDevice.addEventListener('gattserverdisconnected', () => {
        handleDeviceDisconnected(bleDevice.id);
      });

      // Update state
      setSelectedDevice(bleDevice);
      setConnectionStatus('connected');

      // Update permitted devices
      setPermittedDevices(prev => {
        const existing = prev.find(d => d.id === bleDevice.id);
        if (existing) {
          return prev.map(d => d.id === bleDevice.id ? bleDevice : d);
        }
        return [...prev, bleDevice];
      });

      // Update connected devices
      setConnectedDevices(prev => {
        const existing = prev.find(d => d.id === bleDevice.id);
        if (existing) {
          return prev.map(d => d.id === bleDevice.id ? bleDevice : d);
        }
        return [...prev, bleDevice];
      });

      logBleOperation('Device Connection Success', { deviceId: bleDevice.id, deviceName: bleDevice.name });
      return { success: true, data: bleDevice };
    } catch (error: any) {
      setConnectionStatus('error');
      const errorMessage = parseBleError(error);
      setError(errorMessage);
      logBleOperation('Device Connection Failed', { error: errorMessage }, 'error');
      return { success: false, error: errorMessage };
    } finally {
      setIsConnecting(false);
    }
  }, [connectionTimeout, clearError, scanDeviceServices]);

  const handleDeviceDisconnected = useCallback((deviceId: string) => {
    setPermittedDevices(prev => 
      prev.map(d => d.id === deviceId ? { ...d, connected: false } : d)
    );
    
    setConnectedDevices(prev => prev.filter(d => d.id !== deviceId));
    
    if (selectedDevice?.id === deviceId) {
      setSelectedDevice(null);
      setConnectionStatus('disconnected');
    }
  }, [selectedDevice]);

  const disconnectDevice = useCallback(async (deviceId: string): Promise<BleOperationResult> => {
    try {
      const device = permittedDevices.find(d => d.id === deviceId);
      if (!device) {
        return { success: false, error: 'Device not found' };
      }

      if (device.gatt?.connected) {
        device.gatt.disconnect();
      }

      handleDeviceDisconnected(deviceId);
      
      return { success: true };
    } catch (error: any) {
      const errorMessage = `Failed to disconnect: ${error.message}`;
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [permittedDevices, handleDeviceDisconnected]);

  const selectDevice = useCallback((device: BleDevice) => {
    setSelectedDevice(device);
  }, []);
  const readCharacteristic = useCallback(async (serviceUuid: string, characteristicUuid: string): Promise<BleOperationResult> => {
    if (!selectedDevice?.gatt?.connected) {
      return { success: false, error: BLE_ERROR_MESSAGES.DEVICE_DISCONNECTED };
    }

    try {
      const service = await selectedDevice.gatt.getPrimaryService(serviceUuid);
      const characteristic = await service.getCharacteristic(characteristicUuid);
      const value = await characteristic.readValue();
      
      logBleOperation('Characteristic Read', { serviceUuid, characteristicUuid, dataLength: value.byteLength });
      return { success: true, data: value };
    } catch (error: any) {
      const errorMessage = parseBleError(error);
      setError(errorMessage);
      logBleOperation('Characteristic Read Failed', { serviceUuid, characteristicUuid, error: errorMessage }, 'error');
      return { success: false, error: errorMessage };
    }
  }, [selectedDevice]);
  const writeCharacteristic = useCallback(async (serviceUuid: string, characteristicUuid: string, value: ArrayBuffer): Promise<BleOperationResult> => {
    if (!selectedDevice?.gatt?.connected) {
      return { success: false, error: BLE_ERROR_MESSAGES.DEVICE_DISCONNECTED };
    }

    try {
      const service = await selectedDevice.gatt.getPrimaryService(serviceUuid);
      const characteristic = await service.getCharacteristic(characteristicUuid);
      
      if (characteristic.properties.write) {
        await characteristic.writeValue(value);
      } else if (characteristic.properties.writeWithoutResponse) {
        await characteristic.writeValueWithoutResponse(value);
      } else {
        return { success: false, error: BLE_ERROR_MESSAGES.WRITE_FAILED };
      }
      
      logBleOperation('Characteristic Write', { serviceUuid, characteristicUuid, dataLength: value.byteLength });
      return { success: true };
    } catch (error: any) {
      const errorMessage = parseBleError(error);
      setError(errorMessage);
      logBleOperation('Characteristic Write Failed', { serviceUuid, characteristicUuid, error: errorMessage }, 'error');
      return { success: false, error: errorMessage };
    }
  }, [selectedDevice]);
  const subscribeToNotifications = useCallback(async (
    serviceUuid: string, 
    characteristicUuid: string, 
    callback: (value: DataView) => void
  ): Promise<BleOperationResult> => {
    if (!selectedDevice?.gatt?.connected) {
      return { success: false, error: BLE_ERROR_MESSAGES.DEVICE_DISCONNECTED };
    }

    try {
      const service = await selectedDevice.gatt.getPrimaryService(serviceUuid);
      const characteristic = await service.getCharacteristic(characteristicUuid);
      
      if (!characteristic.properties.notify) {
        return { success: false, error: BLE_ERROR_MESSAGES.NOTIFICATION_FAILED };
      }

      await characteristic.startNotifications();
      
      const notificationHandler = (event: any) => {
        callback(event.target.value);
      };
      
      characteristic.addEventListener('characteristicvaluechanged', notificationHandler);
      
      // Store callback for cleanup
      const key = `${serviceUuid}:${characteristicUuid}`;
      setNotificationCallbacks(prev => new Map(prev.set(key, callback)));
      
      logBleOperation('Notification Subscription', { serviceUuid, characteristicUuid });
      return { success: true };
    } catch (error: any) {
      const errorMessage = parseBleError(error);
      setError(errorMessage);
      logBleOperation('Notification Subscription Failed', { serviceUuid, characteristicUuid, error: errorMessage }, 'error');
      return { success: false, error: errorMessage };
    }
  }, [selectedDevice]);
  const unsubscribeFromNotifications = useCallback(async (serviceUuid: string, characteristicUuid: string): Promise<BleOperationResult> => {
    if (!selectedDevice?.gatt?.connected) {
      return { success: false, error: BLE_ERROR_MESSAGES.DEVICE_DISCONNECTED };
    }

    try {
      const service = await selectedDevice.gatt.getPrimaryService(serviceUuid);
      const characteristic = await service.getCharacteristic(characteristicUuid);
      
      await characteristic.stopNotifications();
      
      // Remove callback
      const key = `${serviceUuid}:${characteristicUuid}`;
      setNotificationCallbacks(prev => {
        const newMap = new Map(prev);
        newMap.delete(key);
        return newMap;
      });
      
      logBleOperation('Notification Unsubscription', { serviceUuid, characteristicUuid });
      return { success: true };
    } catch (error: any) {
      const errorMessage = parseBleError(error);
      setError(errorMessage);
      logBleOperation('Notification Unsubscription Failed', { serviceUuid, characteristicUuid, error: errorMessage }, 'error');
      return { success: false, error: errorMessage };
    }
  }, [selectedDevice]);

  const getDeviceInfo = useCallback((deviceId: string): BleDevice | null => {
    return permittedDevices.find(d => d.id === deviceId) || null;
  }, [permittedDevices]);

  const getDeviceServices = useCallback((deviceId: string): BleServiceInfo[] => {
    const device = getDeviceInfo(deviceId);
    return device?.services || [];
  }, [getDeviceInfo]);
  return {
    // Device management
    permittedDevices,
    connectedDevices,
    selectedDevice,
    
    // Connection state
    isScanning,
    isConnecting,
    connectionStatus,
      // Actions
    scanForDevices,
    connectToDevice,
    disconnectDevice,
    selectDevice,
    refreshPermittedDevices: loadPersistedDevices,
    
    // Device operations
    readCharacteristic,
    writeCharacteristic,
    subscribeToNotifications,
    unsubscribeFromNotifications,
      // Utility functions
    isWebBluetoothSupported: () => isWebBluetoothSupported(),
    getDeviceInfo,
    getDeviceServices,
    
    // Error handling
    lastError,
    clearError
  };
};

export default useBleManager;
