#!/bin/bash

# CCALC iOS Project Diagnostic Script
# This script analyzes the project setup and identifies issues

echo "🔍 CCALC iOS Project Diagnostic"
echo "================================"

# Check current directory
echo ""
echo "📍 Current Directory: $(pwd)"

if [ ! -f "Podfile" ]; then
    echo "❌ Not in iOS directory. Please run from: CCALC/app/ios/"
    exit 1
fi

echo "✅ Running from correct directory"

# System Information
echo ""
echo "🖥️  System Information:"
echo "   OS: $(uname -s) $(uname -r)"
echo "   Architecture: $(uname -m)"

if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "   macOS Version: $(sw_vers -productVersion)"
    echo "   Xcode Version: $(xcodebuild -version 2>/dev/null | head -1 || echo "Not installed")"
else
    echo "   ⚠️  Not running on macOS - iOS development limited"
fi

# Node.js and React Native
echo ""
echo "📦 Development Environment:"
if command -v node &> /dev/null; then
    echo "   ✅ Node.js: $(node --version)"
else
    echo "   ❌ Node.js: Not installed"
fi

if command -v npm &> /dev/null; then
    echo "   ✅ npm: $(npm --version)"
else
    echo "   ❌ npm: Not installed"
fi

if command -v npx &> /dev/null; then
    echo "   ✅ npx: Available"
    if npx react-native --version &> /dev/null; then
        echo "   ✅ React Native CLI: $(npx react-native --version | head -1)"
    else
        echo "   ❌ React Native CLI: Not available"
    fi
else
    echo "   ❌ npx: Not available"
fi

# CocoaPods
echo ""
echo "🍫 CocoaPods:"
if command -v pod &> /dev/null; then
    echo "   ✅ CocoaPods: $(pod --version)"
    
    if command -v gem &> /dev/null; then
        echo "   ✅ Ruby Gems: $(gem --version)"
    fi
else
    echo "   ❌ CocoaPods: Not installed"
    echo "      Install with: brew install cocoapods"
    echo "      Or: sudo gem install cocoapods"
fi

# Project Structure Analysis
echo ""
echo "📁 Project Structure:"

# Check critical files
critical_files=(
    "Podfile:CocoaPods configuration"
    "CCALC.xcodeproj/project.pbxproj:Xcode project file"
    "CCALC.xcworkspace/contents.xcworkspacedata:Xcode workspace"
    "CCALC/AppDelegate.h:iOS app delegate header"
    "CCALC/AppDelegate.mm:iOS app delegate implementation"
    "CCALC/main.m:iOS app entry point"
    "CCALC/Info.plist:iOS app configuration"
    "CCALC/CCLCVoice.h:Custom native module header"
    "CCALC/CCLCVoice.m:Custom native module implementation"
)

for file_desc in "${critical_files[@]}"; do
    IFS=':' read -r file desc <<< "$file_desc"
    if [ -f "$file" ]; then
        echo "   ✅ $file ($desc)"
    else
        echo "   ❌ $file ($desc) - MISSING"
    fi
done

# Check optional but important files
optional_files=(
    "Pods:CocoaPods dependencies"
    "Podfile.lock:CocoaPods lock file"
    "CCALC/LaunchScreen.storyboard:Launch screen"
    "CCALC/Images.xcassets:App assets"
    "CCALC/PrivacyInfo.xcprivacy:Privacy manifest"
)

echo ""
echo "📋 Optional Files:"
for file_desc in "${optional_files[@]}"; do
    IFS=':' read -r file desc <<< "$file_desc"
    if [ -e "$file" ]; then
        echo "   ✅ $file ($desc)"
    else
        echo "   ⚠️  $file ($desc) - Missing"
    fi
done

# Check for redundant/conflicting files
echo ""
echo "🗂️  Potential Issues:"

redundant_files=(
    "iosproject:Legacy iOS project directory"
    "build:Build artifacts (can be cleaned)"
    "DerivedData:Xcode derived data (can be cleaned)"
)

found_redundant=false
for file_desc in "${redundant_files[@]}"; do
    IFS=':' read -r file desc <<< "$file_desc"
    if [ -e "$file" ]; then
        echo "   ⚠️  $file ($desc) - Consider removing"
        found_redundant=true
    fi
done

if [ "$found_redundant" = false ]; then
    echo "   ✅ No redundant files detected"
fi

# Package.json analysis
echo ""
echo "📦 Dependencies:"
if [ -f "../package.json" ]; then
    echo "   ✅ package.json found in app root"
    
    # Check for React Native version
    if command -v node &> /dev/null; then
        rn_version=$(node -e "console.log(require('../package.json').dependencies['react-native'] || 'Not found')" 2>/dev/null)
        echo "   📱 React Native: $rn_version"
        
        expo_version=$(node -e "console.log(require('../package.json').dependencies['expo'] || 'Not found')" 2>/dev/null)
        echo "   🔮 Expo: $expo_version"
    fi
else
    echo "   ❌ package.json not found in app root"
fi

# Xcode Project Analysis
echo ""
echo "🔨 Xcode Configuration:"

if [ -f "CCALC.xcodeproj/project.pbxproj" ]; then
    # Check project format
    if grep -q "objectVersion = 54" CCALC.xcodeproj/project.pbxproj; then
        echo "   ✅ Modern Xcode project format"
    else
        echo "   ⚠️  Older Xcode project format detected"
    fi
    
    # Check for schemes
    if [ -d "CCALC.xcodeproj/xcshareddata/xcschemes" ]; then
        scheme_count=$(ls CCALC.xcodeproj/xcshareddata/xcschemes/*.xcscheme 2>/dev/null | wc -l)
        echo "   ✅ Shared schemes: $scheme_count"
    else
        echo "   ❌ No shared schemes found - required for team development"
    fi
    
    # Check bundle identifier
    if grep -q "com.ccalc.app" CCALC.xcodeproj/project.pbxproj; then
        echo "   ✅ Bundle identifier configured"
    else
        echo "   ⚠️  Bundle identifier may need configuration"
    fi
else
    echo "   ❌ Xcode project file not found"
fi

# Summary and Recommendations
echo ""
echo "📋 Summary and Recommendations:"
echo "================================"

# Count critical issues
critical_missing=0
for file_desc in "${critical_files[@]}"; do
    IFS=':' read -r file desc <<< "$file_desc"
    if [ ! -f "$file" ]; then
        ((critical_missing++))
    fi
done

if [ $critical_missing -eq 0 ]; then
    echo "✅ Core project structure is complete!"
else
    echo "❌ $critical_missing critical files missing"
fi

# CocoaPods status
if [ ! -d "Pods" ] && [ -f "Podfile" ]; then
    echo "🍫 Run 'pod install' to set up dependencies"
fi

# Next steps
echo ""
echo "🚀 Next Steps:"
if [[ "$OSTYPE" == "darwin"* ]]; then
    if command -v pod &> /dev/null; then
        if [ ! -d "Pods" ]; then
            echo "1. Run: pod install"
        fi
        echo "2. Open: open CCALC.xcworkspace"
        echo "3. Build and run in Xcode"
    else
        echo "1. Install CocoaPods: brew install cocoapods"
        echo "2. Run: pod install"
        echo "3. Open: open CCALC.xcworkspace"
    fi
else
    echo "1. For macOS development: Install Xcode and CocoaPods"
    echo "2. For Windows development: Use React Native CLI or EAS Build"
    echo "3. Alternative: Use Expo Go for testing"
fi

echo ""
echo "📞 Need Help?"
echo "   Check: ios/README-XCODE.md for detailed instructions"
echo "   Run: ./install-pods.sh (on macOS) for automated setup"
