/**
 * Network Service - Enhanced fetch with iOS compatibility
 * Provides better error handling and debugging for network requests
 */

import { Platform } from 'react-native';

export interface NetworkRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS';
  headers?: Record<string, string>;
  body?: string;
  timeout?: number;
}

export interface NetworkResponse {
  ok: boolean;
  status: number;
  statusText: string;
  data: any;
  headers: Record<string, string>;
}

export class NetworkService {
  private static instance: NetworkService;
  
  public static getInstance(): NetworkService {
    if (!NetworkService.instance) {
      NetworkService.instance = new NetworkService();
    }
    return NetworkService.instance;
  }

  /**
   * Enhanced fetch with better error handling and iOS compatibility
   */
  public async request(url: string, options: NetworkRequestOptions = {}): Promise<NetworkResponse> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = 10000
    } = options;

    console.log(`🌐 [NetworkService] ${method} ${url}`);
    console.log(`🔧 [NetworkService] Platform: ${Platform.OS}`);
    console.log(`📦 [NetworkService] Headers:`, headers);
    if (body) console.log(`📝 [NetworkService] Body:`, body);

    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Request timeout after ${timeout}ms`)), timeout);
    });

    try {
      // Create the fetch request
      const fetchPromise = fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
        body,
      });

      // Race between fetch and timeout
      const response = await Promise.race([fetchPromise, timeoutPromise]);

      console.log(`📡 [NetworkService] Response status: ${response.status}`);
      console.log(`📡 [NetworkService] Response ok: ${response.ok}`);

      // Get response headers
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      console.log(`🔍 [NetworkService] Response headers:`, responseHeaders);

      // Try to parse response data
      let data: any;
      const contentType = response.headers.get('content-type') || '';
      
      try {
        if (contentType.includes('application/json')) {
          data = await response.json();
        } else {
          data = await response.text();
        }
        console.log(`📋 [NetworkService] Response data:`, data);
      } catch (parseError) {
        console.warn(`⚠️ [NetworkService] Failed to parse response:`, parseError);
        data = null;
      }

      return {
        ok: response.ok,
        status: response.status,
        statusText: response.statusText,
        data,
        headers: responseHeaders,
      };

    } catch (error) {
      console.error(`❌ [NetworkService] Fetch error:`, error);
      
      // Enhanced error logging for debugging
      if (error instanceof Error) {
        console.error(`🔍 [NetworkService] Error details:`, {
          name: error.name,
          message: error.message,
          stack: error.stack,
        });
      }

      // Re-throw with enhanced context
      throw new Error(`Network request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convenience method for GET requests
   */
  public async get(url: string, headers?: Record<string, string>): Promise<NetworkResponse> {
    return this.request(url, { method: 'GET', headers });
  }

  /**
   * Convenience method for POST requests
   */
  public async post(url: string, data: any, headers?: Record<string, string>): Promise<NetworkResponse> {
    return this.request(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });
  }

  /**
   * Test network connectivity
   */
  public async testConnectivity(baseUrl: string): Promise<boolean> {
    try {
      console.log(`🧪 [NetworkService] Testing connectivity to ${baseUrl}`);
      
      const response = await this.get(`${baseUrl}/health`);
      const isHealthy = response.ok && response.status === 200;
      
      console.log(`${isHealthy ? '✅' : '❌'} [NetworkService] Health check result:`, isHealthy);
      
      return isHealthy;
    } catch (error) {
      console.error(`❌ [NetworkService] Connectivity test failed:`, error);
      return false;
    }
  }
}
