// AES, RSA, NaCl helpers
// AES encryption/decryption helpers for sensitive BLE data
import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const KEY = process.env.BLE_SECRET_KEY
  ? Buffer.from(process.env.BLE_SECRET_KEY, 'base64')
  : crypto.randomBytes(32); // 32 bytes for AES-256
const IV_LENGTH = 12; // GCM recommended IV length

export function encrypt(text: string): { iv: string; content: string; tag: string } {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
  let encrypted = cipher.update(text, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  const tag = cipher.getAuthTag();
  return {
    iv: iv.toString('base64'),
    content: encrypted,
    tag: tag.toString('base64'),
  };
}

export function decrypt(encrypted: { iv: string; content: string; tag: string }): string {
  const iv = Buffer.from(encrypted.iv, 'base64');
  const tag = Buffer.from(encrypted.tag, 'base64');
  const decipher = crypto.createDecipheriv(ALGORITHM, KEY, iv);
  decipher.setAuthTag(tag);
  let decrypted = decipher.update(encrypted.content, 'base64', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
