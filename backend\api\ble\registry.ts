import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../backend/config/database';
import Device from '../../../backend/models/Device';
import User from '../../../backend/models/User';
import Joi from 'joi';
import { encrypt } from '../../../backend/utils/crypto';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  await dbConnect();
  if (req.method === 'GET') {
    // List all BLE devices
    const devices = await Device.find({ isActive: true }, {
      userId: 1, 'ble.uuid': 1, 'ble.advertisementData': 1, 'ble.characteristics': 1, _id: 1
    }).lean();
    res.status(200).json(devices.map(d => ({
      id: d._id,
      userId: d.userId,
      uuid: d.ble?.uuid,
      adData: d.ble?.advertisementData?.manufacturerData || '',
      name: d.ble?.advertisementData?.localName || '',
      characteristics: d.ble?.characteristics || []
    })));
  }
  if (req.method === 'POST') {
    // Register/update BLE device for a user
    const schema = Joi.object({
      userId: Joi.string().required(),
      name: Joi.string().max(64).allow(''),
      uuid: Joi.string().required(),
      adData: Joi.string().max(256).allow(''),
      serviceUUID: Joi.string().max(64).allow(''),
      characteristicUUID: Joi.string().max(64).allow(''),
      authData: Joi.string().max(512).allow(''),
    });
    const { error, value } = schema.validate(req.body);
    if (error) res.status(400).json({ error: error.details[0].message });
    const { userId, name, uuid, adData, serviceUUID, characteristicUUID, authData } = value;
    try {
      let device = await Device.findOne({ userId, 'ble.uuid': uuid });
      let encryptedAuth: any = undefined;
      if (authData) {
        encryptedAuth = encrypt(authData);
      }
      if (!device) {
        // Check for duplicate deviceId globally (not just for this user)
        const duplicate = await Device.findOne({ deviceId: uuid });
        if (duplicate) {
          res.status(409).json({ error: 'A device with this UUID is already registered.' });
        }
        device = new Device({
          userId,
          deviceId: uuid,
          fingerprint: { hash: '', components: {}, confidence: 0 },
          ble: {
            uuid,
            advertisementData: { localName: name, manufacturerData: adData },
            characteristics: serviceUUID && characteristicUUID && encryptedAuth ? [{ serviceUUID, characteristicUUID, authData: JSON.stringify(encryptedAuth) }] : [],
            lastSeen: new Date(),
            verified: false
          },
          metadata: { registeredAt: new Date(), lastActiveAt: new Date() },
          security: { status: 'pending', riskScore: 0, violations: [], challenges: [] },
          network: { ipAddress: req.socket.remoteAddress || '', lastKnownIPs: [], connectionHistory: [] },
          isActive: true
        });
      } else {
        device.ble.advertisementData = { localName: name, manufacturerData: adData };
        if (serviceUUID && characteristicUUID && encryptedAuth) {
          device.ble.characteristics = [{ serviceUUID, characteristicUUID, authData: JSON.stringify(encryptedAuth) }];
        }
        device.ble.lastSeen = new Date();
        device.isActive = true;
      }
      await device.save();
      res.status(200).json({ success: true });
    } catch (err: any) {
      if (err.name === 'ValidationError') {
        res.status(400).json({ error: 'Invalid device data.' });
      }
      if (err.code === 11000) {
        res.status(409).json({ error: 'A device with this UUID is already registered.' });
      }
      res.status(500).json({ error: 'Failed to save device.' });
    }
  }
  if (req.method === 'DELETE') {
    // Remove BLE device by id
    const { id } = req.query;
    if (!id) res.status(400).json({ error: 'id required' });
    await Device.findByIdAndDelete(id);
    res.status(200).json({ success: true });
  }
  res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
  res.status(405).end(`Method ${req.method} Not Allowed`);
}
