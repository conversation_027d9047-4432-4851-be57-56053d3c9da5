import React from 'react';
import { View, Text } from 'react-native';

interface LoadingSpinnerProps {
  size?: 'small' | 'large';
  color?: string;
  style?: any;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  color = '#007AFF',
  style
}) => {
  const spinnerSize = size === 'large' ? 24 : 18;
  const fontSize = size === 'large' ? 20 : 16;
  
  return (
    <View style={[
      {
        justifyContent: 'center',
        alignItems: 'center',
        width: spinnerSize,
        height: spinnerSize,
      },
      style
    ]}>
      <Text style={{ 
        fontSize, 
        color, 
        fontWeight: 'bold',
        textAlign: 'center'
      }}>
        ⟳
      </Text>
    </View>
  );
};

export default LoadingSpinner;
