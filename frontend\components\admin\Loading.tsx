import React from 'react';
import Icon from './Icon';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullPage?: boolean;
  overlay?: boolean;
}

export default function Loading({ 
  size = 'md', 
  text = 'Loading...', 
  fullPage = false, 
  overlay = false 
}: LoadingProps) {
  const containerClass = fullPage 
    ? 'loading-container loading-container--full-page'
    : overlay 
    ? 'loading-container loading-container--overlay'
    : 'loading-container';

  const spinnerClass = `loading-spinner loading-spinner--${size}`;

  return (
    <div className={containerClass}>
      <div className="loading-content">
        <div className={spinnerClass} />
        {text && <p className="loading-text">{text}</p>}
      </div>
    </div>
  );
}

// Skeleton loading components for specific use cases
export function TableSkeleton({ rows = 5 }: { rows?: number }) {
  return (
    <div className="table-skeleton">
      <div className="table-skeleton-header">
        <div className="skeleton-bar skeleton-bar--sm" />
        <div className="skeleton-bar skeleton-bar--md" />
        <div className="skeleton-bar skeleton-bar--sm" />
        <div className="skeleton-bar skeleton-bar--lg" />
      </div>
      {Array.from({ length: rows }).map((_, index) => (
        <div key={index} className="table-skeleton-row">
          <div className="skeleton-bar skeleton-bar--sm" />
          <div className="skeleton-bar skeleton-bar--md" />
          <div className="skeleton-bar skeleton-bar--sm" />
          <div className="skeleton-bar skeleton-bar--lg" />
        </div>
      ))}
    </div>
  );
}

export function CardSkeleton() {
  return (
    <div className="card">
      <div className="card-body">
        <div className="card-skeleton">
          <div className="skeleton-bar skeleton-bar--lg mb-3" />
          <div className="skeleton-bar skeleton-bar--md mb-2" />
          <div className="skeleton-bar skeleton-bar--sm" />
        </div>
      </div>
    </div>
  );
}

export function StatCardSkeleton() {
  return (
    <div className="card">
      <div className="card-body">
        <div className="stat-card-skeleton">
          <div className="flex items-center justify-between">
            <div>
              <div className="skeleton-bar skeleton-bar--md mb-2" />
              <div className="skeleton-bar skeleton-bar--lg" />
            </div>
            <div className="skeleton-circle skeleton-circle--lg" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading states for specific components
interface LoadingStateProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function LoadingState({ isLoading, children, fallback }: LoadingStateProps) {
  if (isLoading) {
    return fallback ? <>{fallback}</> : <Loading />;
  }
  return <>{children}</>;
}

// Inline loading indicator
export function InlineLoading({ text = 'Loading' }: { text?: string }) {
  return (
    <div className="inline-loading">
      <div className="loading-spinner loading-spinner--sm" />
      <span className="inline-loading-text">{text}</span>
    </div>
  );
}

// Button loading state
export function ButtonLoading() {
  return <div className="loading-spinner loading-spinner--xs" />;
}
