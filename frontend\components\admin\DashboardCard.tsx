import React, { ReactNode } from 'react';
import Icon, { IconName } from './Icon'; // Import Icon component and IconName type

interface DashboardCardProps {
  title?: string;
  children?: ReactNode; // Made children optional for stat cards that don't need extra children
  className?: string;
  fullWidth?: boolean;
  noPadding?: boolean;
  // Props for stat card variant
  iconName?: IconName; // Use proper IconName type
  iconBgClass?: string;    // e.g., 'bg-primary-10'
  iconTextClass?: string;  // e.g., 'text-primary'
  value?: string | number;
  label?: string;
  valueColorClass?: string; // e.g., 'text-primary'
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  children,
  className = '',
  fullWidth = false,
  noPadding = false,
  iconName,
  iconBgClass,
  iconTextClass,
  value,
  label,
  valueColorClass
}) => {
  const baseCardClasses = "card"; // Uses global .card style
  const fullWidthClass = fullWidth ? 'card--full' : '';
  
  // Determine body padding class - only apply if not a stat card or if explicitly noPadding is false for a stat card
  // Stat cards typically have their padding managed internally by the flex layout.
  const bodyPaddingClass = noPadding ? 'card-body--no-padding' : 'card-body';

  // If iconName, value, and label are provided, render as a stat card
  if (iconName && value !== undefined && label) {
    return (
      <div className={`${baseCardClasses} ${className} ${fullWidthClass}`}>
        {/* Stat card content uses card-body for consistent padding unless noPadding is true */}
        <div className={`card-body flex items-center ${noPadding ? 'p-0' : ''}`}>
          <div className={`p-3 rounded-full mr-4 ${iconBgClass || 'bg-accent-10'} flex items-center justify-center`}>
            <Icon name={iconName} size={20} className={`${iconTextClass || 'text-accent'}`} />
          </div>
          <div>
            <div className={`text-2xl font-bold ${valueColorClass || 'text-text'}`}>{value}</div>
            <div className="text-sm text-muted mt-1">{label}</div>
          </div>
        </div>
        {/* Render children if provided, typically for cards that are stat cards but also have other content */}
        {children && (
           <div className={bodyPaddingClass}>{children}</div>
        )}
      </div>
    );
  }

  // Default card rendering (title + children)
  return (
    <div className={`${baseCardClasses} ${className} ${fullWidthClass}`}>
      {title && (
        <div className="card-header">
          <h3 className="card-title">{title}</h3>
        </div>
      )}
      {/* Children are required for a non-stat card */}
      {children && (
        <div className={bodyPaddingClass}>
          {children}
        </div>
      )}
    </div>
  );
};

export default DashboardCard;
