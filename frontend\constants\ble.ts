/**
 * BLE Constants and Configuration
 * Centralized location for all BLE-related constants, UUIDs, and configuration values
 */

// Calculator Service UUIDs
export const CALCULATOR_SERVICE_UUID = '00001234-0000-1000-8000-00805f9b34fb';
export const CALCULATOR_KEY_CHARACTERISTIC_UUID = '00002345-0000-1000-8000-00805f9b34fb';

// Development/Testing UUIDs
export const DEV_CALCULATOR_SERVICE_UUID = '0000cac0-0000-1000-8000-00805f9b34fb';
export const DEV_CALCULATOR_CHAR_UUID = '0000cea0-0000-1000-8000-00805f9b34fb';

// Legacy/Custom UUIDs (for backward compatibility)
export const CUSTOM_SERVICE_UUID = CALCULATOR_SERVICE_UUID;
export const CUSTOM_CHAR_UUID = CALCULATOR_KEY_CHARACTERISTIC_UUID;

// Standard Bluetooth Service UUIDs
export const STANDARD_SERVICES = {
  BATTERY_SERVICE: '0000180f-0000-1000-8000-00805f9b34fb',
  DEVICE_INFORMATION: '0000180a-0000-1000-8000-00805f9b34fb',
  HEART_RATE: '0000180d-0000-1000-8000-00805f9b34fb',
  FITNESS_MACHINE: '00001826-0000-1000-8000-00805f9b34fb',
  AUDIO_SINK: '0000110b-0000-1000-8000-00805f9b34fb',
  A2DP: '0000110e-0000-1000-8000-00805f9b34fb',
  HANDS_FREE: '0000111e-0000-1000-8000-00805f9b34fb',
} as const;

// Connection Configuration
export const CONNECTION_CONFIG = {
  DEFAULT_SCAN_DURATION: 10000, // 10 seconds
  DEFAULT_CONNECTION_TIMEOUT: 15000, // 15 seconds
  AUTO_REFRESH_INTERVAL: 15000, // 15 seconds
  MAX_RETRY_ATTEMPTS: 3,
  PROGRESS_UPDATE_INTERVAL: 100, // milliseconds
} as const;

// RSSI Configuration (signal strength)
export const RSSI_CONFIG = {
  MIN_RSSI: -100, // dBm
  MAX_RSSI: -30,  // dBm
  EXCELLENT_THRESHOLD: -50,
  GOOD_THRESHOLD: -60,
  FAIR_THRESHOLD: -70,
  POOR_THRESHOLD: -80,
} as const;

// Error Messages
export const BLE_ERROR_MESSAGES = {
  NOT_SUPPORTED: 'Web Bluetooth is not supported in this browser. Please use Chrome, Edge, or Opera.',
  DISABLED: 'Web Bluetooth is disabled. Please enable it in your browser settings.',
  PERMISSION_DENIED: 'Bluetooth permission was denied. Please enable Bluetooth permissions in your browser settings.',
  NOT_FOUND: 'No Bluetooth device was selected or found.',
  NOT_ALLOWED: 'Web Bluetooth is disabled by policy or browser settings. Please enable it to use BLE features.',
  SECURITY_ERROR: 'Localhost secure context issue: Make sure you\'re using https or localhost exactly (not 127.0.0.1).',
  HTTPS_REQUIRED: 'Web Bluetooth requires a secure context (HTTPS). For localhost development, use "localhost" instead of an IP address.',
  CONNECTION_TIMEOUT: 'Connection timeout - device did not respond within the expected time.',
  DEVICE_DISCONNECTED: 'Device disconnected unexpectedly.',
  SERVICE_NOT_FOUND: 'Required service not found on this device.',
  CHARACTERISTIC_NOT_FOUND: 'Required characteristic not found on this device.',
  READ_FAILED: 'Failed to read from characteristic.',  WRITE_FAILED: 'Failed to write to characteristic.',
  NOTIFICATION_FAILED: 'Failed to subscribe to notifications.',
  ENVIRONMENT_ERROR: 'BLE environment validation failed.',
  KEY_GENERATION_FAILED: 'Failed to generate BLE key. Please try again.',
} as const;

// Device Type Detection Patterns
export const DEVICE_PATTERNS = {
  CALCULATOR: {
    names: ['calculator', 'calc'],
    services: [CALCULATOR_SERVICE_UUID, DEV_CALCULATOR_SERVICE_UUID],
  },
  AUDIO: {
    names: ['earbud', 'headphone', 'audio', 'airpods', 'buds'],
    services: [STANDARD_SERVICES.AUDIO_SINK, STANDARD_SERVICES.A2DP, STANDARD_SERVICES.HANDS_FREE],
  },
} as const;

// Scan Configuration Presets
export const SCAN_CONFIGS = {
  CALCULATOR_DEVICES: {
    filters: [
      { services: [CALCULATOR_SERVICE_UUID] },
      { services: [DEV_CALCULATOR_SERVICE_UUID] },
    ],
    optionalServices: [CALCULATOR_SERVICE_UUID, DEV_CALCULATOR_SERVICE_UUID],
    deviceType: 'calculator' as const,
  },
  GENERIC_DEVICES: {
    filters: [
      { services: [STANDARD_SERVICES.DEVICE_INFORMATION] },
      { services: [STANDARD_SERVICES.BATTERY_SERVICE] },
    ],
    optionalServices: [STANDARD_SERVICES.DEVICE_INFORMATION, STANDARD_SERVICES.BATTERY_SERVICE],
    deviceType: 'generic' as const,
  },
  AUDIO_DEVICES: {
    filters: [
      { services: [STANDARD_SERVICES.AUDIO_SINK] },
      { services: [STANDARD_SERVICES.A2DP] },
    ],
    optionalServices: [
      STANDARD_SERVICES.AUDIO_SINK,
      STANDARD_SERVICES.A2DP,
      STANDARD_SERVICES.HANDS_FREE,
    ],
    deviceType: 'generic' as const,
  },
} as const;

// Default Service Filter for broad compatibility
export const DEFAULT_SERVICE_FILTER = [
  STANDARD_SERVICES.BATTERY_SERVICE,
  STANDARD_SERVICES.DEVICE_INFORMATION,
  CALCULATOR_SERVICE_UUID,
  DEV_CALCULATOR_SERVICE_UUID,
];

// Browser Compatibility Information
export const BROWSER_SUPPORT = {
  CHROME: { supported: true, minVersion: 56 },
  EDGE: { supported: true, minVersion: 79 },
  OPERA: { supported: true, minVersion: 43 },
  SAMSUNG_INTERNET: { supported: true, minVersion: 6.2 },
  FIREFOX: { supported: false, reason: 'Web Bluetooth not implemented' },
  SAFARI: { supported: false, reason: 'Web Bluetooth not implemented' },
  IE: { supported: false, reason: 'Not supported' },
} as const;

// Development Environment Detection
export const DEV_ENVIRONMENT = {
  LOCALHOST_PATTERNS: ['localhost', '127.0.0.1', '192.168.', '.local'],
  SECURE_CONTEXTS: ['https:', 'localhost', 'file:'],
} as const;

// Utility Functions for Constants
export const isLocalhostEnvironment = (): boolean => {
  if (typeof window === 'undefined') return false;
  const hostname = window.location.hostname;
  return DEV_ENVIRONMENT.LOCALHOST_PATTERNS.some(pattern => 
    hostname === pattern || hostname.includes(pattern)
  );
};

export const isSecureContext = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.isSecureContext;
};

export const getBrowserCompatibility = (): { supported: boolean; reason?: string } => {
  if (typeof navigator === 'undefined') {
    return { supported: false, reason: 'Navigator not available' };
  }

  const userAgent = navigator.userAgent.toLowerCase();
  
  if (userAgent.includes('chrome') && !userAgent.includes('edge')) {
    return BROWSER_SUPPORT.CHROME;
  } else if (userAgent.includes('edge')) {
    return BROWSER_SUPPORT.EDGE;
  } else if (userAgent.includes('opera')) {
    return BROWSER_SUPPORT.OPERA;
  } else if (userAgent.includes('samsung')) {
    return BROWSER_SUPPORT.SAMSUNG_INTERNET;
  } else if (userAgent.includes('firefox')) {
    return BROWSER_SUPPORT.FIREFOX;
  } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
    return BROWSER_SUPPORT.SAFARI;
  } else {
    return { supported: false, reason: 'Unknown browser' };
  }
};

export const getSignalStrengthLevel = (rssi?: number): 'excellent' | 'good' | 'fair' | 'poor' | 'unknown' => {
  if (rssi === undefined) return 'unknown';
  
  if (rssi >= RSSI_CONFIG.EXCELLENT_THRESHOLD) return 'excellent';
  if (rssi >= RSSI_CONFIG.GOOD_THRESHOLD) return 'good';
  if (rssi >= RSSI_CONFIG.FAIR_THRESHOLD) return 'fair';
  if (rssi >= RSSI_CONFIG.POOR_THRESHOLD) return 'poor';
  return 'poor';
};
