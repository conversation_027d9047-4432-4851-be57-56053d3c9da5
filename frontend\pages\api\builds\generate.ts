import type { NextApiRequest, NextApiResponse } from 'next';
import { parse } from 'cookie';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get admin token from cookies
    const cookies = parse(req.headers.cookie || '');
    const token = cookies['admin-token'];

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { userId, deviceId, expiryDays, features } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Forward request to backend
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/builds/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        userId,
        deviceId,
        expiryDays,
        features
      })
    });

    const data = await response.json();
    return res.status(response.status).json(data);
  } catch (error) {
    console.error('Build generation error:', error);
    return res.status(500).json({ error: 'Failed to generate build' });
  }
}
