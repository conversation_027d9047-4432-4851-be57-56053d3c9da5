import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';

export function encryptMessage(text: string, key?: string): { encrypted: string; iv: string; tag: string; keyHash: string } {
  const encryptionKey = key || crypto.randomBytes(32);
  const keyBuffer = typeof encryptionKey === 'string' ? Buffer.from(encryptionKey, 'hex') : encryptionKey;
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipheriv(ALGORITHM, keyBuffer, iv);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  const tag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex'),
    keyHash: crypto.createHash('sha256').update(keyBuffer).digest('hex')
  };
}

export function decryptMessage(encryptedData: { encrypted: string; iv: string; tag: string }, key: string): string {
  const keyBuffer = Buffer.from(key, 'hex');
  const decipher = crypto.createDecipheriv(ALGORITHM, keyBuffer, Buffer.from(encryptedData.iv, 'hex'));
  decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
  
  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

export function encryptFile(buffer: Buffer, key?: string, _iv?: string): { encrypted: Buffer; iv: string; tag: string; keyHash: string } {
  const encryptionKey = key || crypto.randomBytes(32);
  const keyBuffer = typeof encryptionKey === 'string' ? Buffer.from(encryptionKey, 'hex') : encryptionKey;
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipheriv(ALGORITHM, keyBuffer, iv);
  const encrypted = Buffer.concat([cipher.update(buffer), cipher.final()]);
  const tag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex'),
    keyHash: crypto.createHash('sha256').update(keyBuffer).digest('hex')
  };
}

export function decryptFile(encryptedData: { encrypted: Buffer; iv: string; tag: string }, key: string, _iv?: string): Buffer {
  const keyBuffer = Buffer.from(key, 'hex');
  const decipher = crypto.createDecipheriv(ALGORITHM, keyBuffer, Buffer.from(encryptedData.iv, 'hex'));
  decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
  
  return Buffer.concat([decipher.update(encryptedData.encrypted), decipher.final()]);
}

export function generateEncryptionKey(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Chat-specific encryption utilities for admin panel
 */

export interface EncryptedChatData {
  data: string;
  iv: string;
  salt: string;
  isEncrypted: true;
}

export class ChatEncryption {
  /**
   * Generate a secure encryption key based on user credentials
   * This matches the mobile app's key generation
   */
  static generateKey(userId: string, deviceFingerprint: string): string {
    const keyMaterial = `${userId}:${deviceFingerprint}:ccalc-chat-key`;
    return crypto.createHash('sha256').update(keyMaterial).digest('hex');
  }

  /**
   * Try to parse and decrypt a message if it's encrypted
   */
  static async tryDecryptMessage(
    messageText: string,
    userId: string,
    deviceFingerprint: string
  ): Promise<string> {
    try {
      // Try to parse as encrypted data
      const encryptedData = JSON.parse(messageText);
      
      if (encryptedData.isEncrypted) {
        const key = this.generateKey(userId, deviceFingerprint);
        
        // Use the existing decryptMessage with adaptation
        const decrypted = decryptMessage({
          encrypted: encryptedData.data,
          iv: encryptedData.iv,
          tag: encryptedData.salt // Using salt as tag for now
        }, key);
        
        const parsedContent = JSON.parse(decrypted);
        return parsedContent.text || messageText;
      }
      
      return messageText;
    } catch (error) {
      // If parsing/decryption fails, return original text
      console.warn('Failed to decrypt message, using original text:', error);
      return messageText;
    }
  }

  /**
   * Decrypt attachment if encrypted
   */
  static async tryDecryptAttachment(
    attachmentData: any,
    userId: string,
    deviceFingerprint: string
  ): Promise<Buffer | null> {
    try {
      if (attachmentData.isEncrypted) {
        const key = this.generateKey(userId, deviceFingerprint);
        
        // Use existing file decryption logic
        const keyBuffer = Buffer.from(key, 'hex');
        const iv = Buffer.from(attachmentData.iv, 'hex');
        const decipher = crypto.createDecipheriv('aes-256-gcm', keyBuffer, iv);
        
        if (attachmentData.tag) {
          decipher.setAuthTag(Buffer.from(attachmentData.tag, 'hex'));
        }
        
        let decrypted = decipher.update(Buffer.from(attachmentData.data, 'base64'));
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        
        return decrypted;
      }
      
      return null; // Not encrypted
    } catch (error) {
      console.error('Failed to decrypt attachment:', error);
      return null;
    }
  }
}
