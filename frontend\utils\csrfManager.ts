/**
 * CSRF Protection Manager
 * Handles CSRF token management and axios interceptors
 */

import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { isBrowser, safeLocalStorage } from './browserUtils';

interface CSRFConfig {
  tokenEndpoint: string;
  headerName: string;
  cookieName: string;
  excludePaths: string[];
  retryOnFailure: boolean;
  maxRetries: number;
}

class CSRFManager {
  private config: CSRFConfig = {
    tokenEndpoint: (process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000') + '/api/csrf-token',
    headerName: 'X-CSRF-Token',
    cookieName: 'csrf-token',
    excludePaths: [
      '/api/csrf-token', 
      '/api/auth/login', 
      '/api/auth/register',
      '/api/auth/admin/login',
      '/api/auth/admin/login/init',
      '/api/auth/admin/login/complete'
    ],
    retryOnFailure: true,
    maxRetries: 1
  };

  private currentToken: string | null = null;
  private tokenPromise: Promise<string> | null = null;
  private interceptorsSetup: boolean = false;

  /**
   * Initialize CSRF manager with custom config
   */
  init(config?: Partial<CSRFConfig>): void {
    this.config = { ...this.config, ...config };
    this.loadTokenFromStorage();
  }

  /**
   * Load CSRF token from storage
   */
  private loadTokenFromStorage(): void {
    if (!isBrowser()) {
      return;
    }

    try {
      // Try to get from cookie first
      const cookieToken = this.getCookieValue(this.config.cookieName);
      if (cookieToken) {
        this.currentToken = cookieToken;
        return;
      }

      // Fallback to localStorage using safe accessor
      const storageToken = safeLocalStorage.getItem('csrfToken');
      if (storageToken) {
        this.currentToken = storageToken;
      }
    } catch (error) {
      console.warn('Failed to load CSRF token from storage:', error);
    }
  }

  /**
   * Get cookie value by name
   */
  private getCookieValue(name: string): string | null {
    if (!isBrowser() || typeof document === 'undefined') {
      return null;
    }

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }

  /**
   * Store CSRF token (public method for external access)
   */
  setToken(token: string): void {
    this.storeToken(token);
  }

  /**
   * Store CSRF token
   */
  private storeToken(token: string): void {
    this.currentToken = token;

    // Store in localStorage as backup using safe accessor
    safeLocalStorage.setItem('csrfToken', token);
  }

  /**
   * Fetch CSRF token from server
   */
  async fetchToken(): Promise<string> {
    // Return existing promise if already fetching
    if (this.tokenPromise) {
      return this.tokenPromise;
    }

    this.tokenPromise = this.doFetchToken();
    
    try {
      const token = await this.tokenPromise;
      return token;
    } finally {
      this.tokenPromise = null;
    }
  }

  /**
   * Actually fetch the token from server
   */
  private async doFetchToken(): Promise<string> {
    try {
      // Use absolute URL for CSRF token endpoint
      const tokenUrl = this.config.tokenEndpoint;
      console.log('Fetching CSRF token from:', tokenUrl);
      
      const response = await axios.get(tokenUrl, {
        withCredentials: true,
        // Don't include CSRF header for the token fetch request
        headers: {
          'X-Skip-CSRF': 'true'
        }
      });

      if (response.data?.csrfToken) {
        this.storeToken(response.data.csrfToken);
        return response.data.csrfToken;
      }

      throw new Error('No CSRF token in response');
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error);
      throw error;
    }
  }

  /**
   * Get current CSRF token
   */
  getToken(): string | null {
    return this.currentToken;
  }

  /**
   * Clear CSRF token
   */
  clearToken(): void {
    this.currentToken = null;
    
    // Clear from localStorage using safe accessor
    safeLocalStorage.removeItem('csrfToken');
  }

  /**
   * Check if path should be excluded from CSRF protection
   */
  private shouldExcludePath(path: string): boolean {
    const shouldExclude = this.config.excludePaths.some(excludePath => 
      path.includes(excludePath) || path.startsWith(excludePath)
    );
    
    console.log('CSRF shouldExcludePath check:', {
      path,
      excludePaths: this.config.excludePaths,
      shouldExclude
    });
    
    return shouldExclude;
  }

  /**
   * Check if request needs CSRF protection
   */
  private needsCSRFProtection(config: InternalAxiosRequestConfig): boolean {
    const method = config.method?.toLowerCase();
    if (!method || ['get', 'head', 'options'].includes(method)) {
      return false;
    }

    const url = config.url || '';
    if (this.shouldExcludePath(url)) {
      return false;
    }

    // Skip if already has skip header
    if (config.headers?.['X-Skip-CSRF']) {
      return false;
    }

    return true;
  }

  /**
   * Setup axios interceptors for CSRF protection
   */
  setupInterceptors(axiosInstance: AxiosInstance): void {
    if (this.interceptorsSetup) {
      return;
    }

    // Request interceptor to add CSRF token
    axiosInstance.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        if (!this.needsCSRFProtection(config)) {
          return config;
        }

        let token = this.getToken();
        
        // Fetch token if we don't have one
        if (!token) {
          try {
            token = await this.fetchToken();
          } catch (error) {
            console.warn('Failed to fetch CSRF token for request:', error);
            // Continue without token rather than failing the request
            return config;
          }
        }

        if (token) {
          config.headers = config.headers || {};
          config.headers[this.config.headerName] = token;
        }

        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle CSRF token refresh
    axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        // Check for new CSRF token in response headers
        const newToken = response.headers[this.config.headerName.toLowerCase()];
        if (newToken && newToken !== this.currentToken) {
          this.storeToken(newToken);
        }

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as InternalAxiosRequestConfig & { _csrfRetry?: boolean };

        // Handle CSRF token mismatch/invalidity (usually 403)
        if (
          error.response?.status === 403 &&
          this.config.retryOnFailure &&
          !originalRequest._csrfRetry &&
          this.needsCSRFProtection(originalRequest)
        ) {
          originalRequest._csrfRetry = true;

          try {
            // Fetch new CSRF token
            const newToken = await this.fetchToken();
            
            // Retry original request with new token
            if (originalRequest.headers) {
              originalRequest.headers[this.config.headerName] = newToken;
            }

            return axiosInstance.request(originalRequest);
          } catch (fetchError) {
            console.error('Failed to refresh CSRF token and retry request:', fetchError);
            // Fall through to reject original error
          }
        }

        return Promise.reject(error);
      }
    );

    this.interceptorsSetup = true;
  }

  /**
   * Manually refresh CSRF token
   */
  async refreshToken(): Promise<string> {
    this.clearToken();
    return this.fetchToken();
  }

  /**
   * Validate current token with server
   */
  async validateToken(): Promise<boolean> {
    if (!this.currentToken) {
      return false;
    }

    try {
      const response = await axios.post('/api/csrf-validate', {}, {
        headers: {
          [this.config.headerName]: this.currentToken
        },
        withCredentials: true
      });

      return response.data?.valid === true;
    } catch (error) {
      console.warn('CSRF token validation failed:', error);
      return false;
    }
  }

  /**
   * Reset CSRF manager state
   */
  reset(): void {
    this.clearToken();
    this.tokenPromise = null;
    this.interceptorsSetup = false;
  }
}

// Export singleton instance
export const csrfManager = new CSRFManager();

// Convenience functions for backward compatibility
export const getCsrfToken = (): string | null => csrfManager.getToken();
export const setCsrfToken = (token: string): void => csrfManager.setToken(token);
export const clearCsrfToken = (): void => csrfManager.clearToken();
export const refreshCsrfToken = (): Promise<string> => csrfManager.refreshToken();

export default csrfManager;
