import { Request, Response, Router } from 'express';
import User from '../models/User';
import bcrypt from 'bcrypt';
import argon2 from 'argon2';
import { auth, requireAdmin } from '../middleware/auth';
import validate from '../middleware/validate';
import Joi from 'joi';

// Validation schemas
const createUserSchema = Joi.object({
  username: Joi.string().required(),
  expression: Joi.string().required(),
  displayName: Joi.string().required(),
  deviceFingerprint: Joi.string().required(),
  bleUUID: Joi.string().required(),
  isSuperuser: Joi.boolean().optional()
});

const updateUserSchema = Joi.object({
  displayName: Joi.string().optional(),
  status: Joi.string().valid('active', 'inactive', 'locked').optional(),
  isSuperuser: Joi.boolean().optional()
});

const resetDeviceSchema = Joi.object({
  deviceFingerprint: Joi.string().required(),
  bleUUID: Joi.string().required()
});

// GET /api/users - List all users
export async function listUsersController(req: Request, res: Response): Promise<void> {
  try {
    const users = await User.find({}, '-expressionHash -deviceFingerprintHash -bleUUIDHash'); // Never expose hashes
    res.status(200).json(users);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch users' });
  }
}

// POST /api/users - Create new user
export async function createUserController(req: Request, res: Response): Promise<void> {
  try {
    const { username, expression, displayName, deviceFingerprint, bleUUID, isSuperuser } = req.body;
    if (!username || !expression || !displayName || !deviceFingerprint || !bleUUID) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }
    // Normalize expression by removing spaces before hashing
    const normalizedExpression = expression.replace(/\s+/g, '');
    console.log(`👤 [CREATE USER] Original expression: "${expression}"`);
    console.log(`👤 [CREATE USER] Normalized expression: "${normalizedExpression}"`);
    // Hash expression, deviceFingerprint, and bleUUID
    const expressionHash = await bcrypt.hash(normalizedExpression, 12);
    const deviceFingerprintHash = await bcrypt.hash(deviceFingerprint, 12);
    const bleUUIDHash = await bcrypt.hash(bleUUID, 12);
    // If creating a superuser, ensure all other users are not superuser
    if (isSuperuser === true) {
      await User.updateMany({}, { $set: { isSuperuser: false } });
    }
    const user = new User({
      username,
      expressionHash,
      profile: { displayName },
      deviceFingerprintHash,
      bleUUIDHash,
      isSuperuser: !!isSuperuser,
      status: 'active',
    });
    await user.save();
    res.status(201).json({ id: user._id, username: user.username });
  } catch (err: any) {
    if (err.code === 11000) {
      res.status(409).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
}

// GET /api/users/:id - Fetch specific user details
export async function getUserController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id, '-expressionHash -deviceFingerprintHash -bleUUIDHash');
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    res.status(200).json(user);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch user' });
  }
}

// PUT /api/users/:id - Update user details
export async function updateUserController(req: Request, res: Response): Promise<void> {
  try {
    const { displayName, status, isSuperuser } = req.body;
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    if (displayName) user.profile.displayName = displayName;
    if (status) user.status = status;
    // If setting isSuperuser true, ensure all other users are not superuser
    if (typeof isSuperuser === 'boolean' && isSuperuser === true) {
      await User.updateMany({ _id: { $ne: user._id } }, { $set: { isSuperuser: false } });
      user.isSuperuser = true;
    } else if (typeof isSuperuser === 'boolean') {
      user.isSuperuser = isSuperuser;
    }
    await user.save();
    res.status(200).json({ message: 'User updated' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to update user' });
  }
}

// DELETE /api/users/:id - Deactivate or delete user
export async function deleteUserController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    user.status = 'inactive';
    await user.save();
    res.status(200).json({ message: 'User deactivated' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to deactivate user' });
  }
}

// POST /api/users/:id/reset - Reset user device fingerprint/BLE configuration
export async function resetUserDeviceController(req: Request, res: Response): Promise<void> {
  try {
    const { deviceFingerprint, bleUUID } = req.body;
    if (!deviceFingerprint || !bleUUID) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }
    user.deviceFingerprintHash = await bcrypt.hash(deviceFingerprint, 12);
    user.bleUUIDHash = await bcrypt.hash(bleUUID, 12);
    await user.save();
    res.status(200).json({ message: 'Device/BLE reset' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to reset device/BLE' });
  }
}

// GET /api/users/profile - Get current user profile (mobile app)
export async function getUserProfile(req: Request, res: Response): Promise<void> {
  try {
    const userId = (req as any).user?.id;
    if (!userId) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    const user = await User.findById(userId, '-expressionHash -deviceFingerprintHash -bleUUIDHash');
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    res.status(200).json({
      success: true,
      user: {
        _id: user._id,
        username: user.username,
        profile: user.profile,
        isSuperuser: user.isSuperuser || false,
        status: user.status,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
      }
    });
  } catch (error) {
    console.error('Error getting user profile:', error);
    res.status(500).json({ error: 'Failed to get user profile' });
  }
}

// GET /api/users/active-list - Get list of active users (superuser only, mobile app)
export async function getActiveUsersList(req: Request, res: Response): Promise<void> {
  try {
    const currentUserId = (req as any).user?.id;
    const currentUser = await User.findById(currentUserId);
    
    if (!currentUser?.isSuperuser) {
      res.status(403).json({ error: 'Superuser access required' });
      return;
    }

    // Get all active users except the current superuser
    const activeUsers = await User.find({
      _id: { $ne: currentUserId },
      status: 'active',
      isSuperuser: { $ne: true }
    }, 'username profile lastLoginAt status createdAt');

    const usersWithOnlineStatus = activeUsers.map(user => {
      const lastActive = user.lastLoginAt ? new Date(user.lastLoginAt) : null;
      const isOnline = lastActive ? (Date.now() - lastActive.getTime()) < 30 * 60 * 1000 : false; // 30 minutes

      return {
        _id: user._id,
        username: user.username,
        profile: user.profile,
        status: user.status,
        lastLoginAt: user.lastLoginAt,
        isOnline,
        createdAt: user.createdAt,
      };
    });

    res.status(200).json({
      success: true,
      users: usersWithOnlineStatus
    });
  } catch (error) {
    console.error('Error getting active users list:', error);
    res.status(500).json({ error: 'Failed to get active users list' });
  }
}

// Admin-only device and session management endpoints

// POST /api/admin/users/:id/reset-devices - Reset all user devices
export async function resetUserDevicesController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Clear all device registrations
    user.devices = [];
    user.deviceFingerprintHash = undefined;
    user.status = 'pending_device_registration';
    
    // Ensure mathExpression type is set if it exists
    if (user.mathExpression && !user.mathExpression.type) {
      user.mathExpression.type = 'arithmetic'; // default type
    }
    
    await user.save({ validateModifiedOnly: true });

    // Log security event
    const securityEvent = {
      eventType: 'admin_device_reset' as const,
      timestamp: new Date(),
      details: { 
        adminId: (req as any).user?.id,
        targetUserId: user._id 
      },
      severity: 'medium' as const,
      resolved: false
    };
    
    if (!user.securityEvents) user.securityEvents = [];
    user.securityEvents.push(securityEvent);
    await user.save({ validateModifiedOnly: true });

    res.status(200).json({ message: 'User devices reset successfully' });
  } catch (err) {
    console.error('Reset user devices error:', err);
    res.status(500).json({ error: 'Failed to reset user devices' });
  }
}

// POST /api/admin/users/:id/reset-ble - Reset all user BLE devices
export async function resetUserBLEController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Clear all BLE device registrations
    if (user.devices) {
      user.devices.forEach(device => {
        device.bleDevices = [];
      });
    }
    user.bleUUIDHash = undefined;
    
    // Ensure mathExpression type is set if it exists
    if (user.mathExpression && !user.mathExpression.type) {
      user.mathExpression.type = 'arithmetic'; // default type
    }
    
    await user.save({ validateModifiedOnly: true });

    // Log security event
    const securityEvent = {
      eventType: 'admin_ble_reset' as const,
      timestamp: new Date(),
      details: { 
        adminId: (req as any).user?.id,
        targetUserId: user._id 
      },
      severity: 'medium' as const,
      resolved: false
    };
    
    if (!user.securityEvents) user.securityEvents = [];
    user.securityEvents.push(securityEvent);
    await user.save({ validateModifiedOnly: true });

    res.status(200).json({ message: 'User BLE devices reset successfully' });
  } catch (err) {
    console.error('Reset user BLE devices error:', err);
    res.status(500).json({ error: 'Failed to reset user BLE devices' });
  }
}

// POST /api/admin/users/:id/reset-sessions - Reset all user sessions
export async function resetUserSessionsController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Force logout by updating auth tokens/sessions
    user.authTokens = [];
    user.activeSessions = [];
    
    // Ensure mathExpression type is set if it exists
    if (user.mathExpression && !user.mathExpression.type) {
      user.mathExpression.type = 'arithmetic'; // default type
    }
    
    await user.save({ validateModifiedOnly: true });

    // Log security event
    const securityEvent = {
      eventType: 'admin_session_reset' as const,
      timestamp: new Date(),
      details: { 
        adminId: (req as any).user?.id,
        targetUserId: user._id 
      },
      severity: 'high' as const,
      resolved: false
    };
    
    if (!user.securityEvents) user.securityEvents = [];
    user.securityEvents.push(securityEvent);
    await user.save();

    res.status(200).json({ message: 'User sessions reset successfully' });
  } catch (err) {
    console.error('Reset user sessions error:', err);
    res.status(500).json({ error: 'Failed to reset user sessions' });
  }
}

// POST /api/admin/users/:id/unlock - Unlock user and reset failed login attempts
export async function unlockUserController(req: Request, res: Response): Promise<void> {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    // Reset failed login attempts and unlock
    user.failedLoginAttempts = 0;
    user.lockUntil = undefined;
    user.status = 'active';
    
    // Ensure mathExpression type is set if it exists
    if (user.mathExpression && !user.mathExpression.type) {
      user.mathExpression.type = 'arithmetic'; // default type
    }
    
    await user.save({ validateModifiedOnly: true });

    // Log security event
    const securityEvent = {
      eventType: 'admin_user_unlock' as const,
      timestamp: new Date(),
      details: { 
        adminId: (req as any).user?.id,
        targetUserId: user._id 
      },
      severity: 'medium' as const,
      resolved: false
    };
    
    if (!user.securityEvents) user.securityEvents = [];
    user.securityEvents.push(securityEvent);
    await user.save({ validateModifiedOnly: true });

    res.status(200).json({ message: 'User unlocked successfully' });
  } catch (err) {
    console.error('Unlock user error:', err);
    res.status(500).json({ error: 'Failed to unlock user' });
  }
}

// POST /api/admin/users/:id/reset-expression - Reset user mathematical expression
export async function resetUserExpressionController(req: Request, res: Response): Promise<void> {
  try {
    console.log('🔄 Reset Expression Request:', {
      userId: req.params.id,
      body: req.body,
      contentType: req.get('Content-Type'),
      hasBody: !!req.body,
      bodyKeys: req.body ? Object.keys(req.body) : 'no body'
    });

    const { expression } = req.body;
    if (!expression) {
      console.log('❌ Reset Expression Error: No expression provided', {
        reqBody: req.body,
        expression
      });
      res.status(400).json({ error: 'Expression is required' });
      return;
    }

    const user = await User.findById(req.params.id);
    if (!user) {
      console.log('❌ Reset Expression Error: User not found', {
        userId: req.params.id
      });
      res.status(404).json({ error: 'User not found' });
      return;
    }

    console.log('📊 Resetting expression for user:', {
      userId: user._id,
      username: user.username,
      oldExpression: user.mathExpression?.expression,
      newExpression: expression
    });

    // Normalize expression by removing spaces before hashing
    const normalizedExpression = expression.replace(/\s+/g, '');
    console.log(`🔄 [RESET EXPRESSION] Original expression: "${expression}"`);
    console.log(`🔄 [RESET EXPRESSION] Normalized expression: "${normalizedExpression}"`);
    
    // Hash the new expression
    const expressionHash = await bcrypt.hash(normalizedExpression, 12);
    user.expressionHash = expressionHash;
    user.mathExpression = {
      expression: expression, // Store for display purposes (admin only)
      type: 'admin_reset',
      updatedAt: new Date()
    };
    
    await user.save({ validateModifiedOnly: true });

    // Log security event
    const securityEvent = {
      eventType: 'admin_expression_reset' as const,
      timestamp: new Date(),
      details: { 
        adminId: (req as any).user?.id,
        targetUserId: user._id 
      },
      severity: 'high' as const,
      resolved: false
    };
    
    if (!user.securityEvents) user.securityEvents = [];
    user.securityEvents.push(securityEvent);
    await user.save({ validateModifiedOnly: true });

    console.log('✅ Expression reset successfully:', {
      userId: user._id,
      username: user.username,
      newExpressionLength: expression.length,
      adminId: (req as any).user?.id
    });

    res.status(200).json({ 
      message: 'User expression reset successfully',
      user: {
        id: user._id,
        username: user.username,
        mathExpression: user.mathExpression
      }
    });
  } catch (err) {
    console.error('❌ Reset user expression error:', err);
    res.status(500).json({ error: 'Failed to reset user expression' });
  }
}

// Create and configure router
const router = Router();

// Define routes with middleware and validation
router.get('/', auth, requireAdmin, listUsersController);
router.post('/', auth, requireAdmin, validate(createUserSchema), createUserController);
router.get('/:id', auth, requireAdmin, getUserController);
router.put('/:id', auth, requireAdmin, validate(updateUserSchema), updateUserController);
router.delete('/:id', auth, requireAdmin, deleteUserController);
router.post('/:id/reset', auth, requireAdmin, validate(resetDeviceSchema), resetUserDeviceController);

// Mobile app routes
router.get('/profile', auth, getUserProfile);
router.get('/active-list', auth, getActiveUsersList);

// Admin-only routes
router.post('/admin/users/:id/reset-devices', auth, requireAdmin, resetUserDevicesController);
router.post('/admin/users/:id/reset-ble', auth, requireAdmin, resetUserBLEController);
router.post('/admin/users/:id/reset-sessions', auth, requireAdmin, resetUserSessionsController);
router.post('/admin/users/:id/unlock', auth, requireAdmin, unlockUserController);
router.post('/admin/users/:id/reset-expression', auth, requireAdmin, resetUserExpressionController);

export default router;
