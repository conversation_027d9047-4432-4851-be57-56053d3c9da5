import { NextApiRequest, NextApiResponse } from 'next';
import { rateLimit } from '../../../../../utils/rateLimit';
import ServerApiClient from '../../../../../utils/serverApiClient';

const limiter = rateLimit({
  interval: 60 * 1000, // 60 seconds
  uniqueTokenPerInterval: 500, // Max 500 users per second
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await limiter.check(res, 10, 'ADMIN_LOGIN_COMPLETE_RATE_LIMIT');

    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { username, password, ppkSignature, challenge, deviceFingerprint } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    console.log('Admin login complete - Starting authentication process');
    const serverApiClient = new ServerApiClient();

    try {
      // Authenticate with backend
      const response = await serverApiClient.post('/api/auth/admin/login/complete', {
        username,
        password,
        ppkSignature,
        challenge,
        deviceFingerprint: deviceFingerprint || 'web'
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Backend authentication failed:', responseData);
        return res.status(response.status).json(responseData);
      }

      console.log('Admin login complete - Authentication successful');

      // Extract data from response
      const { token, admin } = responseData;
      
      if (!token) {
        console.error('No token received from backend');
        return res.status(500).json({ error: 'No authentication token received' });
      }

      console.log('Login successful, returning token for client-side storage');

      return res.status(200).json({
        success: true,
        admin,
        token,
        tokenType: 'Bearer'
      });

    } catch (error) {
      console.error('Backend request failed:', error);
      return res.status(500).json({ error: 'Authentication server error' });
    }
  } catch (error) {
    console.error('Login complete handler error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}