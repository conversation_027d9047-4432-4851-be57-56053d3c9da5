import { useRouter } from 'next/router';
import Head from 'next/head';

export default function Custom404() {
  const router = useRouter();

  return (
    <div className="error-container">
      <Head>
        <title>404 - Page Not Found | CCALC Admin</title>
      </Head>

      <div className="error-content">
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>The page you are looking for does not exist or has been moved.</p>
        <div className="actions">
          <button onClick={() => router.back()} className="secondary-button">
            Go Back
          </button>
          <button onClick={() => router.push('/dashboard')} className="primary-button">
            Go to Dashboard
          </button>
        </div>
      </div>

      <style jsx>{`
        .error-container {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          padding: 20px;
        }
        .error-content {
          text-align: center;
          max-width: 500px;
        }
        h1 {
          font-size: 120px;
          margin: 0;
          color: #0070f3;
          line-height: 1;
        }
        h2 {
          margin: 0 0 20px;
          font-size: 28px;
        }
        p {
          margin-bottom: 30px;
          color: #666;
          font-size: 18px;
        }
        .actions {
          display: flex;
          justify-content: center;
          gap: 10px;
        }
        .secondary-button, .primary-button {
          padding: 10px 20px;
          font-size: 16px;
        }
      `}</style>
    </div>
  );
}
