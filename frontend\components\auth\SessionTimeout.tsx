/**
 * Session Timeout Warning Component
 * Shows warnings and handles session expiration
 */

import React, { useState, useEffect } from 'react';
import { sessionManager, SessionStatus } from '../../utils/sessionManager';

interface SessionTimeoutProps {
  className?: string;
  onSessionExpired?: () => void;
}

interface TimeRemaining {
  minutes: number;
  seconds: number;
}

const SessionTimeout: React.FC<SessionTimeoutProps> = ({ 
  className = '', 
  onSessionExpired 
}) => {
  const [sessionStatus, setSessionStatus] = useState<SessionStatus | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  const [isExtending, setIsExtending] = useState(false);

  // Format time remaining
  const formatTimeRemaining = (ms: number): TimeRemaining => {
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return { minutes, seconds };
  };

  // Extend session
  const handleExtendSession = async () => {
    setIsExtending(true);
    try {
      const success = await sessionManager.refreshSession();
      if (success) {
        setShowWarning(false);
      } else {
        // If refresh fails, show error and logout
        console.error('Failed to extend session');
        sessionManager.expireSession('error');
      }
    } catch (error) {
      console.error('Session extension error:', error);
      sessionManager.expireSession('error');
    } finally {
      setIsExtending(false);
    }
  };

  // Logout immediately
  const handleLogoutNow = () => {
    sessionManager.logout();
  };

  useEffect(() => {
    // Listen to session status changes
    const unsubscribe = sessionManager.addListener((status: SessionStatus) => {
      setSessionStatus(status);
      
      // Show warning when session is close to expiring
      if (status.isWarning && status.isActive) {
        setShowWarning(true);
      }
      
      // Handle session expiration
      if (!status.isActive) {
        setShowWarning(false);
        if (onSessionExpired) {
          onSessionExpired();
        }
      }
    });

    // Initialize session monitoring if authenticated
    if (sessionManager.getSessionStatus().isActive) {
      sessionManager.initialize();
    }

    return () => {
      unsubscribe();
    };
  }, [onSessionExpired]);

  // Update activity on user interaction
  useEffect(() => {
    const handleUserActivity = () => {
      if (sessionStatus?.isActive) {
        sessionManager.updateActivity();
      }
    };

    // Listen for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
    };
  }, [sessionStatus]);

  if (!showWarning || !sessionStatus) {
    return null;
  }

  const timeRemaining = formatTimeRemaining(sessionStatus.timeRemaining);

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md shadow-lg max-w-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg 
              className="h-5 w-5 text-yellow-400" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path 
                fillRule="evenodd" 
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" 
                clipRule="evenodd" 
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Session Expiring Soon
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Your session will expire in{' '}
                <span className="font-semibold">
                  {timeRemaining.minutes}:{timeRemaining.seconds.toString().padStart(2, '0')}
                </span>
              </p>
              <p className="mt-1">
                Would you like to extend your session?
              </p>
            </div>
            <div className="mt-4 flex space-x-3">
              <button
                type="button"
                onClick={handleExtendSession}
                disabled={isExtending}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isExtending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-700" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Extending...
                  </>
                ) : (
                  'Extend Session'
                )}
              </button>
              <button
                type="button"
                onClick={handleLogoutNow}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Logout Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionTimeout;
