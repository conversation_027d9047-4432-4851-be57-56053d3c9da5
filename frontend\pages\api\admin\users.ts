// Next.js API route to proxy admin user requests to backend
import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3000';
  const url = `${backendUrl}/api/admin/users${req.url ? req.url.replace(/^\/api\/admin\/users/, '') : ''}`;

  try {
    const response = await axios({
      method: req.method,
      url,
      headers: {
        ...req.headers,
        host: undefined, // Remove host header for backend
      },
      data: req.body,
      params: req.query,
      validateStatus: () => true,
    });
    res.status(response.status).json(response.data);
  } catch (error: any) {
    res.status(500).json({ error: 'Proxy error', details: error.message });
  }
}
