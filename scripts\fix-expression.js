const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string
const mongoUri = '*************************************************************';

// User schema
const UserSchema = new mongoose.Schema({
  username: String,
  expressionHash: String,
  unlockExpression: String,
}, { timestamps: true });

const User = mongoose.model('User', UserSchema);

async function fixExpression() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');

    // The expression the mobile app is actually sending
    const mobileAppExpression = '12 + 5 * 7 - 3 * 2 + 8 / 4';
    
    // Hash it with bcrypt
    const newExpressionHash = await bcrypt.hash(mobileAppExpression, 10);

    // Update the user
    const result = await User.updateOne(
      { username: 'mobileuser' },
      { 
        $set: { 
          unlockExpression: mobileAppExpression,
          expressionHash: newExpressionHash
        }
      }
    );

    console.log('✅ Updated expression to match mobile app format');
    console.log(`New expression: "${mobileAppExpression}"`);
    console.log('Mobile app should now authenticate successfully!');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDatabase connection closed');
  }
}

fixExpression();
