import { Request, Response, NextFunction } from 'express';
import AuditLogModel from '../models/AuditLog';

export interface APIError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
  isOperational?: boolean;
}

/**
 * Enhanced error handler for API endpoints with security logging
 */
export const apiErrorHandler = async (
  error: APIError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {    // Log security-related errors
    if (error.statusCode === 401 || error.statusCode === 403 || error.code === 'SECURITY_VIOLATION') {
      await AuditLogModel.create({
        logId: `api_security_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: req.user?.id || req.admin?.id,
        event: {
          type: 'security_violation',
          action: 'api_security_error',
          result: 'blocked',
          severity: error.statusCode === 401 ? 'medium' : 'high'
        },
        context: {
          ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.path,
          method: req.method,
          statusCode: error.statusCode
        },
        compliance: {
          category: 'security',
          retention: 'long',
          piiIncluded: false,
          sensitiveData: true,
          exportable: false
        },
        data: {
          metadata: {
            error: error.message,
            statusCode: error.statusCode,
            deviceId: req.body?.deviceId || req.query?.deviceId
          }
        }
      });
    }

    // Log critical system errors
    if ((error as any).statusCode && (error as any).statusCode >= 500 || !(error as any).isOperational) {
      console.error('API Error:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        body: req.body,
        user: req.user?.id,
        timestamp: new Date().toISOString()
      });      await AuditLogModel.create({
        logId: `api_system_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: req.user?.id || req.admin?.id,
        event: {
          type: 'system_action',
          action: 'api_system_error',
          result: 'failure',
          severity: 'critical'
        },
        context: {
          ipAddress: req.ip || req.socket?.remoteAddress || '127.0.0.1',
          userAgent: req.get('User-Agent') || 'unknown',
          endpoint: req.path,
          method: req.method,
          statusCode: error.statusCode
        },
        compliance: {
          category: 'system',
          retention: 'long',
          piiIncluded: false,
          sensitiveData: false,
          exportable: true
        },
        data: {
          metadata: {
            error: error.message,
            stack: error.stack?.split('\n').slice(0, 5), // First 5 stack lines
            statusCode: error.statusCode
          }
        }
      });
    }

    // Determine response based on error type
    let statusCode = error.statusCode || 500;
    let message = error.message || 'Internal server error';
    let code = error.code || 'INTERNAL_ERROR';

    // Handle specific error types
    if (error.name === 'ValidationError') {
      statusCode = 400;
      code = 'VALIDATION_ERROR';
      message = 'Invalid input data';
    } else if (error.name === 'CastError') {
      statusCode = 400;
      code = 'INVALID_ID';
      message = 'Invalid ID format';
    } else if ((error as any).code && (error as any).code === 11000) { // MongoDB duplicate key
      statusCode = 409;
      code = 'DUPLICATE_ENTRY';
      message = 'Resource already exists';
    } else if (error.name === 'JsonWebTokenError') {
      statusCode = 401;
      code = 'INVALID_TOKEN';
      message = 'Invalid authentication token';
    } else if (error.name === 'TokenExpiredError') {
      statusCode = 401;
      code = 'TOKEN_EXPIRED';
      message = 'Authentication token has expired';
    }

    // Send error response
    res.status(statusCode).json({
      success: false,
      error: message,
      code,
      timestamp: new Date().toISOString(),
      ...(process.env.NODE_ENV === 'development' && {
        details: error.details,
        stack: error.stack
      })
    });

  } catch (loggingError) {
    // If error logging fails, still send response
    console.error('Error logging failed:', loggingError);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Async wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Rate limiting error handler
 */
export const rateLimitErrorHandler = (req: Request, res: Response) => {
  res.status(429).json({
    success: false,
    error: 'Too many requests, please try again later',
    code: 'RATE_LIMIT_EXCEEDED',
    timestamp: new Date().toISOString(),
    retryAfter: 900 // 15 minutes
  });
};
