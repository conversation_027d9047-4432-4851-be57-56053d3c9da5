#!/usr/bin/env node

// Workaround for TypeScript module loading issues with Node.js 22
// This script bypasses the direct TypeScript loading by using require instead

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Set NODE_OPTIONS to handle ES modules better
process.env.NODE_OPTIONS = '--experimental-vm-modules --no-warnings';

// Try to start expo with legacy require resolution
try {
  const expoPath = path.resolve(__dirname, 'node_modules', '.bin', 'expo');
  
  console.log('Starting Expo with custom configuration...');
  
  const expo = spawn('node', [expoPath, 'start', '--clear', '--port', '8082'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      EXPO_LEGACY_IMPORTS: '1',
      EXPO_USE_CUSTOM_TS_TRANSPILER: '1'
    }
  });
  
  expo.on('close', (code) => {
    process.exit(code);
  });
  
  expo.on('error', (err) => {
    console.error('Failed to start Expo:', err);
    process.exit(1);
  });
  
} catch (error) {
  console.error('Error starting Expo:', error);
  process.exit(1);
}
