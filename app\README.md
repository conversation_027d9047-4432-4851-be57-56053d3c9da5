# CCALC Mobile App

A secure mobile application for iOS and Android with end-to-end encrypted messaging, media sharing, and device authentication.

## 🚀 Quick Start

### Development Setup

```bash
# Install dependencies
npm install

# Start Expo development server
npm start

# Run on iOS simulator (requires Mac)
npm run ios

# Run on Android emulator
npm run android
```

### Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.template .env
   ```

2. Update `.env` with your configuration:
   ```bash
   EXPO_PUBLIC_BACKEND_URL=https://your-backend.ngrok.io
   EXPO_PUBLIC_FRONTEND_URL=https://your-frontend.ngrok.io
   ```

3. Or use the setup script:
   ```bash
   # Windows
   setup-build.bat
   
   # macOS/Linux
   ./setup-build.sh
   ```

4. Verify your setup:
   ```bash
   npm run verify-setup
   ```

## 🏗️ Building for Production

See [BUILD_GUIDE.md](./BUILD_GUIDE.md) for detailed build instructions including:
- Local builds with EAS
- GitHub Actions automated builds
- Environment configuration
- Troubleshooting guide

### Quick Build Commands

```bash
# Build for iOS (production)
npm run build:ios

# Build for Android (production)
npm run build:android

# Build preview versions
npm run build:preview
```

## 🔧 Project Structure

```
src/
├── components/          # Reusable UI components
├── screens/            # App screens/pages
├── services/           # API and business logic
├── styles/            # Style definitions
└── utils/             # Helper utilities
```

## 🛠️ Key Features

- **Secure Messaging**: End-to-end encrypted chat
- **Media Sharing**: WhatsApp-like media handling
- **Device Authentication**: Secure device pairing
- **Cross-Platform**: iOS and Android support
- **Offline Support**: Local media caching

## 📱 Platform Support

- **iOS**: 13.4+
- **Android**: API 21+ (Android 5.0)
- **Development**: Expo Go, EAS Development Builds

## 🔐 Security Features

- End-to-end message encryption
- Secure media storage
- Device fingerprinting
- Token-based authentication

## 📚 Documentation

- [Build Guide](./BUILD_GUIDE.md) - Complete build setup
- [Security Summary](../CCALC_FINAL_SECURITY_SUMMARY.md) - Security features

## 🐛 Troubleshooting

### Common Issues

1. **Images disappearing**: Test on standalone build, not Expo Go
2. **Network errors**: Check ngrok URL accessibility
3. **Build failures**: Verify environment variables and credentials

### Debug Mode

Enable debug logging in `.env`:
```bash
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_NETWORK=true
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary and confidential.
