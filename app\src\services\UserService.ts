/**
 * User Service
 * Manages user information, superuser status, and active user listing
 */

import { AuthService } from './AuthService';

export interface User {
  id: string;
  username: string;
  displayName: string;
  isSuperuser: boolean;
  status: 'active' | 'inactive' | 'locked' | 'pending_device_registration';
  lastActive?: Date;
  isOnline?: boolean;
}

export interface UserListResult {
  success: boolean;
  users?: User[];
  error?: string;
}

export interface CurrentUserResult {
  success: boolean;
  user?: User;
  error?: string;
}

export class UserService {
  private static instance: UserService;
  private authService: AuthService;
  private backendUrl: string;
  private currentUser: User | null = null;

  private constructor() {
    this.authService = AuthService.getInstance();
    this.backendUrl = this.authService.getConfig().backendUrl;
  }

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  /**
   * Get current authenticated user information
   */
  public async getCurrentUser(): Promise<CurrentUserResult> {
    try {
      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      const response = await fetch(`${this.backendUrl}/api/users/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform backend response to our User interface
      this.currentUser = {
        id: data.user._id,
        username: data.user.username,
        displayName: data.user.profile?.displayName || data.user.username,
        isSuperuser: data.user.isSuperuser || false,
        status: data.user.status,
        lastActive: data.user.lastLoginAt ? new Date(data.user.lastLoginAt) : undefined,
        isOnline: true, // Current user is always online
      };

      return {
        success: true,
        user: this.currentUser,
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get list of active users (superuser only)
   * Returns all active users with display names
   */
  public async getActiveUsers(): Promise<UserListResult> {
    try {
      const token = this.authService.getToken();
      if (!token) {
        return {
          success: false,
          error: 'Authentication required',
        };
      }

      // First check if current user is superuser
      if (!this.currentUser?.isSuperuser) {
        const userResult = await this.getCurrentUser();
        if (!userResult.success || !userResult.user?.isSuperuser) {
          return {
            success: false,
            error: 'Superuser access required',
          };
        }
      }

      const response = await fetch(`${this.backendUrl}/api/users/active-list`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform backend response to our User interface
      const users: User[] = data.users.map((user: any) => ({
        id: user._id,
        username: user.username,
        displayName: user.profile?.displayName || user.username,
        isSuperuser: user.isSuperuser || false,
        status: user.status,
        lastActive: user.lastLoginAt ? new Date(user.lastLoginAt) : undefined,
        isOnline: this.isUserOnline(user.lastLoginAt),
      }));

      // Filter out current user and only show active non-superuser accounts
      const activeUsers = users.filter(user => 
        user.id !== this.currentUser?.id && 
        user.status === 'active' && 
        !user.isSuperuser
      );

      return {
        success: true,
        users: activeUsers,
      };
    } catch (error) {
      console.error('Error getting active users:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check if user is considered online based on last activity
   */
  private isUserOnline(lastLoginAt?: string): boolean {
    if (!lastLoginAt) return false;
    
    const lastActivity = new Date(lastLoginAt);
    const now = new Date();
    const timeDiff = now.getTime() - lastActivity.getTime();
    
    // Consider user online if active within last 30 minutes
    return timeDiff < 30 * 60 * 1000;
  }

  /**
   * Get cached current user
   */
  public getCachedCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Clear cached user data (for logout)
   */
  public clearUserCache(): void {
    this.currentUser = null;
  }

  /**
   * Check if current user is superuser
   */
  public async isSuperuser(): Promise<boolean> {
    if (this.currentUser?.isSuperuser) {
      return true;
    }

    const userResult = await this.getCurrentUser();
    return userResult.success && userResult.user?.isSuperuser === true;
  }
}

export default UserService;
