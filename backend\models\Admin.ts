import mongoose, { Document, Schema } from 'mongoose';
import { verifySignature } from '../utils/ppk';

// Admin interface
export interface IAdmin extends Document {
  username: string;
  password: string; // bcrypt or Argon2 hash
  email: string;
  role: string;
  isActive: boolean;
  lastLogin?: Date;
  ppkPublicKey?: string; // PPK public key for verification
  ppkKeyVersion?: string; // Track key versions for rotation
  ppkLastRotated?: Date; // Track when the key was last rotated
  ppkEnabled: boolean; // Whether PPK auth is required for this admin
  authMethod: 'password' | 'ppk' | 'both'; // Authentication method requirement
  sessionTokens?: string[]; // Store only hashed tokens
  createdAt: Date;
  updatedAt: Date;
  auditLog?: mongoose.Types.ObjectId[]; // References to AuditLog documents
  verifyPPKSignature: (challenge: string, signature: string) => boolean;
}

const AdminSchema = new Schema<IAdmin>(
  {
    username: { type: String, required: true, unique: true, trim: true },
    password: { type: String, required: true },
    email: { type: String, required: true, unique: true, trim: true },
    role: { type: String, enum: ['admin', 'superadmin'], default: 'admin' },
    isActive: { type: Boolean, default: true },
    lastLogin: { type: Date, default: null },
    ppkPublicKey: { type: String },
    ppkKeyVersion: { type: String },
    ppkLastRotated: { type: Date },
    ppkEnabled: { type: Boolean, default: false },
    authMethod: { 
      type: String, 
      enum: ['password', 'ppk', 'both'],
      default: 'password'
    },
    sessionTokens: [{ type: String }],
    auditLog: [{ type: Schema.Types.ObjectId, ref: 'AuditLog' }],
  },
  { 
    timestamps: true,
    methods: {
      verifyPPKSignature(challenge: string, signature: string): boolean {
        if (!this.ppkPublicKey || !this.ppkEnabled) {
          return false;
        }
        try {
          return verifySignature(challenge, signature, this.ppkPublicKey);
        } catch (error) {
          console.error('PPK signature verification failed:', error);
          return false;
        }
      }
    }
  }
);

// Add index for key lookup
AdminSchema.index({ username: 1, ppkEnabled: 1 });

// Middleware to validate PPK configuration
AdminSchema.pre('save', function(next) {
  if (this.isModified('authMethod') || this.isModified('ppkEnabled')) {
    if (this.authMethod === 'ppk' && !this.ppkEnabled) {
      next(new Error('PPK authentication method requires ppkEnabled to be true'));
      return;
    }
    if (this.authMethod === 'ppk' && !this.ppkPublicKey) {
      next(new Error('PPK authentication method requires a public key'));
      return;
    }
  }
  next();
});

// Export both the interface and the model
export const AdminModel = mongoose.model<IAdmin>('Admin', AdminSchema);
export default AdminModel;
