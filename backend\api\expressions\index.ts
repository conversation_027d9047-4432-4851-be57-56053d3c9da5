import { Router, Request, Response } from 'express';
import { authenticateToken } from '../../middleware/auth';
import UserModel from '../../models/User';
import AuditLogModel from '../../models/AuditLog';
import mathValidator from '../../utils/mathValidator';
import mongoose from 'mongoose';
import crypto from 'crypto';

const router = Router();

/**
 * Get user's unlock expression
 * GET /api/expressions/user/:userId
 */
router.get('/user/:userId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    // Find user
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'USER_EXPRESSION_ACCESSED',
      adminId,
      userId,
      details: {
        username: user.username
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: {
        userId: user._id,
        username: user.username,
        expression: user.unlockExpression || null,
        expressionType: user.expressionType || 'calculator',
        isExpressionSet: !!user.unlockExpression,
        lastUpdated: user.expressionUpdatedAt || null
      }
    });

  } catch (error: any) {
    console.error('Error fetching user expression:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user expression',
      details: error.message
    });
  }
});

/**
 * Update user's unlock expression
 * PUT /api/expressions/user/:userId
 */
router.put('/user/:userId', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const adminId = req.admin?.id;
    const { userId } = req.params;
    const { expression, expressionType = 'calculator' } = req.body;

    // Validate userId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      res.status(400).json({
        success: false,
        error: 'Invalid userId format'
      });
      return;
    }

    // Validate input
    if (!expression || typeof expression !== 'string') {
      res.status(400).json({
        success: false,
        error: 'Expression is required and must be a string'
      });
      return;
    }

    // Validate expression format
    const validationResult = validateExpression(expression, expressionType);
    if (!validationResult.isValid) {
      res.status(400).json({
        success: false,
        error: 'Invalid expression format',
        details: validationResult.errors
      });
      return;
    }

    // Find user
    const user = await UserModel.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    const oldExpression = user.unlockExpression;

    // Update user expression
    user.unlockExpression = expression;
    user.expressionType = expressionType;
    user.expressionUpdatedAt = new Date();
    await user.save();

    // Log admin action
    await AuditLogModel.create({
      action: 'USER_EXPRESSION_UPDATED',
      adminId,
      userId,
      details: {
        username: user.username,
        oldExpression: oldExpression ? '[REDACTED]' : null,
        newExpression: '[REDACTED]',
        expressionType,
        securityScore: validationResult.securityScore
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: 'Expression updated successfully',
      data: {
        userId: user._id,
        username: user.username,
        expressionType,
        securityScore: validationResult.securityScore,
        updatedAt: user.expressionUpdatedAt
      }
    });

  } catch (error: any) {
    console.error('Error updating user expression:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user expression',
      details: error.message
    });
  }
});

/**
 * Validate mathematical expression
 * POST /api/expressions/validate
 */
router.post('/validate', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { expression } = req.body;

    if (!expression || typeof expression !== 'string') {
      res.status(400).json({
        success: false,
        error: 'Expression is required and must be a string'
      });
      return;
    }

    const validation = mathValidator.validateMathematicalExpression(expression);

    res.json({
      success: true,
      data: {
        isValid: validation.isValid,
        error: validation.error,
        expression: expression,
        result: validation.result
      }
    });

  } catch (error) {
    console.error('Expression validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate expression'
    });
  }
});

/**
 * Generate valid mathematical expression
 * POST /api/expressions/generate
 */
router.post('/generate', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { complexity = 3 } = req.body;

    // Validate complexity level
    const complexityLevel = Math.max(1, Math.min(5, parseInt(complexity.toString())));

    const generatedExpression = mathValidator.generateValidMathExpression(complexityLevel);
    
    // Validate the generated expression to get its result
    const validation = mathValidator.validateMathematicalExpression(generatedExpression);

    res.json({
      success: true,
      data: {
        expression: generatedExpression,
        result: validation.result || 0,
        complexity: complexityLevel,
        description: `Complexity level ${complexityLevel} mathematical expression`
      }
    });

  } catch (error) {
    console.error('Expression generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate expression'
    });
  }
});

/**
 * Get expression security analytics
 * GET /api/expressions/analytics
 */
router.get('/analytics', authenticateToken, async (req: Request, res: Response) => {
  try {
    const adminId = req.admin?.id;

    // Get all users with expressions
    const users = await UserModel.find({ 
      unlockExpression: { $exists: true, $ne: null } 
    }).select('username unlockExpression expressionType expressionUpdatedAt');

    // Analyze expressions
    const analytics = {
      totalUsers: users.length,
      expressionTypes: {} as any,
      securityDistribution: {
        weak: 0,
        medium: 0,
        strong: 0
      },
      averageSecurityScore: 0,
      weakExpressions: [] as any[],
      recommendations: [] as string[]
    };

    let totalSecurityScore = 0;

    users.forEach(user => {
      if (user.unlockExpression) {
        const validation = validateExpression(user.unlockExpression, user.expressionType || 'calculator');
        const complexity = getComplexityLevel(validation.securityScore);
        
        // Count expression types
        analytics.expressionTypes[user.expressionType || 'calculator'] = 
          (analytics.expressionTypes[user.expressionType || 'calculator'] || 0) + 1;
        
        // Security distribution
        analytics.securityDistribution[complexity as keyof typeof analytics.securityDistribution]++;
        
        totalSecurityScore += validation.securityScore;
        
        // Track weak expressions
        if (validation.securityScore < 60) {
          analytics.weakExpressions.push({
            userId: user._id,
            username: user.username,
            securityScore: validation.securityScore,
            issues: validation.errors.concat(validation.warnings)
          });
        }
      }
    });

    analytics.averageSecurityScore = users.length > 0 ? totalSecurityScore / users.length : 0;

    // Generate recommendations
    if (analytics.securityDistribution.weak > 0) {
      analytics.recommendations.push(`${analytics.securityDistribution.weak} users have weak expressions that should be updated`);
    }
    if (analytics.averageSecurityScore < 70) {
      analytics.recommendations.push('Overall expression security is below recommended level');
    }

    // Log admin access
    await AuditLogModel.create({
      action: 'EXPRESSION_ANALYTICS_ACCESSED',
      adminId,
      details: {
        totalUsers: analytics.totalUsers,
        averageSecurityScore: analytics.averageSecurityScore
      },
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      data: analytics
    });

  } catch (error: any) {
    console.error('Error fetching expression analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch expression analytics',
      details: error.message
    });
  }
});

// Helper function to validate expressions
function validateExpression(expression: string, expressionType: string) {
  const result = {
    isValid: true,
    securityScore: 0,
    errors: [] as string[],
    warnings: [] as string[],
    suggestions: [] as string[]
  };

  if (expressionType === 'calculator') {
    return validateCalculatorExpression(expression);
  } else if (expressionType === 'pattern') {
    return validatePatternExpression(expression);
  }

  result.errors.push('Unknown expression type');
  result.isValid = false;
  return result;
}

function validateCalculatorExpression(expression: string) {
  const result = {
    isValid: true,
    securityScore: 0,
    errors: [] as string[],
    warnings: [] as string[],
    suggestions: [] as string[]
  };

  // Basic validation
  if (expression.length < 3) {
    result.errors.push('Expression too short (minimum 3 characters)');
    result.isValid = false;
  }

  if (expression.length > 50) {
    result.errors.push('Expression too long (maximum 50 characters)');
    result.isValid = false;
  }

  // Check for valid calculator characters
  const validChars = /^[0-9+\-*/().= ]+$/;
  if (!validChars.test(expression)) {
    result.errors.push('Expression contains invalid characters');
    result.isValid = false;
  }

  // Security scoring
  let score = 30; // Base score

  // Length bonus
  if (expression.length >= 5) score += 10;
  if (expression.length >= 8) score += 10;
  if (expression.length >= 12) score += 10;

  // Complexity bonus
  const operators = (expression.match(/[+\-*/]/g) || []).length;
  if (operators >= 2) score += 15;
  if (operators >= 4) score += 15;

  // Parentheses bonus
  if (expression.includes('(') && expression.includes(')')) score += 10;

  // Deduct for common patterns
  if (/^1\+1=2$/.test(expression)) score -= 20;
  if (/^2\+2=4$/.test(expression)) score -= 20;
  if (/^\d+$/.test(expression)) score -= 15; // Just numbers

  result.securityScore = Math.max(0, Math.min(100, score));

  // Warnings and suggestions
  if (result.securityScore < 50) {
    result.warnings.push('Expression security is weak');
    result.suggestions.push('Use more complex mathematical operations');
  }

  if (operators < 2) {
    result.suggestions.push('Include multiple mathematical operators');
  }

  return result;
}

function validatePatternExpression(expression: string) {
  const result = {
    isValid: true,
    securityScore: 0,
    errors: [] as string[],
    warnings: [] as string[],
    suggestions: [] as string[]
  };

  // Basic validation
  if (expression.length < 4) {
    result.errors.push('Pattern too short (minimum 4 characters)');
    result.isValid = false;
  }

  if (expression.length > 20) {
    result.errors.push('Pattern too long (maximum 20 characters)');
    result.isValid = false;
  }

  // Security scoring
  let score = 20; // Base score

  // Length bonus
  if (expression.length >= 6) score += 15;
  if (expression.length >= 8) score += 15;
  if (expression.length >= 10) score += 10;

  // Unique characters bonus
  const uniqueChars = new Set(expression).size;
  score += uniqueChars * 3;

  // Pattern complexity
  const hasNumbers = /\d/.test(expression);
  const hasLetters = /[a-zA-Z]/.test(expression);
  const hasSymbols = /[^a-zA-Z0-9]/.test(expression);

  if (hasNumbers) score += 10;
  if (hasLetters) score += 10;
  if (hasSymbols) score += 15;

  result.securityScore = Math.max(0, Math.min(100, score));

  // Warnings and suggestions
  if (result.securityScore < 60) {
    result.warnings.push('Pattern security is weak');
    result.suggestions.push('Use a mix of numbers, letters, and symbols');
  }

  return result;
}

function generateCalculatorExpressions(complexity: string): string[] {
  const simple = [
    '3+5=8',
    '12-7=5',
    '4*6=24',
    '15/3=5'
  ];

  const medium = [
    '(7+3)*2=20',
    '25-8+12=29',
    '6*7-9=33',
    '(15+5)/4=5',
    '3*8+7=31'
  ];

  const complex = [
    '((12+8)*3)-15=45',
    '7*6-(4+9)=29',
    '(25/5)+(8*3)=29',
    '15*(3+2)-8=67',
    '((9+6)/3)*8=40'
  ];

  switch (complexity) {
    case 'simple': return simple;
    case 'complex': return complex;
    default: return medium;
  }
}

function generatePatternExpressions(complexity: string): string[] {
  const simple = [
    'abc123',
    'user01',
    'pass1',
    'key456'
  ];

  const medium = [
    'user#123',
    'key@456',
    'app$789',
    'sec*012'
  ];

  const complex = [
    'u$3r#789',
    'k3y@2024',
    'a9p#s3c!',
    'x7@y9#z2'
  ];

  switch (complexity) {
    case 'simple': return simple;
    case 'complex': return complex;
    default: return medium;
  }
}

function getComplexityLevel(score: number): string {
  if (score < 50) return 'weak';
  if (score < 75) return 'medium';
  return 'strong';
}

export default router;
