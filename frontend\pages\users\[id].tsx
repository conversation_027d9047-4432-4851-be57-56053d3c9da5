import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { apiClient } from '../../utils/axiosClient';

interface UserFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword?: string;
  expression: string;
  displayName: string;
  deviceFingerprint: string;
  bleUUID: string;
  role: string;
  isActive: boolean;
  isSuperuser: boolean;
  status: string;
}

export default function UserForm() {
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    expression: '',
    displayName: '',
    deviceFingerprint: '',
    bleUUID: '',
    role: 'user',
    isActive: true,
    isSuperuser: false,
    status: 'active',
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isEdit, setIsEdit] = useState(false);
  const router = useRouter();
  const { id } = router.query;
  useEffect(() => {
    // Check if admin is logged in
    const checkSession = async () => {
      try {
        await apiClient.frontend.get('/api/auth/admin/session');
      } catch (err) {
        router.push('/');
      }
    };

    checkSession();

    // If ID exists, fetch user data for editing
    const fetchUser = async () => {
      if (id && id !== 'new') {
        setIsEdit(true);
        setLoading(true);
        try {
          const response = await apiClient.frontend.get(`/api/users/${id}`);
          const userData = response.data;
          setFormData({
            username: userData.username || '',
            email: userData.email || '',
            password: '',
            confirmPassword: '',
            expression: userData.expression || '',
            displayName: userData.displayName || '',
            deviceFingerprint: userData.deviceFingerprint || '',
            bleUUID: userData.bleUUID || '',
            role: userData.role || 'user',
            isActive: userData.isActive !== undefined ? userData.isActive : true,
            isSuperuser: userData.isSuperuser !== undefined ? userData.isSuperuser : false,
            status: userData.status || 'active',
          });
        } catch (err: any) {
          setError(err.response?.data?.error || err.message || 'Failed to load user data');
        } finally {
          setLoading(false);
        }
      }
    };

    if (id) {
      fetchUser();
    }
  }, [id, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate form
    if (formData.username.trim() === '') {
      setError('Username is required');
      return;
    }

    if (formData.expression.trim() === '') {
      setError('Expression (unlock pattern/PIN) is required');
      return;
    }

    if (formData.displayName.trim() === '') {
      setError('Display name is required');
      return;
    }

    if (formData.deviceFingerprint.trim() === '') {
      setError('Device fingerprint is required');
      return;
    }

    if (formData.bleUUID.trim() === '') {
      setError('BLE UUID is required');
      return;
    }

    if (!isEdit && formData.password.trim() === '') {
      setError('Password is required for new users');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }    setLoading(true);
    try {
      // Prepare data for API
      const { confirmPassword, ...apiData } = {
        ...formData,
        // Only include password if it's provided (for edits)
        ...(formData.password ? { password: formData.password } : {})
      };

      if (isEdit) {
        await apiClient.frontend.put(`/api/users/${id}`, apiData);
      } else {
        await apiClient.frontend.post('/api/users', apiData);
      }

      // Navigate back to users list
      router.push('/users');
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || 'An error occurred while saving user');
    } finally {
      setLoading(false);
    }
  };
  if (loading && isEdit) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading user data...</p>
      </div>
    );
  }

  return (
    <div className="page-container">
      <Head>
        <title>{isEdit ? 'Edit User' : 'Add User'} - CCALC Admin</title>
      </Head>

      <header className="header">
        <div className="header-content">
          <h1 className="brand">CCALC Admin</h1>
          <nav className="nav">
            <a href="/dashboard" className="nav-link">Dashboard</a>
            <a href="/users" className="nav-link active">Users</a>
            <a href="/builds" className="nav-link">Builds</a>
            <a href="/controls" className="nav-link">Controls</a>
          </nav>
        </div>
      </header>

      <main className="main-content">
        <div className="page-header">
          <h2 className="page-title">{isEdit ? 'Edit User' : 'Add New User'}</h2>
          <button className="btn-secondary" onClick={() => router.push('/users')}>
            Back to Users
          </button>
        </div>

        {error && (
          <div className="alert alert-error">
            {error}
          </div>
        )}

        <div className="card">
          <form onSubmit={handleSubmit} className="form">
            <div className="form-grid">
              {/* Username */}
              <div className="form-group">
                <label htmlFor="username" className="label">Username</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  className="input"
                  required
                />
              </div>

              {/* Email */}
              <div className="form-group">
                <label htmlFor="email" className="label">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="input"
                  required
                />
              </div>

              {/* Password */}
              <div className="form-group">
                <label htmlFor="password" className="label">
                  {isEdit ? 'New Password (leave blank to keep current)' : 'Password'}
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="input"
                  {...(!isEdit && { required: true })}
                />
              </div>

              {/* Confirm Password */}
              <div className="form-group">
                <label htmlFor="confirmPassword" className="label">Confirm Password</label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="input"
                  {...(!isEdit && { required: true })}
                />
              </div>

              {/* Expression */}
              <div className="form-group">
                <label htmlFor="expression" className="label">Expression (Unlock Pattern/PIN)</label>
                <input
                  type="text"
                  id="expression"
                  name="expression"
                  value={formData.expression}
                  onChange={handleChange}
                  className="input"
                  required
                />
              </div>

              {/* Display Name */}
              <div className="form-group">
                <label htmlFor="displayName" className="label">Display Name</label>
                <input
                  type="text"
                  id="displayName"
                  name="displayName"
                  value={formData.displayName}
                  onChange={handleChange}
                  className="input"
                  required
                />
              </div>

              {/* Device Fingerprint */}
              <div className="form-group">
                <label htmlFor="deviceFingerprint" className="label">Device Fingerprint</label>
                <input
                  type="text"
                  id="deviceFingerprint"
                  name="deviceFingerprint"
                  value={formData.deviceFingerprint}
                  onChange={handleChange}
                  className="input"
                  required
                />
              </div>

              {/* BLE UUID */}
              <div className="form-group">
                <label htmlFor="bleUUID" className="label">BLE UUID</label>
                <input
                  type="text"
                  id="bleUUID"
                  name="bleUUID"
                  value={formData.bleUUID}
                  onChange={handleChange}
                  className="input"
                  required
                />
              </div>

              {/* Role */}
              <div className="form-group">
                <label htmlFor="role" className="label">Role</label>
                <select
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  className="select"
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                </select>
              </div>

              {/* Status */}
              <div className="form-group">
                <label htmlFor="status" className="label">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="select"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="locked">Locked</option>
                </select>
              </div>

              {/* Superuser */}
              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="isSuperuser"
                    checked={formData.isSuperuser}
                    onChange={handleChange}
                    className="checkbox"
                  />
                  <span>Superuser</span>
                </label>
              </div>

              {/* Active Account */}
              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleChange}
                    className="checkbox"
                  />
                  <span>Active Account</span>
                </label>
              </div>
            </div>

            <div className="form-actions">
              <button type="button" className="btn-secondary" onClick={() => router.push('/users')}>
                Cancel
              </button>
              <button type="submit" className="btn-primary" disabled={loading}>
                {loading ? 'Saving...' : 'Save User'}
              </button>
            </div>
          </form>
        </div>
      </main>      <style jsx>{`
        .page-container {
          min-height: 100vh;
          background-color: #fafafa;
        }

        .header {
          background: white;
          border-bottom: 1px solid #e5e7eb;
          position: sticky;
          top: 0;
          z-index: 10;
        }

        .header-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .brand {
          font-size: 1.5rem;
          font-weight: 600;
          color: #111827;
          margin: 0;
        }

        .nav {
          display: flex;
          gap: 2rem;
        }

        .nav-link {
          color: #6b7280;
          text-decoration: none;
          font-weight: 500;
          padding: 0.5rem 0;
          border-bottom: 2px solid transparent;
          transition: all 0.2s ease;
        }

        .nav-link:hover {
          color: #111827;
        }

        .nav-link.active {
          color: #2563eb;
          border-bottom-color: #2563eb;
        }

        .main-content {
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
        }

        .page-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .page-title {
          font-size: 1.875rem;
          font-weight: 600;
          color: #111827;
          margin: 0;
        }

        .btn-primary {
          background: #2563eb;
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .btn-primary:hover:not(:disabled) {
          background: #1d4ed8;
        }

        .btn-primary:disabled {
          background: #9ca3af;
          cursor: not-allowed;
        }

        .btn-secondary {
          background: white;
          color: #374151;
          border: 1px solid #d1d5db;
          padding: 0.75rem 1.5rem;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .btn-secondary:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }

        .alert {
          padding: 1rem;
          border-radius: 0.5rem;
          margin-bottom: 1.5rem;
        }

        .alert-error {
          background: #fef2f2;
          border: 1px solid #fecaca;
          color: #dc2626;
        }

        .card {
          background: white;
          border-radius: 0.75rem;
          border: 1px solid #e5e7eb;
          padding: 2rem;
        }

        .form {
          width: 100%;
        }

        .form-grid {
          display: grid;
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
        }

        .label {
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }

        .input, .select {
          padding: 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 1rem;
          transition: border-color 0.2s ease;
        }

        .input:focus, .select:focus {
          outline: none;
          border-color: #2563eb;
          box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .checkbox-group {
          flex-direction: row;
          align-items: center;
        }

        .checkbox-label {
          display: flex;
          align-items: center;
          cursor: pointer;
          font-weight: 500;
          color: #374151;
        }

        .checkbox {
          margin-right: 0.75rem;
          width: 1rem;
          height: 1rem;
          accent-color: #2563eb;
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 1rem;
          padding-top: 1.5rem;
          border-top: 1px solid #e5e7eb;
        }

        .loading-container {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background: #fafafa;
        }

        .loading-spinner {
          width: 2rem;
          height: 2rem;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #2563eb;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }

        .loading-container p {
          color: #6b7280;
          font-weight: 500;
        }

        @media (max-width: 768px) {
          .header-content {
            padding: 1rem;
          }

          .main-content {
            padding: 1rem;
          }

          .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .nav {
            gap: 1rem;
          }

          .form-actions {
            flex-direction: column-reverse;
          }
        }
      `}</style>
    </div>
  );
}
