import mongoose, { Document, Schema } from 'mongoose';

// Media model for encrypted file storage and management
export interface IMedia extends Document {
  mediaId: string; // Unique media identifier
  uploaderId: mongoose.Types.ObjectId;
  messageId?: mongoose.Types.ObjectId; // Associated message if any
  
  // File information
  file: {
    originalName: string;
    encryptedName: string;
    mimeType: string;
    size: number; // Original file size
    encryptedSize: number; // Encrypted file size
    extension: string;
    category: 'image' | 'video' | 'document' | 'audio' | 'other';
  };
    // Storage and encryption
  storage: {
    encryptedPath: string; // Path to encrypted file
    thumbnailPath?: string; // Path to encrypted thumbnail
    encryptionKey: string; // Per-file encryption key (encrypted)
    iv: string; // Initialization vector
    tag: string; // Authentication tag for GCM
    salt: string; // Salt for key derivation
    algorithm: string; // Encryption algorithm used
    compressionUsed: boolean;
  };
  
  // Media metadata
  metadata: {
    dimensions?: {
      width: number;
      height: number;
    };
    duration?: number; // For video/audio in seconds
    fps?: number; // For video
    bitrate?: number; // For audio/video
    codec?: string;
    quality?: 'low' | 'medium' | 'high' | 'original';
    exifData?: Record<string, any>; // EXIF data (sanitized)
  };
  
  // Access control
  access: {
    visibility: 'private' | 'chat_members' | 'superuser_only';
    allowedUsers: mongoose.Types.ObjectId[];
    downloadCount: number;
    lastAccessed?: Date;
    expiresAt?: Date; // Auto-deletion date
    maxDownloads?: number;
  };
  
  // Security and compliance
  security: {
    scanned: boolean;
    scanResults?: {
      malwareDetected: boolean;
      threatLevel: 'none' | 'low' | 'medium' | 'high';
      scanEngine: string;
      scanDate: Date;
      findings?: string[];
    };
    contentModeration?: {
      status: 'pending' | 'approved' | 'rejected' | 'flagged';
      flags?: string[];
      reviewedAt?: Date;
      reviewedBy?: mongoose.Types.ObjectId;
    };
    uploadSource: {
      deviceFingerprint: string;
      bleUUID: string;
      ipAddress: string;
      userAgent: string;
    };
  };
  
  // Processing status
  processing: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    thumbnailGenerated: boolean;
    compressionCompleted: boolean;
    encryptionCompleted: boolean;
    processedAt?: Date;
    errorDetails?: string;
  };
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const MediaSchema = new Schema<IMedia>(
  {
    mediaId: { type: String, required: true, unique: true },
    uploaderId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    messageId: { type: Schema.Types.ObjectId, ref: 'Message' },
    
    file: {
      originalName: { type: String, required: true },
      encryptedName: { type: String, required: true },
      mimeType: { type: String, required: true },
      size: { type: Number, required: true },
      encryptedSize: { type: Number, required: true },
      extension: { type: String, required: true },
      category: {
        type: String,
        enum: ['image', 'video', 'document', 'audio', 'other'],
        required: true,
      },
    },
      storage: {
      encryptedPath: { type: String, required: true },
      thumbnailPath: String,
      encryptionKey: { type: String, required: true },
      iv: { type: String, required: true },
      tag: { type: String, required: true },
      salt: { type: String, required: true },
      algorithm: { type: String, default: 'aes-256-gcm' },
      compressionUsed: { type: Boolean, default: false },
    },
    
    metadata: {
      dimensions: {
        width: Number,
        height: Number,
      },
      duration: Number,
      fps: Number,
      bitrate: Number,
      codec: String,
      quality: {
        type: String,
        enum: ['low', 'medium', 'high', 'original'],
        default: 'original',
      },
      exifData: Schema.Types.Mixed,
    },
    
    access: {
      visibility: {
        type: String,
        enum: ['private', 'chat_members', 'superuser_only'],
        default: 'private',
      },
      allowedUsers: [{ type: Schema.Types.ObjectId, ref: 'User' }],
      downloadCount: { type: Number, default: 0 },
      lastAccessed: Date,
      expiresAt: Date,
      maxDownloads: Number,
    },
    
    security: {
      scanned: { type: Boolean, default: false },
      scanResults: {
        malwareDetected: { type: Boolean, default: false },
        threatLevel: {
          type: String,
          enum: ['none', 'low', 'medium', 'high'],
          default: 'none',
        },
        scanEngine: String,
        scanDate: Date,
        findings: [String],
      },
      contentModeration: {
        status: {
          type: String,
          enum: ['pending', 'approved', 'rejected', 'flagged'],
          default: 'pending',
        },
        flags: [String],
        reviewedAt: Date,
        reviewedBy: { type: Schema.Types.ObjectId, ref: 'Admin' },
      },
      uploadSource: {
        deviceFingerprint: { type: String, required: true },
        bleUUID: { type: String, required: true },
        ipAddress: { type: String, required: true },
        userAgent: { type: String, required: true },
      },
    },
    
    processing: {
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'failed'],
        default: 'pending',
      },
      thumbnailGenerated: { type: Boolean, default: false },
      compressionCompleted: { type: Boolean, default: false },
      encryptionCompleted: { type: Boolean, default: false },
      processedAt: Date,
      errorDetails: String,
    },
    
    isActive: { type: Boolean, default: true },
  },
  { timestamps: true }
);

// Indexes for performance
MediaSchema.index({ uploaderId: 1, createdAt: -1 });
MediaSchema.index({ mediaId: 1 });
MediaSchema.index({ messageId: 1 });
MediaSchema.index({ 'file.category': 1 });
MediaSchema.index({ 'access.expiresAt': 1 }, { expireAfterSeconds: 0 });
MediaSchema.index({ 'processing.status': 1 });
MediaSchema.index({ 'security.scanned': 1 });

export default mongoose.model<IMedia>('Media', MediaSchema);
