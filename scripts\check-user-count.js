/**
 * <PERSON><PERSON>t to check the number of users in the database
 */

const mongoose = require('mongoose');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// User schema definition (minimal for this script)
const userSchema = new mongoose.Schema({
  username: String,
  email: String,
  status: String,
  createdAt: Date,
  deviceFingerprintHash: String,
  bleUUIDHash: String,
  // Add expression fields matching the actual model
  unlockExpression: String,
  expressionHash: String,
  profile: {
    displayName: String
  }
});

const User = mongoose.model('User', userSchema);

async function checkUserCount() {
  try {
    // Use localhost since we're connecting from host machine to Docker container
    const mongoUri = '*************************************************************';
    
    console.log('Connecting to MongoDB...');
    console.log('URI:', mongoUri.replace(/\/\/.*@/, '//***:***@')); // Hide credentials in log
    
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000
    });

    console.log('Connected to MongoDB successfully');

    // Count total users
    const totalUsers = await User.countDocuments();
    console.log(`Total users in database: ${totalUsers}`);

    // Count users by status
    const statusCounts = await User.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    console.log('\nUsers by status:');
    statusCounts.forEach(status => {
      console.log(`  ${status._id || 'undefined'}: ${status.count}`);
    });

    // Count users with/without device registration
    const withDevice = await User.countDocuments({ 
      deviceFingerprintHash: { $exists: true, $ne: null } 
    });
    const withoutDevice = totalUsers - withDevice;

    console.log('\nDevice registration status:');
    console.log(`  With device fingerprint: ${withDevice}`);
    console.log(`  Without device fingerprint: ${withoutDevice}`);

    // Show recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('username email status createdAt deviceFingerprintHash bleUUIDHash unlockExpression profile');

    console.log('\nRecent users (last 5):');
    recentUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.username} (${user.email})`);
      console.log(`     Status: ${user.status || 'undefined'}`);
      console.log(`     Created: ${user.createdAt}`);
      console.log(`     Display Name: ${user.profile?.displayName || 'none'}`);
      console.log(`     Expression: ${user.unlockExpression || 'none'}`);
      console.log(`     Device fingerprint: ${user.deviceFingerprintHash ? user.deviceFingerprintHash.substring(0, 16) + '...' : 'none'}`);
      console.log(`     Has BLE UUID: ${!!user.bleUUIDHash}`);
      console.log('');
    });

  } catch (error) {
    console.error('Error:', error.message);
    
    if (error.name === 'MongoServerSelectionError') {
      console.log('\nTroubleshooting tips:');
      console.log('1. Make sure MongoDB is running (try: docker-compose up mongo)');
      console.log('2. Check if the connection string is correct');
      console.log('3. Verify network connectivity to MongoDB');
    }
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

checkUserCount();
